package com.eatapp.clementine.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.eatapp.clementine.R
import com.eatapp.clementine.databinding.ListItemChitConfigBinding
import com.eatapp.clementine.internal.ChitPrintConfigItem


class ChitPrintConfigAdapter(val listener: (ChitPrintConfigItem) -> Unit) :
    ListAdapter<ChitPrintConfigItem, ChitPrintConfigAdapter.ItemViewHolder>(ChitPrintConfigItemDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ItemViewHolder {

        return ItemViewHolder(
            ListItemChitConfigBinding.inflate(
                LayoutInflater.from(parent.context), parent, false
            )
        )
    }

    override fun onBindViewHolder(holder: ItemViewHolder, position: Int) {
        val item = getItem(position)
        holder.bind(createOnClickListener(item), item)
    }

    private fun createOnClickListener(
        item: ChitPrintConfigItem
    ): View.OnClickListener {
        return View.OnClickListener {
            listener(item)
        }
    }

    class ItemViewHolder(
        private val binding: ListItemChitConfigBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(l: View.OnClickListener, i: ChitPrintConfigItem) {
            binding.apply {
                clickListener = l
                item = i
                executePendingBindings()
            }

            binding.iconsCont.removeAllViews()

            i.icons.forEach {
                val imageView = ImageView(itemView.context)
                imageView.setImageResource(it)
                imageView.setColorFilter(ContextCompat.getColor(itemView.context, R.color.colorDark50),
                    android.graphics.PorterDuff.Mode.MULTIPLY);
                binding.iconsCont.addView(imageView)
            }
        }
    }
}

private class ChitPrintConfigItemDiffCallback : DiffUtil.ItemCallback<ChitPrintConfigItem>() {

    override fun areItemsTheSame(oldItem: ChitPrintConfigItem, newItem: ChitPrintConfigItem): Boolean {
        return oldItem.key == newItem.key
    }

    override fun areContentsTheSame(oldItem: ChitPrintConfigItem, newItem: ChitPrintConfigItem): Boolean {
        return oldItem == newItem
    }
}