package com.eatapp.clementine.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.eatapp.clementine.R
import com.eatapp.clementine.data.network.response.comment.CommentModel
import com.eatapp.clementine.databinding.ListItemCommentBinding
import com.eatapp.clementine.internal.tintColor


class CommentsAdapter(val listener: (Int) -> Unit) :
    ListAdapter<CommentModel, CommentsAdapter.ItemViewHolder>(CommentsItemDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ItemViewHolder {

        return ItemViewHolder(
            ListItemCommentBinding.inflate(
                LayoutInflater.from(parent.context), parent, false
            )
        )
    }

    override fun onBindViewHolder(holder: ItemViewHolder, position: Int) {
        val item = getItem(position)
        holder.bind(createOnClickListener(item, holder, position), item)
    }

    private fun createOnClickListener(
        comment: CommentModel,
        holder: ItemViewHolder,
        position: Int
    ): View.OnClickListener {
        return View.OnClickListener {
            if (comment.isInEditMode) {
                comment.comment = holder.itemView.findViewById<TextView>(R.id.com).text.toString()
            }

            comment.isInEditMode = !comment.isInEditMode
            listener(position)
        }
    }

    class ItemViewHolder(
        private val binding: ListItemCommentBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(l: View.OnClickListener, i: CommentModel) {
            binding.apply {
                editClickListener = l
                comment = i
                executePendingBindings()
            }

            if (i.isInEditMode) {
                binding.com.requestFocus()
                binding.editIcon.tintColor(R.color.colorPrimary)
            } else {
                binding.editIcon.tintColor(R.color.colorDark50)
            }
        }
    }
}

private class CommentsItemDiffCallback : DiffUtil.ItemCallback<CommentModel>() {

    override fun areItemsTheSame(oldItem: CommentModel, newItem: CommentModel): Boolean {
        return oldItem.comment == newItem.comment
    }

    override fun areContentsTheSame(oldItem: CommentModel, newItem: CommentModel): Boolean {
        return oldItem == newItem
    }
}