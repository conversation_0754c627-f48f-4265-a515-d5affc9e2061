package com.eatapp.clementine.adapter

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.eatapp.clementine.R

class EatPagerAdapter(fragmentActivity: FragmentActivity) :
    FragmentStateAdapter(fragmentActivity) {

    val fragmentList = mutableListOf<Fragment>()
    private val titleList = mutableListOf<String>()
    private val iconList = mutableListOf<Int>()

    fun addFragment(fragment: Fragment, title: String, iconRes: Int = R.drawable.ic_icon_main) {
        fragmentList.add(fragment)
        titleList.add(title)
        iconList.add(iconRes)
    }

    fun getItem(position: Int): Fragment {
        return fragmentList.getOrNull(position) ?: Fragment()
    }

    override fun getItemCount(): Int = fragmentList.size

    override fun createFragment(position: Int): Fragment {
        // For positions 0 and 1 (Details and Profile), return the same fragment instance
        // This ensures they share the same scrollable content
        return if (position == 0 || position == 1) {
            fragmentList[0] // Always return the first fragment (ReservationDetailsFragment)
        } else {
            fragmentList[position]
        }
    }

    fun getPageTitle(position: Int): String = titleList[position]

    fun getPageIcon(position: Int): Int = iconList.getOrElse(position) { R.drawable.ic_icon_main }
}