package com.eatapp.clementine.adapter

import android.graphics.Typeface
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.text.toSpannable
import androidx.paging.PagingData
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.eatapp.clementine.R
import com.eatapp.clementine.data.network.response.guest.Guest
import com.eatapp.clementine.databinding.ListHeaderItemBinding
import com.eatapp.clementine.databinding.ListItemGuestBinding
import com.eatapp.clementine.internal.visibleOrGone


class GuestAdapter(
    val listener: (Guest) -> Unit
) : PagingDataAdapter<Guest, RecyclerView.ViewHolder>(GuestDiffCallback()) {

    private var firsNameQuery = ""
    private var lastNameQuery = ""
    private var phoneQuery = ""

    enum class ItemType {
        GUEST,
        HEADER
    }

    companion object {
        val HEADER = ItemType.HEADER.ordinal
        val GUEST = ItemType.GUEST.ordinal
    }
//
//    override fun isStickyHeader(position: Int): Boolean {
//        if (position < 0) {
//            return false
//        }
//        return getItem(position)?.isHeader == true
//    }

    override fun getItemViewType(position: Int): Int {
        return if (getItem(position)?.isHeader == true) {
            HEADER
        } else {
            GUEST
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {

        return if (viewType == HEADER) {
            HeaderViewHolder(
                ListHeaderItemBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
            )
        } else {
            GuestViewHolder(
                ListItemGuestBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
            )
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {

        if (position < 0) return

        val guest = getItem(position)

        when (guest?.isHeader) {
            true -> (holder as HeaderViewHolder).bind(guest)
            false -> (holder as GuestViewHolder).bind(guest)
            null -> {}
        }

        holder.itemView.tag = guest
    }

    suspend fun submitList(
        guests: List<Guest>,
        firsNameQuery: String,
        lastNameQuery: String,
        phoneQuery: String
    ) {
        this.firsNameQuery = firsNameQuery
        this.lastNameQuery = lastNameQuery
        this.phoneQuery = phoneQuery

        submitData(PagingData.from(guests))
    }

    inner class GuestViewHolder(
        private val binding: ListItemGuestBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(item: Guest) {
            binding.apply {
                guest = item
                executePendingBindings()

                tvExample.visibleOrGone = item.attributes?.demo == true

                tvGuestName.text = highlightQuery(
                    String.format("%s %s", item.firstName, item.lastName).toSpannable(),
                    <EMAIL>()
                )
                tvGuestName.text = highlightQuery(
                    binding.tvGuestName.text.toSpannable(),
                    <EMAIL>()
                )
                tvPhone.text = highlightQuery(
                    (item.phone ?: "").toSpannable(),
                    <EMAIL>()
                )
            }

            binding.root.setOnClickListener {
                listener.invoke(item)
            }
        }

        private fun highlightQuery(result: Spannable, query: String): SpannableString {
            val spannable = SpannableString(result)

            if (query.isEmpty()) {
                return spannable
            }

            val startIndex = result.indexOf(query, ignoreCase = true)

            if (startIndex != -1) {
                spannable.setSpan(
                    StyleSpan(Typeface.BOLD),
                    startIndex,
                    startIndex + query.length,
                    Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                )

                val color = ContextCompat.getColor(binding.root.context, R.color.grey800)
                spannable.setSpan(
                    ForegroundColorSpan(color),
                    startIndex,
                    startIndex + query.length,
                    Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }

            return spannable
        }
    }

    class HeaderViewHolder(
        val binding: ListHeaderItemBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        private val icExpand = binding.expandIcon
        private val heIcon = binding.headerIcon

        fun bind(item: Guest) {
            binding.apply {
                title = item.header
                showSeparator = true
                icExpand.visibility = View.GONE
                heIcon.visibility = View.GONE
                itemView.setPadding(
                    itemView.resources.getDimension(R.dimen.toolbar_icon_padding).toInt(), 0, 0, 0
                )
                executePendingBindings()
            }
        }
    }
}

private class GuestDiffCallback : DiffUtil.ItemCallback<Guest>() {

    override fun areItemsTheSame(oldItem: Guest, newItem: Guest): Boolean {
        return oldItem.id == newItem.id
    }

    override fun areContentsTheSame(oldItem: Guest, newItem: Guest): Boolean {
        return oldItem == newItem
    }
}