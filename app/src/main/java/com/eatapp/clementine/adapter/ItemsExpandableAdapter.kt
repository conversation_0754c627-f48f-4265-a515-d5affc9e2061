package com.eatapp.clementine.adapter

import android.annotation.SuppressLint
import android.graphics.Color
import android.graphics.PorterDuff
import android.os.Handler
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.eatapp.clementine.databinding.ListHeaderItemBinding
import com.eatapp.clementine.databinding.ListItemSelectorBinding
import com.eatapp.clementine.internal.ItemsGroup
import com.eatapp.clementine.internal.SelectorItem
import kotlin.properties.Delegates
import kotlin.reflect.KProperty

class ItemsExpandableAdapter(
    private val itemsGroup: ItemsGroup,
    private val singleSelection: Boolean
) :
    RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    var dismiss: (() -> Unit)? = null

    companion object {
        private const val VIEW_TYPE_ITEM = 1
        private const val VIEW_TYPE_HEADER = 2

        private const val IC_EXPANDED_ROTATION_DEG = 0F
        private const val IC_COLLAPSED_ROTATION_DEG = 180F
    }

    private var isExpanded: Boolean by Delegates.observable(true) { _: KProperty<*>, _: Boolean, newExpandedValue: Boolean ->
        if (newExpandedValue) {
            notifyItemRangeInserted(1, itemsGroup.items.size)
            //To update the header expand icon
            notifyItemChanged(0)
        } else {
            notifyItemRangeRemoved(1, itemsGroup.items.size)
            //To update the header expand icon
            notifyItemChanged(0)
        }
    }

    private val onHeaderClickListener = View.OnClickListener {
        isExpanded = !isExpanded
    }

    private fun createOnClickListener(tag: SelectorItem, position: Int): View.OnClickListener {
        return View.OnClickListener {

            if (tag.isDisabled) return@OnClickListener

            if (singleSelection) {
                for (i in 0 until itemCount) {
                    if (itemsGroup.items[position - 1].isSelected) {
                        itemsGroup.items[position - 1].isSelected = false
                        notifyItemChanged(i)
                    }
                }
            }

            tag.isSelected = !tag.isSelected
            notifyItemChanged(position)

            if (singleSelection) Handler().postDelayed({ dismiss?.let { it() } }, 500)
        }
    }

    private fun createOnCheckedListener(
        itemsGroup: ItemsGroup,
        position: Int
    ): View.OnClickListener {
        return View.OnClickListener {
            itemsGroup.items.forEach {
                it.isSelected = itemsGroup.checked
            }
            notifyItemRangeChanged(position + 1, itemsGroup.items.count())
        }
    }

    override fun getItemViewType(position: Int): Int {
        return if (position == 0) VIEW_TYPE_HEADER else VIEW_TYPE_ITEM
    }

    override fun getItemCount(): Int = if (isExpanded) itemsGroup.items.size + 1 else 1


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {

        return if (viewType == VIEW_TYPE_HEADER) {
            HeaderViewHolder(
                ListHeaderItemBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
            )
        } else {
            ItemViewHolder(
                ListItemSelectorBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
            )
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {

        when (holder) {
            is ItemViewHolder -> holder.bind(
                createOnClickListener(
                    itemsGroup.items[position - 1],
                    position
                ), false, itemsGroup.items[position - 1]
            )
            is HeaderViewHolder -> {
                holder.bind(
                    itemsGroup,
                    isExpanded,
                    onHeaderClickListener,
                    createOnCheckedListener(itemsGroup, position)
                )
            }
        }
    }

    class ItemViewHolder(
        private val binding: ListItemSelectorBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(l: View.OnClickListener, s: Boolean, i: SelectorItem?) {
            binding.apply {
                clickListener = l
                radio = s
                item = i
                executePendingBindings()
            }
        }
    }

    class HeaderViewHolder(
        val binding: ListHeaderItemBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        private val icExpand = binding.expandIcon
        private val heIcon = binding.headerIcon

        fun bind(g: ItemsGroup, e: Boolean, l: View.OnClickListener, c: View.OnClickListener) {
            binding.apply {
                item = g
                title = g.title
                showSeparator = false
                clickListener = l
                checkedListener = c
                icExpand.rotation = if (e) IC_EXPANDED_ROTATION_DEG else IC_COLLAPSED_ROTATION_DEG
                heIcon.setColorFilter(Color.parseColor(g.color), PorterDuff.Mode.SRC_ATOP)
                bcg.setBackgroundColor(Color.parseColor(g.color))
                executePendingBindings()
            }
        }
    }
}


