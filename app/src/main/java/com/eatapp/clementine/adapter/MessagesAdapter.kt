package com.eatapp.clementine.adapter

import android.annotation.SuppressLint
import android.content.res.ColorStateList
import android.graphics.Color
import android.graphics.Typeface
import android.text.Spannable
import android.text.SpannableString
import android.text.TextPaint
import android.text.format.DateUtils
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.text.style.TypefaceSpan
import android.util.Log
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.WebChromeClient
import android.webkit.WebViewClient
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.eatapp.clementine.R
import com.eatapp.clementine.data.network.response.message.ChannelType
import com.eatapp.clementine.data.network.response.message.Message
import com.eatapp.clementine.data.network.response.message.MessageStatus
import com.eatapp.clementine.databinding.ListDateHeaderItemBinding
import com.eatapp.clementine.databinding.ListHeaderItemBinding
import com.eatapp.clementine.databinding.ListItemMessageBinding
import com.eatapp.clementine.internal.dp
import com.eatapp.clementine.internal.isYesterday
import com.eatapp.clementine.internal.px
import com.eatapp.clementine.internal.width
import com.eatapp.clementine.ui.common.hubspot.CustomWebChromeClient
import com.jay.widget.StickyHeaders
import java.text.SimpleDateFormat
import java.util.*

class MessagesAdapter: ListAdapter<MessageItem, RecyclerView.ViewHolder>(MessageDiffCallback()), StickyHeaders {

    var failedMessageListener: ((View, Message) -> Unit)? = null

    enum class ItemType {
        MESSAGE,
        HEADER
    }

    companion object {
        val HEADER = ItemType.HEADER.ordinal
        val MESSAGE = ItemType.MESSAGE.ordinal
    }

    override fun isStickyHeader(position: Int): Boolean {
        if (position < 0 || position > currentList.size) {
            return false
        }
        return getItem(position).isHeader
    }

    override fun getItemViewType(position: Int): Int {
        return if (getItem(position).isHeader) {
            HEADER
        } else {
            MESSAGE
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return if (viewType == HEADER) {
            HeaderViewHolder(
                ListDateHeaderItemBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
            )
        } else {
            MessageViewHolder(
                ListItemMessageBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                ),
                failedMessageListener
            )
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (position < 0) return

        val item = getItem(position)

        when (item.isHeader) {
            true -> (holder as HeaderViewHolder).bind(item)
            false -> (holder as MessageViewHolder).bind(item.message!!, getPreviousMessage(position))
        }
    }

    private fun getPreviousMessage(position: Int): Message? {
        var prevPos = position - 1
        while (prevPos >= 0) {
            val item = getItem(prevPos)
            if (!item.isHeader) {
                return item.message
            }
            prevPos--
        }
        return null
    }

    fun submitMessages(messages: List<Message>) {
        val groupedMessages = groupMessagesByDate(messages)
        submitList(groupedMessages)
    }

    private fun groupMessagesByDate(messages: List<Message>): List<MessageItem> {
        val groupedItems = mutableListOf<MessageItem>()
        val dateFormat = SimpleDateFormat("EEE, MMM dd", Locale.getDefault())
        var currentDate: String? = null

        messages.forEach { message ->
            val messageTime = message.createdAt ?: Date()

            val headerText = when {
                DateUtils.isToday(messageTime.time) -> { "Today" }
                messageTime.isYesterday() -> { "Yesterday" }
                else -> { dateFormat.format(messageTime) }
            }
            
            if (headerText != currentDate) {
                currentDate = headerText
                groupedItems.add(MessageItem(header = headerText))
            }
            
            groupedItems.add(MessageItem(message = message))
        }

        return groupedItems
    }

    class MessageViewHolder(
        private val binding: ListItemMessageBinding,
        private val failedMessageListener: ((View, Message) -> Unit)?
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(message: Message, previousMessage: Message?) {
            binding.apply {
                this.message = message
                executePendingBindings()
            }

            // Hide channel icon if previous message has same channel type
             if (previousMessage?.channel == message.channel
                && previousMessage.inbound == message.inbound) {
                 binding.channelIconCont.visibility = View.INVISIBLE
                 binding.arrowView.visibility =  View.INVISIBLE
                 binding.mainCont.setPadding(binding.mainCont.paddingLeft, 8.px,
                     binding.mainCont.paddingRight, binding.mainCont.paddingBottom)
            } else {
                binding.mainCont.setPadding(binding.mainCont.paddingLeft, 24.px,
                    binding.mainCont.paddingRight, binding.mainCont.paddingBottom)
                 binding.channelIconCont.visibility = View.VISIBLE
                 binding.arrowView.visibility = View.VISIBLE
            }

            binding.bodyLarge.typeface = if (message.messageTemplateName == null) {
                binding.root.context.resources.getFont(R.font.inter_medium)
            } else {
                binding.root.context.resources.getFont(R.font.inter_regular)
            }

            binding.bodyLarge.setTextSize(
                TypedValue.COMPLEX_UNIT_SP,
                if (message.messageTemplateName == null) 14f else 13f
            )

            when (message.channel) {
                ChannelType.SMS -> binding.channelIcon.setImageResource(R.drawable.ic_icon_sms)
                ChannelType.EMAIL -> binding.channelIcon.setImageResource(R.drawable.ic_icon_email)
                ChannelType.WHATSAPP -> binding.channelIcon.setImageResource(R.drawable.ic_icon_whatsapp)
            }

            binding.messageContainerSmall.visibility = View.GONE
            binding.messageContainerLarge.visibility = View.VISIBLE

            binding.dateSmall.text = messageStatusText
            binding.dateLarge.text = messageStatusText

            if (message.channel == ChannelType.SMS || message.channel == ChannelType.WHATSAPP || !isHTMLContent(message.content)) {
                updateBody(message.messageTemplateId != null, message.content)
            } else {
                updateEmailPreview(message.content)
            }

            binding.checkSmall.visibility = View.VISIBLE
            binding.checkLarge.visibility = View.VISIBLE

            if (message.channel == ChannelType.WHATSAPP && !message.inbound) {
                when (message.status) {
                    MessageStatus.SENT -> {
                        binding.checkSmall.setImageResource(R.drawable.ic_message_one_check)
                        binding.checkLarge.setImageResource(R.drawable.ic_message_one_check)
                        binding.checkSmall.setColorFilter(ContextCompat.getColor(binding.root.context, R.color.grey700))
                        binding.checkLarge.setColorFilter(ContextCompat.getColor(binding.root.context, R.color.grey700))
                    }
                    MessageStatus.DELIVERED -> {
                        binding.checkSmall.setImageResource(R.drawable.ic_message_two_check)
                        binding.checkLarge.setImageResource(R.drawable.ic_message_two_check)
                        binding.checkSmall.setColorFilter(ContextCompat.getColor(binding.root.context, R.color.grey700))
                        binding.checkLarge.setColorFilter(ContextCompat.getColor(binding.root.context, R.color.grey700))
                    }
                    MessageStatus.READ -> {
                        binding.checkSmall.setImageResource(R.drawable.ic_message_two_check)
                        binding.checkLarge.setImageResource(R.drawable.ic_message_two_check)
                        binding.checkSmall.setColorFilter(ContextCompat.getColor(binding.root.context, R.color.blue600))
                        binding.checkLarge.setColorFilter(ContextCompat.getColor(binding.root.context, R.color.blue600))
                    }
                    else -> {
                        binding.checkSmall.visibility = View.GONE
                        binding.checkLarge.visibility = View.GONE
                    }
                }
            } else {
                binding.checkSmall.visibility = View.GONE
                binding.checkLarge.visibility = View.GONE
            }

            binding.channelIconCont.backgroundTintList = ColorStateList.valueOf(backgroundColor)
            binding.messageContainerLarge.backgroundTintList = ColorStateList.valueOf(backgroundColor)
            binding.messageContainerSmall.backgroundTintList = ColorStateList.valueOf(backgroundColor)
            binding.arrowView.backgroundTintList = ColorStateList.valueOf(backgroundColor)

            binding.failSmall.visibility = if (message.isErrored) View.VISIBLE else View.GONE
            binding.failLarge.visibility = if (message.isErrored) View.VISIBLE else View.GONE

            // Add click listeners for fail icons
            if (message.isErrored) {
                binding.failSmall.setOnClickListener {
                    failedMessageListener?.invoke(it, message)
                }
                binding.failLarge.setOnClickListener {
                    failedMessageListener?.invoke(it, message)
                }
            }

            binding.previewButton.setOnClickListener {
                message.previewExpanded = !message.previewExpanded
                binding.emailPreview.visibility =
                    if (message.previewExpanded) View.VISIBLE else View.GONE
                binding.root.requestLayout()
                binding.root.invalidate()
            }
        }

        private fun updateBody(isTemplate: Boolean, body: String?) {
            binding.emailPreview.visibility = View.GONE
            binding.previewButton.visibility = View.GONE
            binding.bodyLarge.visibility = View.VISIBLE

            val density = binding.bodySmall.context.resources.displayMetrics.density
            val typeface = ResourcesCompat.getFont(binding.bodySmall.context, R.font.inter_medium) ?: Typeface.DEFAULT

            val smallTextWidth = body?.width(14f, typeface, density) ?: 0f 

            val screenWidth = binding.bodySmall.context.resources.displayMetrics.widthPixels
            val dateSmallWidth = binding.dateSmall.text.toString().width(10f, typeface, density)
            val failIconWidth = (if (binding.message?.isErrored == true) 20.px else 0)
            val checkIconWidth = (if (!binding.message?.inbound!! && binding.message?.channel == ChannelType.WHATSAPP && !binding.message?.isErrored!!) 20.px else 0)

            // Calculate available width for single line
            val availableWidth: Float = screenWidth - dateSmallWidth - failIconWidth - checkIconWidth -
                    48.px - // channel icon
                    2 * 16.px - // margin
                    2 * 12.px // padding

            // Check if it's a multiline text
            val isMultiline = body?.contains("\n") == true

            // Check if text fits in single line
            val fits = !isTemplate && !isMultiline && smallTextWidth <= availableWidth

            // Show appropriate layout based on line count
            binding.messageContainerSmall.visibility = if (fits) View.VISIBLE else View.GONE
            binding.messageContainerLarge.visibility = if (fits) View.GONE else View.VISIBLE

            // Set text in visible TextView
            if (fits) {
                binding.bodySmall.text = body
            } else {
                binding.bodyLarge.text = body
            }
        }

        @SuppressLint("SetJavaScriptEnabled")
        private fun updateEmailPreview(body: String) {
            binding.bodyLarge.visibility = View.GONE
            binding.previewButton.visibility = View.VISIBLE
            binding.emailPreview.visibility = View.GONE
            binding.emailPreview.settings.javaScriptEnabled = true
            binding.emailPreview.webChromeClient = WebChromeClient()

            val scaledHtml = body.replace(
                "<body", "<body style=\"zoom:70%; margin:0px;\""
            )

            binding.emailPreview.loadDataWithBaseURL(
                "https://www.google.com/",
                scaledHtml,
                "text/html",
                "UTF-8", 
                "index.html"
            )
        }

        private val backgroundColor: Int
            get() = when {
                binding.message?.isErrored == true -> ContextCompat.getColor(binding.root.context, R.color.red50)
                binding.message?.inbound == true -> ContextCompat.getColor(binding.root.context, R.color.grey100)
                else -> ContextCompat.getColor(binding.root.context, R.color.green50)
            }

        private val messageStatusText: SpannableString
            get() {
                val date = binding.message?.createdAt?.let {
                    SimpleDateFormat("hh:mm a", Locale.US).format(it)
                } ?: ""

                val spannableString: SpannableString

                if (binding.message?.isErrored == true) {
                    spannableString = SpannableString("$errorText. $date")
                    spannableString.setSpan(
                        ForegroundColorSpan(ContextCompat.getColor(binding.mainCont.context, R.color.red500)),
                        0,
                        spannableString.length,
                        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                    spannableString.setSpan(
                        StyleSpan(Typeface.BOLD),
                        0,
                        spannableString.length - date.length - 1,
                        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                } else {
                    spannableString = SpannableString(date)
                    spannableString.setSpan(
                        ForegroundColorSpan(ContextCompat.getColor(binding.mainCont.context, R.color.grey600)),
                        0,
                        spannableString.length,
                        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                }

                return spannableString
            }

        private val errorText: String
            get() =
                binding.message?.errorDetails?.displayPhrase ?: run {
                    when (binding.message?.status) {
                        MessageStatus.FAILED, MessageStatus.ERRORED -> "Message failed to be sent"
                        MessageStatus.UNDELIVERED -> "Message is not delivered"
                        MessageStatus.BLOCKED -> "Message blocked"
                        else -> "Message failed to be sent"
                    }
                }

        private fun isHTMLContent(string: String?): Boolean {
            val htmlTagPattern = "<[^>]+>".toRegex()
            return string?.contains(htmlTagPattern) == true
        }
    }

    class HeaderViewHolder(
        val binding: ListDateHeaderItemBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(item: MessageItem) {
            binding.apply {
                title = item.header
                executePendingBindings()
            }
        }
    }

    private class MessageDiffCallback : DiffUtil.ItemCallback<MessageItem>() {
        override fun areItemsTheSame(oldItem: MessageItem, newItem: MessageItem): Boolean {
            return if (oldItem.isHeader && newItem.isHeader) {
                oldItem.header == newItem.header
            } else if (!oldItem.isHeader && !newItem.isHeader) {
                oldItem.message?.id == newItem.message?.id
            } else {
                false
            }
        }

        override fun areContentsTheSame(oldItem: MessageItem, newItem: MessageItem): Boolean {
            return oldItem == newItem
        }
    }
}

data class MessageItem(
    val message: Message? = null,
    val header: String? = null
) {
    val isHeader: Boolean
        get() = header != null
}