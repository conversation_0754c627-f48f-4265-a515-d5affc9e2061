package com.eatapp.clementine.adapter

import android.graphics.PorterDuff
import android.graphics.PorterDuffColorFilter
import android.graphics.drawable.Drawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.eatapp.clementine.R
import com.eatapp.clementine.data.network.response.notification.Notification
import com.eatapp.clementine.data.network.response.payment.PaymentStatus
import com.eatapp.clementine.databinding.ListItemNotificationDateBinding
import com.eatapp.clementine.databinding.ListItemNotificationBinding
import com.eatapp.clementine.internal.BookingSource
import com.eatapp.clementine.internal.Status
import com.eatapp.clementine.internal.TextDrawable
import com.eatapp.clementine.internal.visible
import com.eatapp.clementine.internal.visibleOrGone
import androidx.core.graphics.toColorInt
import com.eatapp.clementine.internal.managers.waitListTimer
import com.eatapp.clementine.views.LoadingButton
import okhttp3.internal.wait

typealias NotificationClickListener = (Notification) -> Unit

class NotificationsAdapter(
    private val paymentsActive: Boolean,
    private val statusListener: (Notification, Status.Value) -> Unit,
    private val onNotificationClickListener: NotificationClickListener
    ) : ListAdapter<NotificationAdapterItem, RecyclerView.ViewHolder>(RequestedReservationDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_TYPE_DATE -> {
                val binding = ListItemNotificationDateBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                DateViewHolder(binding)
            }
            VIEW_TYPE_RESERVATION -> {
                val binding = ListItemNotificationBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                ReservationViewHolder(binding)
            }
            else -> throw IllegalArgumentException("Unknown view type")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is DateViewHolder -> holder.bind(getItem(position) as DateItem)
            is ReservationViewHolder -> holder.bind(getItem(position) as NotificationItem,
                paymentsActive, onNotificationClickListener)
        }
    }

    override fun getItemViewType(position: Int): Int {
        return when (getItem(position)) {
            is DateItem -> VIEW_TYPE_DATE
            is NotificationItem -> VIEW_TYPE_RESERVATION
        }
    }

    class DateViewHolder(
        private val binding: ListItemNotificationDateBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: DateItem) {
            binding.dateText.text = item.date
        }
    }

    inner class ReservationViewHolder(
        private val binding: ListItemNotificationBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(
            item: NotificationItem,
            paymentsActive: Boolean,
            listener: NotificationClickListener
        ) {
            val showSource = item.notification.reservation?.source?.isBlank() == false
                    && item.notification.reservation?.source != (binding.root.context.resources.getString(
                R.string.in_house_source))
                    && item.notification.reservation?.source != Status.Value.WAITLIST.code

            val showActionsRow = item.notification.reservation?.status == Status.Value.CONFIRMED.code
                    || item.notification.reservation?.status == Status.Value.NOT_CONFIRMED.code

            binding.apply {
                notification = item.notification
                this.showSource = showSource
                this.paymentsActive = paymentsActive
                this.showActionsRow = showActionsRow
                executePendingBindings()
            }

            // payment
            item.notification.reservation?.paymentStatus?.let {
                binding.paymentsIcon.visible = true
                binding.paymentsIcon.setImageResource(PaymentStatus.icon(it))
            } ?: run {
                binding.paymentsIcon.visibleOrGone = false
            }

             when (item.notification.reservation?.status == Status.Value.CONFIRMED.code) {
                true -> binding.confirmBtn.visibility = View.GONE
                else -> binding.confirmBtn.visibility = View.VISIBLE

            }

            binding.confirmBtn.hideLoading()
            binding.confirmBtn.setOnClickListener {
                binding.confirmBtn.showLoading()
                statusListener(item.notification, Status.Value.CONFIRMED)
            }

            binding.declineBtn.hideLoading()
            binding.declineBtn.setOnClickListener {
                binding.declineBtn.showLoading()
                statusListener(item.notification, Status.Value.DENIED)
            }
            binding.declineBtn.visibility = View.VISIBLE

            binding.waitlistBtn.hideLoading()
            binding.waitlistBtn.setOnClickListener {
                binding.waitlistBtn.showLoading()
                statusListener(item.notification, Status.Value.WAITLIST)
            }
            binding.waitlistBtn.visibility = View.VISIBLE

            //source
            binding.onlineIcon.colorFilter = null
            if (item.notification.reservation?.source != null && item.notification.reservation?.source?.isBlank() == false
                && item.notification.reservation?.source != binding.root.resources.getString(R.string.in_house_source)
                && item.notification.reservation?.source != Status.Value.WAITLIST.code) {

                val source = BookingSource.sources?.firstOrNull { it.name == item.notification.reservation?.source }

                source?.logo?.let {
                    Glide.with(binding.root)
                        .load(it)
                        .listener(object : RequestListener<Drawable> {
                            override fun onResourceReady(
                                resource: Drawable, model: Any, target: Target<Drawable>?,
                                dataSource: DataSource, isFirstResource: Boolean
                            ): Boolean { return false }

                            override fun onLoadFailed(
                                e: GlideException?,
                                model: Any?,
                                target: Target<Drawable>,
                                isFirstResource: Boolean
                            ): Boolean {
                                loadResourceFallbackIcon(item.notification.reservation?.source)
                                return true
                            }
                        })
                        .into(binding.onlineIcon)
                } ?: run {
                    loadResourceFallbackIcon(item.notification.reservation?.source)
                }
            }

            if (item.notification.reservation?.animateStatusChange == true) {
                binding.statusCont.animate()
                    .scaleX(1.3f)
                    .scaleY(1.3f)
                    .setDuration(300)
                    .setInterpolator(android.view.animation.AccelerateInterpolator())
                    .withEndAction {
                        binding.statusCont.animate()
                            .scaleX(1f)
                            .scaleY(1f)
                            .setDuration(120)
                            .setInterpolator(android.view.animation.DecelerateInterpolator())
                            .start()
                    }
                    .start()

                binding.imageStatus.animate()
                    .scaleX(1.4f)
                    .scaleY(1.4f)
                    .setDuration(300)
                    .setInterpolator(android.view.animation.AccelerateInterpolator())
                    .withEndAction {
                        binding.imageStatus.animate()
                            .scaleX(1f)
                            .scaleY(1f)
                            .setDuration(120)
                            .setInterpolator(android.view.animation.DecelerateInterpolator())
                            .start()
                    }
                    .start()

                item.notification.reservation?.animateStatusChange = false
            }

            binding.root.setOnClickListener { listener(item.notification) }
        }

        private fun loadResourceFallbackIcon(source: String?) {
            val resources = binding.onlineIcon.resources
            binding.onlineIcon.setImageDrawable(
                TextDrawable(
                    resources,
                    binding.onlineIcon.context,
                    source?.uppercase()?.first().toString(),
                    resources.getDimension(R.dimen.abbreviation_mega_text_size),
                    resources.getDimension(R.dimen.abbreviation_text_mega_position)
                )
            )
            binding.onlineIcon.colorFilter =
                PorterDuffColorFilter("#128849".toColorInt(), PorterDuff.Mode.SRC_ATOP)
        }
    }

    companion object {
        private const val VIEW_TYPE_DATE = 0
        private const val VIEW_TYPE_RESERVATION = 1
    }
}

sealed class NotificationAdapterItem

data class DateItem(val date: String) : NotificationAdapterItem()
data class NotificationItem(val notification: Notification) : NotificationAdapterItem()

fun NotificationItem.copyDeep(): NotificationItem {
    return NotificationItem(notification.copyDeep())
}

private class RequestedReservationDiffCallback : DiffUtil.ItemCallback<NotificationAdapterItem>() {
    override fun areItemsTheSame(oldItem: NotificationAdapterItem, newItem: NotificationAdapterItem): Boolean {
        return when {
            oldItem is DateItem && newItem is DateItem -> oldItem.date == newItem.date
            oldItem is NotificationItem && newItem is NotificationItem -> oldItem.notification.id == newItem.notification.id
            else -> false
        }
    }

    override fun areContentsTheSame(oldItem: NotificationAdapterItem, newItem: NotificationAdapterItem): Boolean {
        return when {
            oldItem is DateItem && newItem is DateItem -> oldItem == newItem
            oldItem is NotificationItem && newItem is NotificationItem -> oldItem == newItem
            else -> false
        }
    }
} 