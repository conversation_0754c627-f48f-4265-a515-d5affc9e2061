package com.eatapp.clementine.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.eatapp.clementine.data.network.response.pos.MenuItem
import com.eatapp.clementine.databinding.ModifierItemBinding
import com.eatapp.clementine.databinding.PosRecordItemBinding

class PosItemsAdapter(
    private val currency: String?
) : ListAdapter<MenuItem, PosItemsAdapter.ViewHolder>(MenuItemDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {

        return ViewHolder(
            PosRecordItemBinding.inflate(
            LayoutInflater.from(parent.context), parent, false))

    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {

        val item = getItem(position)
        holder.apply { bind(item, currency) }
    }

    class ViewHolder(
        private val binding: PosRecordItemBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(i: MenuItem, c: String?) {
            binding.apply {
                item = i
                currency = c

                val modifiersAdapter = ModifiersAdapter(c)
                binding.modifiersList.adapter = modifiersAdapter
                modifiersAdapter.submitList(i.modifiers)

                executePendingBindings()
            }
        }
    }
}

class ModifiersAdapter(
    private val currency: String?
) : ListAdapter<MenuItem, ModifiersAdapter.ViewHolder>(MenuItemDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {

        return ViewHolder(
            ModifierItemBinding.inflate(
                LayoutInflater.from(parent.context), parent, false))

    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {

        val item = getItem(position)
        holder.apply { bind(item, currency) }
    }

    class ViewHolder(
        private val binding: ModifierItemBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(i: MenuItem, c: String?) {
            binding.apply {
                item = i
                currency = c
                executePendingBindings()
            }
        }
    }
}

private class MenuItemDiffCallback : DiffUtil.ItemCallback<MenuItem>() {

    override fun areItemsTheSame(oldItem: MenuItem, newItem: MenuItem): Boolean {
        return oldItem.name == newItem.name
    }

    override fun areContentsTheSame(oldItem: MenuItem, newItem: MenuItem): Boolean {
        return oldItem == newItem
    }
}