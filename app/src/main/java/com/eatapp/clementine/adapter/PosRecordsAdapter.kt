package com.eatapp.clementine.adapter

import android.annotation.SuppressLint
import android.graphics.Color
import android.graphics.PorterDuff
import android.graphics.PorterDuffColorFilter
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.GridLayout
import android.widget.ImageView
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.eatapp.clementine.R
import com.eatapp.clementine.data.network.response.pos.PosRecord
import com.eatapp.clementine.databinding.ItemHeaderBinding
import com.eatapp.clementine.databinding.ListItemPosRecordBinding
import com.eatapp.clementine.enums.ReservationTag
import com.eatapp.clementine.internal.PosSection
import com.eatapp.clementine.internal.TextDrawable

class PosRecordsAdapter(
    private val onPosRecordClickListener: (PosRecord) -> Unit
) : ListAdapter<Any, RecyclerView.ViewHolder>(PosRecordDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            ViewType.SECTION -> {
                SectionViewHolder(
                    ItemHeaderBinding.inflate(
                        LayoutInflater.from(parent.context), parent, false
                    )
                )
            }
            ViewType.SECTION_ITEM -> {
                PosRecordViewHolder(
                    ListItemPosRecordBinding.inflate(
                        LayoutInflater.from(parent.context), parent, false
                    )
                )
            }
            else -> throw(Throwable("View type not matching"))
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (val item = getItem(position)) {
            is PosSection -> (holder as?
                    SectionViewHolder)?.bind(item.title)
            is PosRecord -> (holder as?
                    PosRecordViewHolder)?.bind(onPosRecordClickListener, item)
        }
    }

    override fun getItemViewType(position: Int): Int {
        return when (getItem(position)) {
            is PosSection -> {
                ViewType.SECTION
            }
            is PosRecord -> {
                ViewType.SECTION_ITEM
            }
            else -> super.getItemViewType(position)
        }
    }

    object ViewType {
        const val SECTION = 101
        const val SECTION_ITEM = 102
    }

    class PosRecordViewHolder(
        private val binding: ListItemPosRecordBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(listener: (PosRecord) -> Unit, pr: PosRecord) {

            binding.apply {
                clickListener = View.OnClickListener { listener(pr) }
                reservation = pr.reservation
                posRecord = pr
                executePendingBindings()
            }

            // tags
            binding.tagsCont.removeAllViews()

            pr.reservation?.taggings?.take(6)?.forEach { tag ->
                binding.tagsCont.addView(tagIcon(ReservationTag.getIcon(tag.icon), tag.color))
            }

            if ((pr.reservation?.taggings?.size ?: 0) < 6) pr.reservation?.guest?.taggings?.take(
                6 - (pr.reservation?.taggings?.size ?: 0)
            )?.forEach { tag ->
                binding.tagsCont.addView(tagIcon(ReservationTag.getIcon(tag.icon), tag.color))
            }

            itemView.tag = pr.reservation
        }

        private fun tagIcon(icon: Any?, color: String?): ImageView {

            val tagView = ImageView(itemView.context)

            val size = if (icon is Int) {
                itemView.context.resources.getDimension(
                    R.dimen.tag_reservation_item_size)
            } else {
                itemView.context.resources.getDimension(
                    R.dimen.abbreviation_reservation_item_size)
            }
            val margin = itemView.context.resources.getDimension(
                R.dimen.tag_reservation_item_margin
            )

            val params: GridLayout.LayoutParams = GridLayout.LayoutParams()
            params.height = size.toInt()
            params.width = size.toInt()
            params.marginEnd = margin.toInt()
            params.setGravity(Gravity.CENTER_VERTICAL)

            if (icon is Int) {
                tagView.setImageResource(icon)
            } else if (icon is String) {
                tagView.setImageDrawable(
                    TextDrawable(
                        tagView.resources, tagView.context,
                        icon, tagView.resources.getDimension(R.dimen.abbreviation_small_text_size),
                        tagView.resources.getDimension(R.dimen.abbreviation_text_position)
                    )
                )
            }

            color?.let {
                tagView.colorFilter =
                    PorterDuffColorFilter(Color.parseColor(color), PorterDuff.Mode.SRC_ATOP)
            }

            tagView.layoutParams = params

            return tagView
        }
    }

    class SectionViewHolder(
        private val binding: ItemHeaderBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(item: String) {
            binding.apply {
                section = String.format("%s reservations", item)
                showSeparator = true
                executePendingBindings()
            }
        }
    }
}

private class PosRecordDiffCallback : DiffUtil.ItemCallback<Any>() {

    override fun areItemsTheSame(oldItem: Any, newItem: Any): Boolean {
        return when {
            oldItem is PosRecord && newItem is PosRecord -> {
                oldItem.id == newItem.id
            }
            oldItem is String && newItem is String -> {
                oldItem == newItem
            }
            else -> false
        }
    }

    @SuppressLint("DiffUtilEquals")
    override fun areContentsTheSame(oldItem: Any, newItem: Any): Boolean {
        return when {
            oldItem is PosRecord && newItem is PosRecord -> {
                oldItem == newItem
            }
            oldItem is String && newItem is String -> {
                oldItem == newItem
            }
            else -> false
        }
    }
}