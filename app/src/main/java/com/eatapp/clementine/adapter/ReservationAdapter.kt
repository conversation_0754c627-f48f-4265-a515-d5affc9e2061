package com.eatapp.clementine.adapter

import android.annotation.SuppressLint
import android.graphics.Color
import android.graphics.PorterDuff
import android.graphics.PorterDuffColorFilter
import android.graphics.drawable.Drawable
import android.text.TextUtils
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.GridLayout
import android.widget.ImageView
import androidx.core.content.ContextCompat
import androidx.databinding.library.baseAdapters.BR
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.eatapp.clementine.R
import com.eatapp.clementine.data.network.response.payment.PaymentStatus
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.network.response.user.Permission
import com.eatapp.clementine.databinding.ListItemReservationBinding
import com.eatapp.clementine.databinding.ListItemReservationGroupBinding
import com.eatapp.clementine.databinding.ReservationSectionItemBinding
import com.eatapp.clementine.enums.ReservationTag
import com.eatapp.clementine.internal.BookingSource
import com.eatapp.clementine.internal.ReservationGroupSection
import com.eatapp.clementine.internal.ReservationSection
import com.eatapp.clementine.internal.Status
import com.eatapp.clementine.internal.TextDrawable
import com.eatapp.clementine.internal.visible
import com.eatapp.clementine.internal.visibleOrGone
import com.eatapp.clementine.ui.home.overview.ReservationsViewModel
import java.util.*
import kotlin.concurrent.fixedRateTimer

typealias StatusClickListener = (Reservation) -> Unit
typealias ReservationClickListener = (Reservation) -> Unit
typealias SectionGroupClickListener = (ReservationGroupSection) -> Unit
typealias SectionClickListener = (ReservationSection) -> Unit

class ReservationAdapter(
    val type: ListType,
    val posActive: Boolean,
    private val openedReservations: MutableMap<String, Date>,
    private val paymentsActive: Boolean,
    private val timerPermission: Permission?,
    private val onReservationClickListener: ReservationClickListener,
    private val onSectionGroupClickListener: SectionGroupClickListener,
    private val onSectionClickListener: SectionClickListener,
    private val onStatusClickListener: StatusClickListener,
) : ListAdapter<Any, RecyclerView.ViewHolder>(ReservationDiffCallback()) {

    val maxTagsDisplayed = 4

    private var timer: Timer?

    private var filterType: ReservationsViewModel.FilterType = ReservationsViewModel.FilterType.ALL
    private var reservationCompactMode: Boolean = false

    enum class ListType {
        OVERVIEW,
        GUEST
    }

    @SuppressLint("NotifyDataSetChanged")
    fun submitList(
        list: MutableList<Any>?,
        filterType: ReservationsViewModel.FilterType,
        reservationCompactMode: Boolean,
    ) {
        this.filterType = filterType

        if (this.reservationCompactMode != reservationCompactMode) {
            this.notifyDataSetChanged()
        }

        this.reservationCompactMode = reservationCompactMode

        list?.map {
            if (it is Reservation) {
                it.timerPermission = timerPermission
                it.lastOpenedAt = openedReservations[it.id]
            }
        }

        super.submitList(list)
    }

    init {

        timer = fixedRateTimer("default", false, 100L, 1000L) {
            currentList.forEachIndexed { index, reservation ->
                if (reservation is Reservation && reservation.status == Status.Value.WAITLIST.code) {
                    (getItem(index) as Reservation).notifyPropertyChanged(BR.waitlistInterval)
                }
                if (reservation is Reservation) {
                    reservation.lastOpenedAt = openedReservations[reservation.id]
                    reservation.notifyPropertyChanged(BR.updatedAtInterval)
                }
            }
        }
    }

    fun stopTimers() {
        timer?.cancel()
        timer?.purge()
        timer = null
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            ViewType.SECTION -> {
                SectionViewHolder(
                    ReservationSectionItemBinding.inflate(
                        LayoutInflater.from(parent.context), parent, false
                    )
                )
            }

            ViewType.SECTION_ITEM -> {
                ReservationViewHolder(
                    ListItemReservationBinding.inflate(
                        LayoutInflater.from(parent.context), parent, false
                    )
                )
            }

            ViewType.SECTION_GROUP -> {
                ReservationGroupViewHolder(
                    ListItemReservationGroupBinding.inflate(
                        LayoutInflater.from(parent.context), parent, false
                    )
                )
            }

            else -> SectionViewHolder(
                ReservationSectionItemBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
            )
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (val item = getItem(position)) {
            is ReservationSection -> (holder as? SectionViewHolder)?.bind(item, filterType)
            is Reservation -> (holder as? ReservationViewHolder)?.bind(
                onReservationClickListener,
                item, type, posActive, paymentsActive, statusClickListener = onStatusClickListener
            )

            is ReservationGroupSection -> (holder as? ReservationGroupViewHolder)?.bind(item)
        }
    }

    override fun getItemViewType(position: Int): Int {
        return when (getItem(position)) {
            is ReservationSection -> {
                ViewType.SECTION
            }

            is Reservation -> {
                ViewType.SECTION_ITEM
            }

            is ReservationGroupSection -> {
                ViewType.SECTION_GROUP
            }

            else -> super.getItemViewType(position)
        }
    }

    object ViewType {
        const val SECTION = 101
        const val SECTION_ITEM = 102
        const val SECTION_GROUP = 103
    }

    inner class ReservationViewHolder(
        val binding: ListItemReservationBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        @SuppressLint("SetTextI18n")
        fun bind(
            listener: ReservationClickListener,
            item: Reservation,
            t: ListType,
            posActive: Boolean,
            paymentsActive: Boolean,
            statusClickListener: StatusClickListener
        ) {

            val showSource = item.source?.isBlank() == false
                    && item.source != (binding.root.context.resources.getString(R.string.in_house_source))
                    && item.source != Status.Value.WAITLIST.code
            val showTags = !item.taggings.isNullOrEmpty() || !item.guest?.taggings.isNullOrEmpty()
            val showPos = ((!Status.isUpcoming(item.status) && !Status.isRemoved(item.status))
                    || item.hasPosTickets) && posActive
            val showPreference = !TextUtils.isEmpty(item.channel?.displayName)

            binding.apply {
                clickListener = View.OnClickListener { listener(item) }
                statusCont.setOnClickListener {
                    statusClickListener(item)
                }
                reservation = item
                type = t
                this.showSource = showSource
                this.paymentsActive = paymentsActive
                executePendingBindings()
            }

            // tags
            binding.tagsCont.removeAllViews()

            item.taggings?.take(maxTagsDisplayed)?.forEach { tag ->
                binding.tagsCont.addView(tagIcon(ReservationTag.getIcon(tag.icon), tag.color))
            }

            if ((item.taggings?.size ?: 0) < maxTagsDisplayed) item.guest?.taggings?.take(
                maxTagsDisplayed - (item.taggings?.size ?: 0)
            )?.forEach { tag ->
                binding.tagsCont.addView(tagIcon(ReservationTag.getIcon(tag.icon), tag.color))
            }

            // table
            binding.textTable.text =
                if (item.tables?.isEmpty() == true) "--" else item.tables?.joinToString { it.number }

            itemView.tag = item

            binding.layoutStatuses.visibility =
                if (reservationCompactMode || (!showTags && !showPos && !paymentsActive && !showSource && !showPreference)) View.GONE else View.VISIBLE

            binding.ivCompactViewTags.visibleOrGone = if (reservationCompactMode) {
                item.taggings?.isNotEmpty() ?: false || item.guest?.taggings?.isNotEmpty() ?: false
            } else {
                false
            }

            // pos
            if (showPos) {
                binding.posIcon.visibility = View.VISIBLE
            } else {
                binding.posIcon.visibility = View.GONE
            }

            // payment
            if (item.payments.isEmpty()) {
                binding.paymentsIcon.visibleOrGone = false
            } else {
                binding.paymentsIcon.visible = true
                binding.paymentsIcon.setImageResource(PaymentStatus.icon(item.payments[0].status))
            }

            //source
            binding.onlineIcon.colorFilter = null
            if (item.source != null && item.source?.isBlank() == false
                && item.source != binding.root.resources.getString(R.string.in_house_source)
                && item.source != Status.Value.WAITLIST.code
            ) {

                val source = BookingSource.sources?.firstOrNull { it.name == item.source }

                source?.logo?.let {
                    Glide.with(binding.root)
                        .load(it)
                        .listener(object : RequestListener<Drawable> {
                            override fun onResourceReady(
                                resource: Drawable, model: Any, target: Target<Drawable>?,
                                dataSource: DataSource, isFirstResource: Boolean
                            ): Boolean {
                                return false
                            }

                            override fun onLoadFailed(
                                e: GlideException?,
                                model: Any?,
                                target: Target<Drawable>,
                                isFirstResource: Boolean
                            ): Boolean {
                                loadResourceFallbackIcon(item)
                                return true
                            }
                        })
                        .into(binding.onlineIcon)
                } ?: run {
                    loadResourceFallbackIcon(item)
                }
            }

            // notes
            binding.containerReservationNotes.visibleOrGone =
                !item.notes.isNullOrEmpty() && !reservationCompactMode
            binding.tvNotes.text = item.notes

            // waitlist position
            binding.tvWaitListPosition.visibleOrGone =
                item.attributes.waitlistPosition != null && !reservationCompactMode && item.status == Status.Value.WAITLIST.code
            binding.tvWaitListPosition.text = "#${item.attributes.waitlistPosition}"
        }

        private fun loadResourceFallbackIcon(item: Reservation) {
            val resources = binding.onlineIcon.resources
            binding.onlineIcon.setImageDrawable(
                TextDrawable(
                    resources,
                    binding.onlineIcon.context,
                    item.source.toString().uppercase().first().toString(),
                    resources.getDimension(R.dimen.abbreviation_mega_text_size),
                    resources.getDimension(R.dimen.abbreviation_text_mega_position)
                )
            )
            binding.onlineIcon.colorFilter =
                PorterDuffColorFilter(Color.parseColor("#128849"), PorterDuff.Mode.SRC_ATOP)
        }

        private fun tagIcon(icon: Any?, color: String?): ImageView {

            val tagView = ImageView(itemView.context)

            val margin = itemView.context.resources.getDimension(
                R.dimen.tag_reservation_item_margin
            )

            val params: GridLayout.LayoutParams = GridLayout.LayoutParams()
            params.width = itemView.context.resources.getDimension(
                R.dimen.tag_reservation_item_size
            ).toInt()
            params.height = itemView.context.resources.getDimension(
                R.dimen.tag_reservation_item_size
            ).toInt()
            params.marginEnd = margin.toInt()
            params.setGravity(Gravity.CENTER_VERTICAL)

            if (icon is Int) {
                tagView.setImageResource(icon)
            } else if (icon is String) {
                tagView.setImageDrawable(
                    TextDrawable(
                        tagView.resources, tagView.context,
                        icon, tagView.resources.getDimension(R.dimen.abbreviation_small_text_size),
                        tagView.resources.getDimension(R.dimen.abbreviation_text_position)
                    )
                )
            }

            color?.let {
                tagView.colorFilter =
                    PorterDuffColorFilter(Color.parseColor(color), PorterDuff.Mode.SRC_ATOP)
            }

            tagView.layoutParams = params

            return tagView
        }
    }

    inner class SectionViewHolder(
        private val binding: ReservationSectionItemBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(
            item: ReservationSection,
            ft: ReservationsViewModel.FilterType
        ) {
            binding.apply {
                item.filterType = ft
                section = item
                executePendingBindings()
            }

            val color = when (item.color) {
                is Int -> ContextCompat.getColor(itemView.context, item.color)
                else -> Color.parseColor(item.color as String)
            }
            binding.backgroundView.background.setColorFilter(
                color, PorterDuff.Mode.SRC_ATOP
            )

            binding.bottomLine.background.setColorFilter(
                color, PorterDuff.Mode.SRC_ATOP
            )

            binding.ivChevron.setImageResource(
                if (item.expanded) R.drawable.ic_icon_arrow_up else R.drawable.ic_icon_arrow_down
            )

            binding.root.setOnClickListener {
                onSectionClickListener.invoke(item)
            }
        }
    }

    inner class ReservationGroupViewHolder(private val binding: ListItemReservationGroupBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(groupSection: ReservationGroupSection) {
            binding.title.text = groupSection.title
            binding.textReservationCount.text =
                groupSection.reservationsCount(filterType).toString()
            binding.textCoversCount.text = groupSection.coversCount(filterType).toString()
            binding.ivChevron.setImageResource(
                if (groupSection.expanded) R.drawable.ic_icon_arrow_up else R.drawable.ic_icon_arrow_down
            )

            binding.containerHeader.setOnClickListener {
                onSectionGroupClickListener.invoke(groupSection)
            }
        }
    }
}

private class ReservationDiffCallback : DiffUtil.ItemCallback<Any>() {

    override fun areItemsTheSame(oldItem: Any, newItem: Any): Boolean {
        return when {
            oldItem is ReservationSection && newItem is ReservationSection -> {
                oldItem.title == newItem.title
            }

            oldItem is Reservation && newItem is Reservation -> {
                oldItem.id == newItem.id
            }

            oldItem is ReservationGroupSection && newItem is ReservationGroupSection -> {
                oldItem.title == newItem.title
            }

            else -> false
        }
    }

    @SuppressLint("DiffUtilEquals")
    override fun areContentsTheSame(oldItem: Any, newItem: Any): Boolean {
        return when {
            oldItem is ReservationSection && newItem is ReservationSection -> {
                return oldItem.title == newItem.title && oldItem.coversCount == newItem.coversCount && oldItem.resCount == newItem.resCount && oldItem.expanded == newItem.expanded
            }

            oldItem is Reservation && newItem is Reservation -> {
                return if ((oldItem.status == Status.Value.WAITLIST.code && newItem.status == Status.Value.WAITLIST.code) || (oldItem.showUpdateInterval || newItem.showUpdateInterval)) false else oldItem == newItem
            }

            oldItem is ReservationGroupSection && newItem is ReservationGroupSection -> {
                return false
            }

            else -> false
        }
    }
}