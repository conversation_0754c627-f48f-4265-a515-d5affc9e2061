package com.eatapp.clementine.adapter

import android.annotation.SuppressLint
import android.graphics.PorterDuff
import android.graphics.PorterDuffColorFilter
import android.graphics.drawable.Drawable
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CompoundButton
import androidx.core.content.ContextCompat
import androidx.core.view.setPadding
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.eatapp.clementine.R
import com.eatapp.clementine.data.network.response.custom_fields.CustomField
import com.eatapp.clementine.data.network.response.custom_fields.CustomFieldType
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.network.response.user.Permission
import com.eatapp.clementine.data.network.response.user.PermissionName
import com.eatapp.clementine.data.network.response.vouchers.VoucherAssignmentModel
import com.eatapp.clementine.databinding.DeleteReservationListItemBinding
import com.eatapp.clementine.databinding.DividerListItemBinding
import com.eatapp.clementine.databinding.DoubleToggleListItemBinding
import com.eatapp.clementine.databinding.InputSelectArrowBinding
import com.eatapp.clementine.databinding.InputSelectBinding
import com.eatapp.clementine.databinding.ListItemCustomFieldMultiBinding
import com.eatapp.clementine.databinding.ListItemVouchersBinding
import com.eatapp.clementine.databinding.ReservationGuestListItemBinding
import com.eatapp.clementine.databinding.ReservationTagsListItemBinding
import com.eatapp.clementine.databinding.ToggleListItemBinding
import com.eatapp.clementine.internal.BookingSource
import com.eatapp.clementine.internal.SelectorItem
import com.eatapp.clementine.internal.Status
import com.eatapp.clementine.internal.TextDrawable
import com.eatapp.clementine.internal.labelized
import com.eatapp.clementine.internal.px
import com.eatapp.clementine.internal.statusColor
import com.eatapp.clementine.internal.visibleOrGone
import com.eatapp.clementine.ui.common.custom_fields.CustomFieldMultiAdapter
import com.eatapp.clementine.ui.common.tables.conflict.ConflictItem
import com.eatapp.clementine.ui.common.tables.conflict.ConflictsRecyclerViewAdapter
import com.eatapp.clementine.ui.reservation.ReservationDetailsItem
import com.eatapp.clementine.ui.reservation.ReservationDetailsItemType
import com.eatapp.clementine.ui.reservation.vouchers.VouchersAssignmentsAdapter
import com.eatapp.clementine.views.DoubleStepperView
import com.eatapp.clementine.views.ExpandableInputRegular
import com.eatapp.clementine.views.InputSelectStepperView
import com.eatapp.clementine.views.StepperView
import com.eatapp.clementine.views.StepperView.Companion.setDisabled

class ReservationDetailsAdapter(
    val reservation: Reservation,
    var conflictList: List<ConflictItem>,
    val tagsAvailable: Boolean,
    val hideReservationTaker: Boolean,
    val deleteButtonVisible: Boolean,
    val permissionReservation: Permission,
    val permissionGuest: Permission,
    val loyaltyActive: Boolean,
    val isFreemium: Boolean,
    val isMarketingVisible: Boolean,
    val posActive: Boolean,
    val currency: String,
    var reservationTaggingsList: MutableList<SelectorItem>?,
    var guestTaggingsList: MutableList<SelectorItem>?,
    val variationWhatsappChatFlow: String?,
    val showNewGuestFlow: Boolean,
    val showVouchers: Boolean,
    val addTagListener: AddTagListener? = null,
    val removeTagListener: RemoveTagListener? = null
) : ListAdapter<ReservationDetailsItem, RecyclerView.ViewHolder>(
    ReservationDetailsListItemsDiffCallback()
) {

    companion object {
        const val ITEM_TYPE_MULTI = 996
        const val ITEM_TYPE_INPUT = 997
        const val ITEM_TYPE_BOOLEAN = 998
        const val ITEM_TYPE_COUNTABLE = 999
    }

    private lateinit var reservationTagsAdapter: TagAdapter
    private lateinit var tablesConflictAdapter: ConflictsRecyclerViewAdapter
    private lateinit var timesConflictAdapter: ConflictsRecyclerViewAdapter
    private lateinit var vouchersAssignmentsAdapter: VouchersAssignmentsAdapter

    var statusClickListener: (() -> Unit)? = null
    var tableClickListener: (() -> Unit)? = null
    var dateClickListener: View.OnClickListener? = null
    var timeUpdateListener: ((s: StepperView.Step) -> Unit)? = null
    var timeClickListener: (() -> Unit)? = null
    var coversUpdateListener: ((s: StepperView.Step) -> Unit)? = null
    var coversClickListener: (() -> Unit)? = null
    var waitTimeUpdateListener: ((s: StepperView.Step) -> Unit)? = null
    var waitTimeClickListener: (() -> Unit)? = null
    var durationUpdateListener: ((s: StepperView.Step) -> Unit)? = null
    var durationClickListener: (() -> Unit)? = null
    var commentsClickListener: (() -> Unit)? = null
    var walkInCheckedChangeListener: CompoundButton.OnCheckedChangeListener? = null
    var waitlistCheckedChangeListener: CompoundButton.OnCheckedChangeListener? = null
    var notesChangedListener: TextWatcher? = null
    var takerClickListener: (() -> Unit)? = null
    var guestChangeIconClickListener: ((Boolean) -> Unit)? = null
    var guestSearchLayoutClickListener: (() -> Unit)? = null
    var guestDetailsClickListener: (() -> Unit)? = null
    var guestPhoneNumberClickListener: (() -> Unit)? = null
    var guestGoogleClickListener: (() -> Unit)? = null
    var guestWhatsappClickListener: (() -> Unit)? = null
    var guestTagsChevronClickListener: (() -> Unit)? = null
    var deleteReservationClickListener: (() -> Unit)? = null
    var sendMessageCheckedChangeListener: CompoundButton.OnCheckedChangeListener? = null
    var customFieldUpdateListener: ((Pair<String, Any>) -> Unit)? = null
    var marketingCheckedChangeListener: View.OnClickListener? = null
    var customFieldCounterUpdateListener: ((Pair<String, StepperView.Step>) -> Unit)? = null
    var customFieldCounterClickListener: ((String) -> Unit)? = null
    var sourceClickListener: ((String?) -> Unit)? = null
    var vouchersClickListener: (() -> Unit)? = null
    var voucherClickListener: ((VoucherAssignmentModel) -> Unit)? = null
    var loyaltyClickListener: (() -> Unit)? = null

    private var deleteLoadingInProgress = false
    private var sendMessageEnabled = false

    init {
        bindAdapters()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {

        return when (viewType) {

            ReservationDetailsItemType.GUEST.itemType -> {
                GuestViewHolder(
                    ReservationGuestListItemBinding.inflate(
                        LayoutInflater.from(parent.context), parent, false
                    )
                )
            }

            ReservationDetailsItemType.PREFERENCE.itemType -> {
                PreferenceViewHolder(
                    InputSelectBinding.inflate(
                        LayoutInflater.from(parent.context), parent, false
                    )
                )
            }

            ReservationDetailsItemType.SOURCE.itemType -> {
                SourceViewHolder(
                    InputSelectBinding.inflate(
                        LayoutInflater.from(parent.context), parent, false
                    )
                )
            }

            ReservationDetailsItemType.STATUS.itemType -> {
                StatusViewHolder(
                    InputSelectBinding.inflate(
                        LayoutInflater.from(parent.context), parent, false
                    )
                )
            }

            ReservationDetailsItemType.WALK_IN_WAITLIST.itemType -> {
                WalkinWaitlistViewHolder(
                    DoubleToggleListItemBinding.inflate(
                        LayoutInflater.from(parent.context), parent, false
                    )
                )
            }

            ReservationDetailsItemType.DATE_TIME.itemType -> {
                DateTimeViewHolder(
                    InputSelectStepperView(parent.context)
                )
            }

            ReservationDetailsItemType.COVERS_DURATION.itemType -> {
                CoversDurationViewHolder(
                    DoubleStepperView(parent.context)
                )
            }

            ReservationDetailsItemType.TABLE.itemType -> {
                TableViewHolder(
                    InputSelectArrowBinding.inflate(
                        LayoutInflater.from(parent.context), parent, false
                    )
                )
            }

            ReservationDetailsItemType.WAIT_TIME.itemType -> {
                WaitTimeViewHolder(
                    StepperView(parent.context)
                )
            }

            ReservationDetailsItemType.NOTES.itemType -> {
                ReservationNotesViewHolder(
                    ExpandableInputRegular(parent.context)
                )
            }

            ReservationDetailsItemType.COMMENTS.itemType -> {
                ReservationCommentsViewHolder(
                    InputSelectArrowBinding.inflate(
                        LayoutInflater.from(parent.context), parent, false
                    )
                )
            }

            ReservationDetailsItemType.TAKER.itemType -> {
                ReservationTakerViewHolder(
                    InputSelectBinding.inflate(
                        LayoutInflater.from(parent.context), parent, false
                    )
                )
            }

            ReservationDetailsItemType.TAGS.itemType -> {
                ReservationTagsViewHolder(
                    ReservationTagsListItemBinding.inflate(
                        LayoutInflater.from(parent.context), parent, false
                    )
                )
            }

            ReservationDetailsItemType.DELETE_RESERVATION.itemType -> {
                DeleteReservationViewHolder(
                    DeleteReservationListItemBinding.inflate(
                        LayoutInflater.from(parent.context), parent, false
                    )
                )
            }

            ReservationDetailsItemType.SEND_MESSAGE.itemType -> {
                SendMessageViewHolder(
                    ToggleListItemBinding.inflate(
                        LayoutInflater.from(parent.context), parent, false
                    )
                )
            }

            ReservationDetailsItemType.DIVIDER.itemType -> {
                DividerViewHolder(
                    DividerListItemBinding.inflate(
                        LayoutInflater.from(parent.context), parent, false
                    )
                )
            }

            ReservationDetailsItemType.VOUCHER.itemType -> {
                ReservationVoucherViewHolder(
                    ListItemVouchersBinding.inflate(
                        LayoutInflater.from(parent.context), parent, false
                    )
                )
            }

            ReservationDetailsItemType.LOYALTY.itemType -> {
                LoyaltyPointsViewHolder(
                    InputSelectBinding.inflate(
                        LayoutInflater.from(parent.context), parent, false
                    )
                )
            }

            ITEM_TYPE_INPUT -> {
                CustomFieldInputViewHolder(
                    ExpandableInputRegular(parent.context)
                )
            }

            ITEM_TYPE_BOOLEAN -> {
                CustomFieldCheckBoxViewHolder(
                    ToggleListItemBinding.inflate(
                        LayoutInflater.from(parent.context), parent, false
                    )
                )
            }

            ITEM_TYPE_COUNTABLE -> {
                CustomFieldCountableViewHolder(
                    StepperView(parent.context)
                )
            }

            ITEM_TYPE_MULTI -> {
                CustomFieldMultiViewHolder(
                    ListItemCustomFieldMultiBinding.inflate(
                        LayoutInflater.from(parent.context), parent, false
                    )
                )
            }

            else -> {
                PreferenceViewHolder(
                    InputSelectBinding.inflate(
                        LayoutInflater.from(parent.context), parent, false
                    )
                )
            }
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is ReservationDetailsViewHolder -> {
                holder.bind()
            }

            is GuestViewHolder -> {
                holder.bind(getItem(position))
            }

            is ReservationNotesViewHolder -> {
                holder.bind()
            }

            is CustomFieldInputViewHolder -> {
                holder.bind(getItem(position).customField)
            }

            is CustomFieldCheckBoxViewHolder -> {
                holder.bind(getItem(position).customField)
            }

            is CustomFieldCountableViewHolder -> {
                holder.bind(getItem(position).customField)
            }

            is CustomFieldMultiViewHolder -> {
                holder.bind(getItem(position).customField)
            }
        }
    }

    override fun getItemViewType(position: Int): Int {
        val item = getItem(position)

        return when (item.customField?.type) {
            CustomFieldType.TEXT -> {
                ITEM_TYPE_INPUT
            }

            CustomFieldType.BOOLEAN -> {
                ITEM_TYPE_BOOLEAN
            }

            CustomFieldType.COUNTABLE -> {
                ITEM_TYPE_COUNTABLE
            }

            CustomFieldType.MULTI -> {
                ITEM_TYPE_MULTI
            }

            else -> {
                getItem(position).itemType
            }
        }
    }

    fun updateReservationTags(taggings: MutableList<SelectorItem>?) {
        this.reservationTaggingsList = taggings
        val position =
            currentList.indexOfFirst { it.itemType == ReservationDetailsItemType.TAGS.itemType }
        notifyItemChanged(position)
    }

    fun updateGuestTags(taggings: MutableList<SelectorItem>?) {
        this.guestTaggingsList = taggings
        val position =
            currentList.indexOfFirst { it.itemType == ReservationDetailsItemType.GUEST.itemType }
        notifyItemChanged(position)
    }

    fun updateItem(itemType: ReservationDetailsItemType) {
        val position = currentList.indexOfFirst { it.itemType == itemType.itemType }
        notifyItemChanged(position)
    }

    fun updateCustomField(name: String) {
        val position = currentList.indexOfFirst { it.customField?.attributes?.name == name }
        notifyItemChanged(position)
    }

    fun showDeleteLoading() {
        deleteLoadingInProgress = true
        updateItem(ReservationDetailsItemType.DELETE_RESERVATION)
    }

    fun hideDeleteLoading() {
        deleteLoadingInProgress = false
        updateItem(ReservationDetailsItemType.DELETE_RESERVATION)
    }

    fun updateConflicts(conflictList: List<ConflictItem>) {
        this.conflictList = conflictList
        updateItem(ReservationDetailsItemType.TABLE)
        updateItem(ReservationDetailsItemType.DATE_TIME)
    }

    fun updateSendMessage(phoneText: String, emailText: String) {
        sendMessageEnabled =
            (reservation.guest?.phone != null || reservation.guest?.email != null || phoneText.isNotEmpty() || emailText.isNotEmpty()) && !isFreemium && !reservation.locked
        updateItem(ReservationDetailsItemType.SEND_MESSAGE)
    }

    private fun bindAdapters() {
        reservationTagsAdapter = TagAdapter(
            !isFreemium,
            addTagListener = addTagListener, removeTagListener = removeTagListener
        )
        tablesConflictAdapter = ConflictsRecyclerViewAdapter(false) { }
        timesConflictAdapter = ConflictsRecyclerViewAdapter(false) { }
        vouchersAssignmentsAdapter = VouchersAssignmentsAdapter({
            voucherClickListener?.invoke(it)
        })
    }

    private fun hideView(view: View) {
        view.visibleOrGone = false
        view.layoutParams = RecyclerView.LayoutParams(0, 0)
    }

    private fun showView(view: View) {
        view.visibleOrGone = true
        view.layoutParams = RecyclerView.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
    }

    inner class PreferenceViewHolder(
        private val binding: InputSelectBinding
    ) : ReservationDetailsViewHolder(binding.root) {

        override fun bind() {
            binding.run {
                if (TextUtils.isEmpty(reservation.channel?.displayName)) {
                    hideView(binding.root)
                    return@run
                }
                showView(binding.root)
                disabled = reservation.locked || isFreemium
                showIcon = false
                key = binding.root.context.getString(R.string.preference)
                value = reservation.channel?.displayName
            }
        }
    }

    inner class SourceViewHolder(
        private val binding: InputSelectBinding
    ) : ReservationDetailsViewHolder(binding.root) {

        override fun bind() {
            binding.run {

                if (BookingSource.sourcesWithImage.isNullOrEmpty()
                    || reservation.source == Status.Value.WAITLIST.code
                ) {
                    hideView(binding.root)
                    return@run
                }

                showIcon = false
                key = binding.root.context.getString(R.string.source)
                hint = binding.root.context.getString(R.string.select_reservation_source)
                value = null
                showView(binding.root)
                disabled = reservation.locked || isFreemium
                cont.setOnClickListener {
                    sourceClickListener?.invoke(reservation.source)
                }
            }

            //source
            if (reservation.source != null && reservation.source?.isBlank() == false
                && reservation.source != binding.root.resources.getString(R.string.in_house_source)
                && reservation.source != Status.Value.WAITLIST.code
            ) {

                binding.value = reservation.source?.labelized
                val source = BookingSource.sources?.firstOrNull { it.name == reservation.source }
                binding.showIcon = true

                val p = binding.root.resources.getDimension(R.dimen.margin_4).toInt()
                binding.iconImg.setPadding(p, p, p, p)

                source?.logo?.let {
                    Glide.with(binding.root)
                        .load(it)
                        .listener(object : RequestListener<Drawable> {
                            override fun onResourceReady(
                                resource: Drawable, model: Any, target: Target<Drawable>?,
                                dataSource: DataSource, isFirstResource: Boolean
                            ): Boolean {
                                return false
                            }

                            override fun onLoadFailed(
                                e: GlideException?,
                                model: Any?,
                                target: Target<Drawable>,
                                isFirstResource: Boolean
                            ): Boolean {
                                val resources = binding.iconImg.resources
                                binding.iconImg.setImageDrawable(
                                    TextDrawable(
                                        resources,
                                        binding.iconImg.context,
                                        reservation.source.toString().uppercase().first()
                                            .toString(),
                                        resources.getDimension(R.dimen.abbreviation_mega_text_size),
                                        resources.getDimension(R.dimen.margin_16)
                                    )
                                )
                                binding.iconImg.colorFilter =
                                    PorterDuffColorFilter(
                                        ContextCompat.getColor(
                                            binding.root.context,
                                            R.color.colorPrimary
                                        ), PorterDuff.Mode.SRC_ATOP
                                    )
                                return true
                            }
                        })
                        .into(binding.iconImg)
                } ?: run {
                    val resources = binding.iconImg.resources
                    binding.iconImg.setImageDrawable(
                        TextDrawable(
                            resources,
                            binding.iconImg.context,
                            reservation.source.toString().uppercase().first().toString(),
                            resources.getDimension(R.dimen.abbreviation_mega_text_size),
                            resources.getDimension(R.dimen.margin_16)
                        )
                    )
                    binding.iconImg.colorFilter =
                        PorterDuffColorFilter(
                            ContextCompat.getColor(
                                binding.root.context,
                                R.color.colorPrimary
                            ), PorterDuff.Mode.SRC_ATOP
                        )
                }
            }
        }
    }

    inner class StatusViewHolder(
        private val binding: InputSelectBinding
    ) : ReservationDetailsViewHolder(binding.root) {

        override fun bind() {
            binding.run {
                root.setOnClickListener {
                    statusClickListener?.invoke()
                }
                disabled = reservation.locked || isFreemium
                key = binding.root.context.getString(R.string.status)
                showIcon = true
                icon = reservation.statusIcon
                value = reservation.statusTitle
            }

            binding.iconImg.statusColor(Status.getColor(reservation.status))
        }
    }

    inner class WalkinWaitlistViewHolder(
        private val binding: DoubleToggleListItemBinding
    ) : ReservationDetailsViewHolder(binding.root) {

        override fun bind() {
            binding.run {
                cbToggle1.setOnCheckedChangeListener(walkInCheckedChangeListener)
                cbToggle2.setOnCheckedChangeListener(waitlistCheckedChangeListener)
                key1 = binding.root.context.getString(R.string.walk_in)
                value1 = reservation.walkIn
                key2 = binding.root.context.getString(R.string.waitlist)
                value2 = reservation.status == Status.Value.WAITLIST.code
                enabled = !isFreemium && !reservation.locked
            }
        }
    }

    inner class DateTimeViewHolder(
        private val view: InputSelectStepperView
    ) : ReservationDetailsViewHolder(view) {

        init {
            view.setConflictsAdapter(timesConflictAdapter)
        }

        override fun bind() {
            // Bind Date
            view.binding.inputSelect.apply {
                view.setOnClickListener(dateClickListener)
                disabled = reservation.locked || isFreemium
                key = this.root.context.getString(R.string.date)
                showIcon = false
                hideSeparator = true
                value = reservation.dateS
            }

            // Bind Time
            view.binding.stepperView.apply {
                setDisabled(reservation.locked || isFreemium)
                listener = timeUpdateListener
                setOnClickListener {
                    timeClickListener?.invoke()
                }
                setTitle(context.getString(R.string.time))
                separatorVisibility(false)
                titleAllCaps(false)
                setValue(String.format("%s %s", reservation.timeHourS, reservation.timeMarkerS))
                val timeBlockedConflict = conflictList.filter {
                    it.timeBlocked || it.permission?.name == PermissionName.RESERVATION_TIME.permissionName
                }
                view.showConflictsList(timeBlockedConflict.isNotEmpty())
                timesConflictAdapter.submitList(timeBlockedConflict)
            }
        }
    }

    inner class CoversDurationViewHolder(
        private val view: DoubleStepperView
    ) : ReservationDetailsViewHolder(view) {

        override fun bind() {
            // Bind Covers
            view.binding.stepperViewOne.apply {
                setDisabled(reservation.locked || isFreemium)
                listener = coversUpdateListener
                setOnClickListener {
                    coversClickListener?.invoke()
                }
                setTitle(view.context.getString(R.string.covers))
                setValue(reservation.coversS)
                titleAllCaps(false)
                separatorVisibility(false)
            }

            // Bind Duration
            view.binding.stepperViewTwo.apply {
                setDisabled(reservation.locked || isFreemium)
                listener = durationUpdateListener
                setOnClickListener {
                    durationClickListener?.invoke()
                }
                setTitle(view.context.getString(R.string.duration))
                setValue(reservation.durationS)
                titleAllCaps(false)
                separatorVisibility(false)
            }
        }
    }

    inner class TableViewHolder(
        private val binding: InputSelectArrowBinding
    ) : ReservationDetailsViewHolder(binding.root) {

        init {
            binding.recyclerConflicts.adapter = tablesConflictAdapter
        }

        override fun bind() {
            binding.run {
                cont.setOnClickListener {
                    tableClickListener?.invoke()
                }
                counter = 0
                disabled = reservation.locked || isFreemium
                hint = root.context.getString(R.string.table_hint)
                key = root.context.getString(R.string.table)
                value = reservation.tablesS
                val tableConflicts = conflictList.filterNot {
                    it.timeBlocked || it.permission?.name == PermissionName.RESERVATION_TIME.permissionName
                }

                tablesConflictAdapter.submitList(tableConflicts)

                if (tableConflicts.isNotEmpty()) {
                    binding.recyclerConflicts.setPadding(0, 0, 0, 8.px)
                } else {
                    binding.recyclerConflicts.setPadding(0)
                }

                tvTitle.isAllCaps = false
            }
        }
    }

    inner class WaitTimeViewHolder(
        private val view: StepperView
    ) : ReservationDetailsViewHolder(view) {

        override fun bind() {
            if (!reservation.showWaitQuote) {
                hideView(view)
                return
            }
            showView(view)
            view.setDisabled(reservation.locked || isFreemium)
            view.listener = waitTimeUpdateListener
            view.setOnClickListener {
                waitTimeClickListener?.invoke()
            }
            view.setTitle(view.context.getString(R.string.wait))
            view.setValue(reservation.waitQuoteS)
            view.titleAllCaps(false)
        }
    }

    inner class ReservationNotesViewHolder(
        val view: ExpandableInputRegular
    ) : RecyclerView.ViewHolder(view) {

        fun bind() {
            view.updateView(
                view.context.getString(R.string.notes),
                view.context.getString(R.string.reservation_notes_hint),
                reservation.notes, reservation.locked || isFreemium, notesChangedListener
            )
        }
    }

    inner class ReservationCommentsViewHolder(
        private val binding: InputSelectArrowBinding
    ) : ReservationDetailsViewHolder(binding.root) {

        override fun bind() {
            binding.run {
                root.setOnClickListener {
                    commentsClickListener?.invoke()
                }
                counter = reservation.comments?.size
                disabled = reservation.locked || isFreemium
                hint = binding.root.context.getString(R.string.comments_hint)
                key = binding.root.context.getString(R.string.comments)
                value = reservation.lastComment
                tvTitle.isAllCaps = false
                recyclerConflicts.visibility = View.GONE
            }
        }
    }

    inner class ReservationTakerViewHolder(
        private val binding: InputSelectBinding
    ) : ReservationDetailsViewHolder(binding.root) {

        override fun bind() {
            binding.run {
                if (hideReservationTaker) {
                    hideView(binding.root)
                    return@run
                }
                showView(binding.root)
                root.setOnClickListener {
                    takerClickListener?.invoke()
                }
                disabled = reservation.locked || isFreemium
                hint = binding.root.context.getString(R.string.reservation_by_hint)
                key = binding.root.context.getString(R.string.reservation_by)
                showIcon = false
                value = reservation.createdBy
            }
        }
    }

    inner class ReservationTagsViewHolder(
        val binding: ReservationTagsListItemBinding
    ) : ReservationDetailsViewHolder(binding.root) {

        init {
            binding.reservationTagsList.adapter = reservationTagsAdapter
        }

        override fun bind() {
            binding.run {
                if (tagsAvailable || (reservation.taggings?.isNotEmpty() == true)) {
                    showView(binding.root)
                    reservationTagsAdapter.submitListToAdapter(reservationTaggingsList)
                } else {
                    hideView(binding.root)
                }
            }
        }
    }

    inner class ReservationVoucherViewHolder(
        val binding: ListItemVouchersBinding
    ) : ReservationDetailsViewHolder(binding.root) {

        init {
            binding.rvVoucherAssignments.adapter = vouchersAssignmentsAdapter
        }

        override fun bind() = with(binding) {
            if (!showVouchers) {
                hideView(binding.root)
                return@with
            }
            showView(binding.root)
            binding.rvVoucherAssignments.visibleOrGone =
                !reservation.voucherAssignments.isNullOrEmpty()
            vouchersAssignmentsAdapter.submitList(reservation.voucherAssignments?.toMutableList())
            ivAddVoucher.setOnClickListener {
                vouchersClickListener?.invoke()
            }
        }
    }

    inner class DeleteReservationViewHolder(
        val binding: DeleteReservationListItemBinding
    ) : ReservationDetailsViewHolder(binding.root) {

        override fun bind() {
            binding.run {
                btnDelete.setOnClickListener {
                    deleteReservationClickListener?.invoke()
                }
                btnDelete.showLoading(deleteLoadingInProgress)
                isFreemium = <EMAIL>
                deleteButtonVisible = <EMAIL>
                permissionReservation =
                    <EMAIL>
                executePendingBindings()
            }
        }
    }

    inner class GuestViewHolder(
        val binding: ReservationGuestListItemBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(item: ReservationDetailsItem) = with(binding) {

            if (showNewGuestFlow && permissionGuest.boolValue) {
                if (<EMAIL> == null && <EMAIL>()) {
                    hideView(binding.root)
                    return
                } else {
                    showView(binding.root)
                }
            } else {
                showView(binding.root)
            }

            reservation = <EMAIL>
            isFreemium = <EMAIL>
            isMarketingVisible = <EMAIL>
            permission = <EMAIL>
            posActive = <EMAIL>
            currency = <EMAIL>
            loyaltyActive = <EMAIL>

            btnExpand.setImageResource(if (item.expanded) R.drawable.ic_icon_arrow_up else R.drawable.ic_icon_arrow_down)

            btnWhatsapp.visibleOrGone =
                <EMAIL> != null
                        && <EMAIL>?.phone?.isEmpty() == false
                        && !<EMAIL>(
                    "off"
                )
            btnGoogle.visibleOrGone =
                <EMAIL> != null || !TextUtils.isEmpty(
                    <EMAIL>
                )

            btnExpand.visibleOrGone =
                <EMAIL> != null

            btnPhone.visibleOrGone =
                <EMAIL> != null
                        && <EMAIL>?.phone?.isEmpty() == false

            if (<EMAIL>?.isNotEmpty() == true) {
                guestTagsCont.visibleOrGone = true
                separator4.visibleOrGone = true
                val adapter = TagAdapter(false, {}, {})
                guestTagsList.adapter = adapter
                adapter.submitListToAdapter(guestTaggingsList)
            } else {
                guestTagsCont.visibleOrGone = false
                separator4.visibleOrGone = false
            }

            tvExample.visibleOrGone =
                <EMAIL>?.attributes?.demo == true

            ltvLayout.visibleOrGone = item.expanded
            phoneDivider.visibleOrGone = !item.expanded
            visitsCount.visibleOrGone = !item.expanded

            btnChangeGuest.setOnClickListener {
                guestChangeIconClickListener?.invoke(searchEditText.text.isEmpty())
            }

            searchLayout.setOnClickListener {
                guestSearchLayoutClickListener?.invoke()
            }

            btnPhone.setOnClickListener {
                guestPhoneNumberClickListener?.invoke()
            }

            guestDetails.setOnClickListener {
                guestDetailsClickListener?.invoke()
            }

            phoneNumber.setOnClickListener {
                guestPhoneNumberClickListener?.invoke()
            }

            btnGoogle.setOnClickListener {
                guestGoogleClickListener?.invoke()
            }

            btnWhatsapp.setOnClickListener {
                guestWhatsappClickListener?.invoke()
            }

            btnExpand.setOnClickListener {
                guestTagsChevronClickListener?.invoke()
            }

            marketingOptIn.cbToggle.setOnClickListener(marketingCheckedChangeListener)

            searchEditText.setTextColor(ContextCompat.getColor(root.context, R.color.grey800))
            searchEditText.text = if (<EMAIL> == null) {
                <EMAIL> { !it.isNullOrEmpty() }
                    ?: run {
                        searchEditText.setTextColor(
                            ContextCompat.getColor(
                                root.context,
                                R.color.grey400
                            )
                        )
                        root.context.getString(R.string.guests_search_hint)
                    }
            } else {
                String.format(
                    "%s %s",
                    <EMAIL>?.firstName,
                    <EMAIL>?.lastName
                )
            }

            executePendingBindings()
        }
    }

    inner class SendMessageViewHolder(
        private val binding: ToggleListItemBinding
    ) : ReservationDetailsViewHolder(binding.root) {
        override fun bind() {
            binding.run {
                if (!TextUtils.isEmpty(reservation.id)) {
                    hideView(binding.root)
                    return@run
                }

                if (!arrayOf(
                        Status.Value.CONFIRMED.code,
                        Status.Value.NOT_CONFIRMED.code
                    ).contains(reservation.status)
                ) {
                    hideView(binding.root)
                    return@run
                }

                showView(binding.root)
                enabled = sendMessageEnabled

                cbToggle.setOnCheckedChangeListener(sendMessageCheckedChangeListener)
                value = reservation.sendMessage

                val context = binding.root.context
                val request =
                    if (reservation.status == Status.Value.CONFIRMED.code) context.getString(R.string.send_message_confirmed_label) else context.getString(
                        R.string.send_message_request_label
                    )
                val hint =
                    if (reservation.guest == null) context.getString(R.string.send_message_add_guest_hint) else ""
                val text =
                    binding.root.context.getString(R.string.send_message_label, request, hint)
                key = text
            }
        }
    }

    inner class CustomFieldCheckBoxViewHolder(
        val binding: ToggleListItemBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        init {
            binding.cbToggle.setOnCheckedChangeListener { _, isChecked ->
                val item = getItem(absoluteAdapterPosition).customField
                item?.attributes?.name?.let { name ->
                    customFieldUpdateListener?.invoke(Pair(name, isChecked))
                }
            }
        }

        fun bind(item: CustomField?) = with(binding) {
            key =
                item?.label + if (item?.deleted == true) binding.root.context.getString(R.string.deleted_custom_field_label) else ""
            value = item?.value as? Boolean == true
            enabled = !isFreemium && !reservation.locked
            executePendingBindings()
        }
    }

    inner class CustomFieldInputViewHolder(
        val view: ExpandableInputRegular
    ) : RecyclerView.ViewHolder(view) {

        val listener: TextWatcher = object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun afterTextChanged(s: Editable?) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                val item = getItem(absoluteAdapterPosition).customField
                item?.attributes?.name?.let { name ->
                    customFieldUpdateListener?.invoke(Pair(name, s.toString()))
                }
            }
        }

        fun bind(item: CustomField?) {
            view.updateView(
                item?.label + if (item?.deleted == true) view.context.getString(R.string.deleted_custom_field_label) else "",
                view.context.getString(R.string.custom_field_text_input_hint),
                item?.value?.toString() ?: "",
                isFreemium || reservation.locked,
                listener
            )
        }
    }

    inner class CustomFieldCountableViewHolder(
        val view: StepperView
    ) : RecyclerView.ViewHolder(view) {

        init {
            view.listener = {
                val item = getItem(absoluteAdapterPosition).customField
                customFieldCounterUpdateListener?.invoke(Pair(item?.attributes?.name!!, it))
            }

            view.setOnClickListener {
                val item = getItem(absoluteAdapterPosition).customField
                customFieldCounterClickListener?.invoke(item?.attributes?.name!!)
            }
        }

        fun bind(item: CustomField?) {
            view.setDisabled(isFreemium || reservation.locked)
            view.setTitle(item?.label ?: "")
            view.setValue((item?.value as? Int)?.toString() ?: "0")
            view.titleAllCaps(false)

        }
    }

    inner class CustomFieldMultiViewHolder(
        val binding: ListItemCustomFieldMultiBinding
    ): RecyclerView.ViewHolder(binding.root)  {

        @SuppressLint("SetTextI18n")
        fun bind(item: CustomField?) = with(binding){
            val adapter = CustomFieldMultiAdapter().apply {
                this.selectedOptions = item?.value as? MutableList<String> ?: mutableListOf()
                submitList(item?.attributes?.options)
                selectedListener = {
                    item?.attributes?.name?.let { name ->
                        customFieldUpdateListener?.invoke(Pair(name, it))
                    }
                }
            }
            rvChoices.adapter = adapter
            tvTitle.text = item?.label + if (item?.deleted == true) binding.root.context.getString(R.string.deleted_custom_field_label) else ""
        }
    }

    inner class DividerViewHolder(
        binding: DividerListItemBinding
    ) : ReservationDetailsViewHolder(binding.root) {
        override fun bind() {}
    }

    inner class LoyaltyPointsViewHolder(
        private val binding: InputSelectBinding
    ) : ReservationDetailsViewHolder(binding.root) {

        override fun bind() {
            binding.run {
                if (!loyaltyActive || reservation.guest == null) {
                    hideView(binding.root)
                    return@run
                }
                showView(binding.root)
                root.setOnClickListener {
                    loyaltyClickListener?.invoke()
                }
                disabled = reservation.locked || isFreemium
                key = binding.root.context.getString(R.string.loyalty_points_label)
                showIcon = false
                value = reservation.guest?.attributes?.loyaltyPoints.toString()
            }
        }
    }
}

abstract class ReservationDetailsViewHolder(
    view: View
) : RecyclerView.ViewHolder(view) {
    abstract fun bind()
}

class ReservationDetailsListItemsDiffCallback :
    DiffUtil.ItemCallback<ReservationDetailsItem>() {
    override fun areItemsTheSame(
        oldItem: ReservationDetailsItem,
        newItem: ReservationDetailsItem
    ): Boolean {
        return oldItem.itemType == newItem.itemType
    }

    override fun areContentsTheSame(
        oldItem: ReservationDetailsItem,
        newItem: ReservationDetailsItem
    ): Boolean {
        if (oldItem.customField != null && newItem.customField != null) {
            return oldItem.customField == newItem.customField
        }
        return oldItem.expanded == newItem.expanded
    }
}