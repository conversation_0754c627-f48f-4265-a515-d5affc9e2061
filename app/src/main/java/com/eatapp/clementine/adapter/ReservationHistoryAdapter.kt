package com.eatapp.clementine.adapter

import android.graphics.Color
import android.graphics.PorterDuff
import android.graphics.PorterDuffColorFilter
import android.graphics.drawable.Drawable
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.eatapp.clementine.R
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.network.response.reservation.StatusHistoryModel
import com.eatapp.clementine.databinding.ListItemHistoryDividerBinding
import com.eatapp.clementine.databinding.ListItemHistoryItemBinding
import com.eatapp.clementine.internal.BookingSource
import com.eatapp.clementine.internal.Status
import com.eatapp.clementine.internal.TextDrawable
import com.eatapp.clementine.internal.formatISO8601Timezone
import com.eatapp.clementine.internal.setIcon
import com.eatapp.clementine.internal.statusColor


class ReservationHistoryAdapter: ListAdapter<Any?, RecyclerView.ViewHolder>(
    HistoryItemDiffCallback()
) {

    lateinit var reservation: Reservation

    companion object {
        private const val CREATED_AT = "created_at"
        private const val ITEM_TYPE_HISTORY = 998
        private const val ITEM_TYPE_DIVIDER = 990
    }

    fun createList(reservation: Reservation) {
        this.reservation = reservation

        val list: MutableList<Any?> = mutableListOf()

        list.add(StatusHistoryModel(status = CREATED_AT, timestamp = reservation.createdAt?.formatISO8601Timezone()))
        list.add(null)

        reservation.statusHistory?.forEach {
            list.add(it)
            list.add(null)
        }

        list.removeAt(list.count()-1)
        (list.last() as? StatusHistoryModel)?.last = true

        submitList(list)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {

        return if (viewType == ITEM_TYPE_HISTORY) {
            HistoryItemViewHolder(
                ListItemHistoryItemBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
            )
        } else {
            DividerItemViewHolder(
                ListItemHistoryDividerBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
            )
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (holder is HistoryItemViewHolder) {
            holder.bind(getItem(position) as StatusHistoryModel)
        }
    }

    override fun getItemViewType(position: Int): Int {
        return if (getItem(position) is StatusHistoryModel) {
            ITEM_TYPE_HISTORY
        } else {
            ITEM_TYPE_DIVIDER
        }
    }

    inner class HistoryItemViewHolder(
        private val binding: ListItemHistoryItemBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: StatusHistoryModel) {
            binding.item = item

            if (item.status == CREATED_AT) {
                binding.title.text = binding.title.resources.getString(R.string.reservation_created)

                if (reservation.source != null && reservation.source?.isBlank() == false
                    && reservation.source != binding.root.resources.getString(R.string.in_house_source)
                    && reservation.source != Status.Value.WAITLIST.code) {

                    binding.date.text = String.format("%s, %s", item.date, reservation.source?.replaceFirstChar { it.toString().uppercase() })

                    val source = BookingSource.sources?.firstOrNull { it.name == reservation.source }

                    source?.logo?.let {
                        Glide.with(binding.root)
                            .load(it)
                            .listener(object : RequestListener<Drawable> {
                                override fun onResourceReady(
                                    resource: Drawable, model: Any, target: Target<Drawable>?,
                                    dataSource: DataSource, isFirstResource: Boolean
                                ): Boolean { return false }

                                override fun onLoadFailed(
                                    e: GlideException?, model: Any?, target: Target<Drawable>, isFirstResource: Boolean
                                ): Boolean {
                                    val resources = binding.icon.resources
                                    binding.icon.setImageDrawable(
                                        TextDrawable(
                                            resources, binding.icon.context,
                                            reservation.source.toString().uppercase().first().toString(), resources.getDimension(R.dimen.abbreviation_mega_text_size),
                                            resources.getDimension(R.dimen.margin_16)))
                                    binding.icon.colorFilter =
                                        PorterDuffColorFilter(Color.parseColor("#128849"), PorterDuff.Mode.SRC_ATOP)
                                    return true
                                }
                            })
                            .into(binding.icon)
                    } ?: run {
                        val resources = binding.icon.resources
                        binding.icon.setImageDrawable(
                            TextDrawable(
                                resources,
                                binding.icon.context,
                                reservation.source.toString().uppercase().first().toString(),
                                resources.getDimension(R.dimen.abbreviation_mega_text_size),
                                resources.getDimension(R.dimen.margin_16)))
                        binding.icon.colorFilter =
                            PorterDuffColorFilter(Color.parseColor("#128849"), PorterDuff.Mode.SRC_ATOP)
                    }
                } else {
                    binding.date.text = String.format("%s, %s", item.date, binding.title.resources.getString(R.string.in_house))
                    binding.icon.setIcon(R.drawable.ic_icon_restaurant)
                    binding.icon.statusColor(binding.title.resources.getColor(R.color.grey800))
                }

            } else {
                binding.title.text = item.statusTitle
                binding.date.text = item.date
                binding.icon.setIcon(item.statusIcon)
                binding.icon.statusColor(item.statusColor)
            }
        }
    }

    inner class DividerItemViewHolder(
        private val binding: ListItemHistoryDividerBinding
    ) : RecyclerView.ViewHolder(binding.root)
}

private class HistoryItemDiffCallback : DiffUtil.ItemCallback<Any?>() {

    override fun areItemsTheSame(
        oldItem: Any,
        newItem: Any
    ): Boolean {
        return (oldItem as? StatusHistoryModel)?.status == (newItem as? StatusHistoryModel)?.status
    }

    override fun areContentsTheSame(
        oldItem: Any,
        newItem: Any
    ): Boolean {
        return (oldItem as? StatusHistoryModel) == (newItem as? StatusHistoryModel)
    }
}