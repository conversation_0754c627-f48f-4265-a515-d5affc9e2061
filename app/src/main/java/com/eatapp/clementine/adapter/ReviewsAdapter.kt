package com.eatapp.clementine.adapter

import android.os.Handler
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.databinding.ListItemReviewBinding


class ReviewsAdapter(val listener: (Reservation) -> Unit)
    : ListAdapter<Reservation, ReviewsAdapter.ItemViewHolder>(ReviewsItemDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ItemViewHolder {

        return ItemViewHolder(ListItemReviewBinding.inflate(
            LayoutInflater.from(parent.context), parent, false))

    }

    override fun onBindViewHolder(holder: ItemViewHolder, position: Int) {

        val item = getItem(position)
        holder.bind(createOnClickListener(item), createReadMoreOnClickListener(position), item)
    }

    private fun createOnClickListener(reservation: Reservation): View.OnClickListener {
        return View.OnClickListener {
            listener(reservation)
        }
    }

    private fun createReadMoreOnClickListener(position: Int): View.OnClickListener {
        return View.OnClickListener {
            getItem(position).review?.isExpanded = getItem(position).review?.isExpanded?.not() ?: false
            notifyItemChanged(position)
        }
    }

    class ItemViewHolder(
        private val binding: ListItemReviewBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(l: View.OnClickListener, r: View.OnClickListener, i: Reservation) {
            binding.apply {
                clickListener = l
                readMoreClickListener = r
                reservation = i
                executePendingBindings()
            }

            Handler().postDelayed({
                val lineCount = binding.comment.layout?.lineCount
                val ellipsisCount = binding.comment.layout?.getEllipsisCount((lineCount ?: 0) - 1)
                if (ellipsisCount == null || (ellipsisCount == 0 && (lineCount ?: 0) <= 3)) {
                    binding.readMore.visibility = View.GONE
                }
            }, 10)
        }
    }
}

private class ReviewsItemDiffCallback : DiffUtil.ItemCallback<Reservation>() {

    override fun areItemsTheSame(oldItem: Reservation, newItem: Reservation): Boolean {
        return oldItem.id == newItem.id
    }

    override fun areContentsTheSame(oldItem: Reservation, newItem: Reservation): Boolean {
        return oldItem == newItem
    }
}