package com.eatapp.clementine.adapter

import android.graphics.PorterDuff
import android.os.Handler
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.view.updateLayoutParams
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.eatapp.clementine.R
import com.eatapp.clementine.databinding.ListHeaderItemBinding
import com.eatapp.clementine.databinding.ListItemSelectorBinding
import com.eatapp.clementine.internal.SelectorItem
import com.eatapp.clementine.internal.px
import com.jay.widget.StickyHeaders


class SelectorAdapter(
    private val singleSelection: Boolean,
    private val centerItemsHorizontally: Boolean = false,
    private val allowDeselect: Boolean = false) :
    ListAdapter<SelectorItem, RecyclerView.ViewHolder>(SelectorItemDiffCallback()), StickyHeaders {

    var dismiss: ((SelectorItem?) -> Unit)? = null
    var selectionListener: ((item: SelectorItem)->Unit)? = null

    enum class ItemType {
        ITEM,
        HEADER
    }

    companion object {
        val HEADER = ItemType.HEADER.ordinal
        val GUEST = ItemType.ITEM.ordinal
    }

    override fun isStickyHeader(position: Int): Boolean {
        return getItem(position).isHeader
    }

    override fun getItemViewType(position: Int): Int {
        return if (getItem(position).isHeader) {
            HEADER
        } else {
            GUEST
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {

        return if (viewType == HEADER) {
            HeaderViewHolder(
                ListHeaderItemBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
            )
        } else {
            ItemViewHolder(
                ListItemSelectorBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                ), centerItemsHorizontally
            )
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {

        val item = getItem(position)

        when (item.isHeader) {
            true -> (holder as HeaderViewHolder).bind(item)
            false -> (holder as ItemViewHolder).bind(
                createOnClickListener(item, position),
                singleSelection,
                item
            )
        }
    }

    private fun createOnClickListener(tag: SelectorItem, position: Int): View.OnClickListener {
        return View.OnClickListener {

            if (tag.isDisabled) return@OnClickListener

            if (tag.isSelected && allowDeselect) {
                tag.isSelected = false
                notifyItemChanged(position)
                Handler().postDelayed({ dismiss?.invoke(null) }, 500)
                return@OnClickListener
            }

            if (singleSelection) {
                for (i in 0 until itemCount) {
                    if (getItem(i).isSelected) {
                        getItem(i).isSelected = false
                        notifyItemChanged(i)
                    }
                }
            }

            tag.isSelected = !tag.isSelected
            notifyItemChanged(position)

            if (singleSelection) Handler().postDelayed({ dismiss?.invoke(tag) }, 500)

            selectionListener?.invoke(tag)
        }
    }

    class ItemViewHolder(
        private val binding: ListItemSelectorBinding,
        private val centerItemsHorizontally: Boolean
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(l: View.OnClickListener, s: Boolean, i: SelectorItem?) {
            binding.apply {
                clickListener = l
                radio = s
                item = i

                item?.color?.let {
                    if (it != 0) binding.icon.setColorFilter(it, PorterDuff.Mode.SRC_IN)
                }

                if (centerItemsHorizontally) {
                    title.updateLayoutParams<ConstraintLayout.LayoutParams> {
                        startToStart = binding.innerContainer.id
                        endToEnd = binding.innerContainer.id
                        endToStart = ConstraintLayout.LayoutParams.UNSET
                        startToEnd = ConstraintLayout.LayoutParams.UNSET
                        width = ViewGroup.LayoutParams.WRAP_CONTENT
                        marginStart = 0
                        marginEnd = 0
                    }

                    imageView5.updateLayoutParams<ConstraintLayout.LayoutParams> {
                        startToEnd = binding.title.id
                        endToEnd = ConstraintLayout.LayoutParams.UNSET
                        marginStart = 16.px
                    }

                    binding.container.setBackgroundColor(
                        if (item?.isSelected == true) ContextCompat.getColor(
                            this.container.context,
                            R.color.green50
                        ) else ContextCompat.getColor(this.container.context, R.color.white)
                    )
                }

                i?.imageUrl?.let {
                    Glide.with(binding.root.context)
                        .load(it)
                        .into(icon)
                }

                executePendingBindings()
            }
        }
    }

    class HeaderViewHolder(
        val binding: ListHeaderItemBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        private val icExpand = binding.expandIcon
        private val heIcon = binding.headerIcon

        fun bind(item: SelectorItem) {
            binding.apply {
                title = item.name
                showSeparator = false
                icExpand.visibility = View.GONE
                heIcon.visibility = View.GONE
                itemView.setPadding(
                    itemView.resources.getDimension(R.dimen.toolbar_icon_padding).toInt(), 0, 0, 0
                )
                executePendingBindings()
            }
        }
    }
}

private class SelectorItemDiffCallback : DiffUtil.ItemCallback<SelectorItem>() {

    override fun areItemsTheSame(oldItem: SelectorItem, newItem: SelectorItem): Boolean {
        return oldItem.name == newItem.name
    }

    override fun areContentsTheSame(oldItem: SelectorItem, newItem: SelectorItem): Boolean {
        return oldItem == newItem
    }
}