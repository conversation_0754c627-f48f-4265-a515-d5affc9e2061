package com.eatapp.clementine.adapter

import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.eatapp.clementine.data.network.response.server.Server
import com.eatapp.clementine.databinding.ListItemServerBinding


class ServersAdapter(val listener: (Server) -> Unit)
    : ListAdapter<Server, ServersAdapter.ItemViewHolder>(ServersItemDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ItemViewHolder {

        return ItemViewHolder(ListItemServerBinding.inflate(
            LayoutInflater.from(parent.context), parent, false))

    }

    override fun onBindViewHolder(holder: ItemViewHolder, position: Int) {

        val item = getItem(position)
        holder.bind(createOnClickListener(item, position), item)
    }

    private fun createOnClickListener(server: Server, position: Int): View.OnClickListener {
        return View.OnClickListener {
            listener(server)
        }
    }

    class ItemViewHolder(
        private val binding: ListItemServerBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(l: View.OnClickListener, i: Server) {
            binding.apply {
                clickListener = l
                server = i
                executePendingBindings()
            }

            binding.color.setBackgroundColor(Color.parseColor(i.color))
        }

    }
}

private class ServersItemDiffCallback : DiffUtil.ItemCallback<Server>() {

    override fun areItemsTheSame(oldItem: Server, newItem: Server): Boolean {
        return oldItem.id == newItem.id
    }

    override fun areContentsTheSame(oldItem: Server, newItem: Server): Boolean {
        return oldItem == newItem
    }
}