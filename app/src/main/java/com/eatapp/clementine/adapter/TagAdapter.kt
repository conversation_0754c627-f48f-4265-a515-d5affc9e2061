package com.eatapp.clementine.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.graphics.toColorInt
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.eatapp.clementine.R
import com.eatapp.clementine.data.network.response.tagging.Tagging
import com.eatapp.clementine.databinding.ListItemTagBinding
import com.eatapp.clementine.internal.SelectorItem
import com.eatapp.clementine.internal.blendColors

typealias AddTagListener = () -> Unit
typealias RemoveTagListener = (SelectorItem) -> Unit


class TagAdapter(
    private val editable: Boolean,
    val addTagListener: AddTagListener? = null,
    val removeTagListener: RemoveTagListener? = null
) : ListAdapter<SelectorItem, TagAdapter.ViewHolder>(TagDiffCallback()) {

    private var list: MutableList<SelectorItem>? = null

    fun submitListToAdapter(tags: MutableList<SelectorItem>?) {
        this.list = tags
        submitList(this.list)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {

        return ViewHolder(ListItemTagBinding.inflate(
            LayoutInflater.from(parent.context), parent, false))
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {

        val tag = getItem(position)
        holder.bind(createOnClickListener(tag), tag, editable, removeTagListener)
    }

    private fun createOnClickListener(tag: SelectorItem): View.OnClickListener {
        return View.OnClickListener {

            if (!editable) return@OnClickListener

            if (tag.isAddItem) {
                addTagListener?.invoke()
                return@OnClickListener
            }
        }
    }

    class ViewHolder(
        private val binding: ListItemTagBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(
            listener: View.OnClickListener,
            item: SelectorItem,
            e: Boolean,
            removeTagListener: RemoveTagListener?
        ) {
            binding.apply {
                clickListener = listener
                tag = item
                editable = e
                executePendingBindings()
                iconRemoveTag.setOnClickListener{
                    removeTagListener?.invoke(item)
                }
            }

            (item.value as? Tagging)?.let {
                val color = (it.color ?: "#808080").toColorInt()

                val borderDrawable = binding.main.background.mutate()
                borderDrawable.setTint(color and 0x73FFFFFF) // 45% alpha
                binding.main.background = borderDrawable
                binding.tagBcg.background = borderDrawable

                val textColor = blendColors(color, ContextCompat.getColor(binding.root.context, R.color.black), 0.4f)
                binding.tagName.setTextColor(textColor)
            }
        }
    }
}

private class TagDiffCallback : DiffUtil.ItemCallback<SelectorItem>() {

    override fun areItemsTheSame(oldItem: SelectorItem, newItem: SelectorItem): Boolean {
        return oldItem.name == newItem.name
    }

    override fun areContentsTheSame(oldItem: SelectorItem, newItem: SelectorItem): Boolean {
        return oldItem == newItem
    }
}