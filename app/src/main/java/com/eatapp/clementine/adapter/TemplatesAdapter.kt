package com.eatapp.clementine.adapter

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.WebChromeClient
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.eatapp.clementine.data.network.response.message.ChannelType
import com.eatapp.clementine.data.network.response.templates.Template
import com.eatapp.clementine.databinding.ItemHeaderBinding
import com.eatapp.clementine.databinding.ListHeaderItemBinding
import com.eatapp.clementine.databinding.ListItemTemplateBinding
import com.jay.widget.StickyHeaders

class TemplatesAdapter(
    private val sendTemplateListener: (Template) -> Unit
) : ListAdapter<Any, RecyclerView.ViewHolder>(TemplateDiffCallback()), StickyHeaders {

    enum class ItemType {
        TEMPLATE,
        HEADER
    }

    companion object {
        val HEADER = ItemType.HEADER.ordinal
        val TEMPLATE = ItemType.TEMPLATE.ordinal
    }

    override fun isStickyHeader(position: Int): Boolean {
        if (position < 0 || position > currentList.size) {
            return false
        }
        return getItem(position) is String
    }

    override fun getItemViewType(position: Int): Int {
        return if (getItem(position) is String) {
            HEADER
        } else {
            TEMPLATE
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return if (viewType == HEADER) {
            HeaderViewHolder(
                ItemHeaderBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
            )
        } else {
            TemplateViewHolder(
                ListItemTemplateBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
            )
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (position < 0) return

        val item = getItem(position)

        when (item) {
            is String -> (holder as HeaderViewHolder).bind(item)
            is Template -> (holder as TemplateViewHolder).bind(sendTemplateListener, item)
        }

        holder.itemView.tag = item
    }

    inner class TemplateViewHolder(
        private val binding: ListItemTemplateBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(listener: (Template) -> Unit, t: Template) {
            binding.apply {
                clickListener = View.OnClickListener { listener(t) }
                template = t
                executePendingBindings()
            }

            if (t.channel == ChannelType.EMAIL) {
                updateEmailPreview(t.body)
            } else {
                updateBody(t.body)
            }

            binding.previewButton.setOnClickListener {
                t.previewExpanded = !t.previewExpanded
                binding.emailPreview.visibility =
                    if (t.previewExpanded) View.VISIBLE else View.GONE
                binding.root.requestLayout()
                binding.root.invalidate()
            }
        }

        private fun updateBody(body: String) {
            binding.emailPreview.visibility = View.GONE
            binding.previewButton.visibility = View.GONE
            binding.body.text = body
        }

        @SuppressLint("SetJavaScriptEnabled")
        private fun updateEmailPreview(body: String?) {
            binding.body.visibility = View.GONE
            binding.previewButton.visibility = View.VISIBLE
            binding.emailPreview.visibility = View.GONE
            binding.emailPreview.settings.javaScriptEnabled = true
            binding.emailPreview.webChromeClient = WebChromeClient()

            if (body == null) return

            val scaledHtml = body.replace(
                "<body", "<body style=\"zoom:70%; margin:0px;\""
            )

            binding.emailPreview.loadDataWithBaseURL(
                "https://www.google.com/",
                scaledHtml,
                "text/html",
                "UTF-8",
                "index.html"
            )
        }
    }

    class HeaderViewHolder(
        val binding: ItemHeaderBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(header: String) {
            binding.apply {
                section = header
                showSeparator = false
                executePendingBindings()
            }
        }
    }
}

private class TemplateDiffCallback : DiffUtil.ItemCallback<Any>() {

    override fun areItemsTheSame(oldItem: Any, newItem: Any): Boolean {
        return when {
            oldItem is String && newItem is String -> oldItem == newItem
            oldItem is Template && newItem is Template -> oldItem.id == newItem.id
            else -> false
        }
    }

    @SuppressLint("DiffUtilEquals")
    override fun areContentsTheSame(oldItem: Any, newItem: Any): Boolean {
        return when {
            oldItem is String && newItem is String -> oldItem == newItem
            oldItem is Template && newItem is Template -> oldItem == newItem
            else -> false
        }
    }
} 