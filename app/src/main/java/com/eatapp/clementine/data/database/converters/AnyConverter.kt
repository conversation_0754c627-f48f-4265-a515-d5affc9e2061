package com.eatapp.clementine.data.database.converters

import androidx.room.TypeConverter
import com.eatapp.clementine.data.network.response.custom_fields.CustomFieldComponent
import com.eatapp.clementine.data.network.response.custom_fields.CustomFieldType
import com.google.gson.Gson

class AnyConverter {
    private val gson = Gson()

    @TypeConverter
    fun fromAny(value: Any?): String? {
        return gson.toJson(value)
    }

    @TypeConverter
    fun toAny(value: String?): Any? {
        return value?.let {
            gson.fromJson(it, Any::class.java)
        }
    }

    @TypeConverter
    fun fromCustomFieldType(value: CustomFieldType?): String? {
        return value?.name
    }

    @TypeConverter
    fun toCustomFieldType(value: String?): CustomFieldType? {
        return value?.let { CustomFieldType.valueOf(it) }
    }

    @TypeConverter
    fun fromCustomFieldComponent(value: CustomFieldComponent?): String? {
        return value?.name
    }

    @TypeConverter
    fun toCustomFieldComponent(value: String?): CustomFieldComponent? {
        return value?.let { CustomFieldComponent.valueOf(it) }
    }
}