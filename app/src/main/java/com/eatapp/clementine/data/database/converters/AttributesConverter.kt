package com.eatapp.clementine.data.database.converters

import androidx.room.TypeConverter
import com.eatapp.clementine.data.network.response.reservation.ReservationAttributes
import com.google.gson.Gson

class AttributesConverter {

    private val gson = Gson()

    @TypeConverter
    fun fromString(value: String?): ReservationAttributes {
        return gson.fromJson(value, ReservationAttributes::class.java)
    }

    @TypeConverter
    fun toString(author: ReservationAttributes): String? {
        return gson.toJson(author)
    }
}