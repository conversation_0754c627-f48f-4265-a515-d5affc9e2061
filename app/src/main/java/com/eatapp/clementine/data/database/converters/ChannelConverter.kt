package com.eatapp.clementine.data.database.converters

import androidx.room.TypeConverter
import com.eatapp.clementine.data.database.entities.ChannelEntity
import com.google.gson.Gson

class ChannelConverter {

    private val gson = Gson()

    @TypeConverter
    fun fromString(value: String?): ChannelEntity? {
        return gson.fromJson(value, ChannelEntity::class.java)
    }

    @TypeConverter
    fun toString(channelEntity: ChannelEntity?): String? {
        return gson.toJson(channelEntity)
    }
}