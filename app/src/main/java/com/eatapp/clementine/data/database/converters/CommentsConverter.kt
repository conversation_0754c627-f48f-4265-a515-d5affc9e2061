package com.eatapp.clementine.data.database.converters

import androidx.room.TypeConverter
import com.eatapp.clementine.data.database.entities.CommentEntity
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type

class CommentsConverter {

    private val gson = Gson()

    @TypeConverter
    fun fromString(value: String): List<CommentEntity>? {
        val listType: Type = object : TypeToken<List<CommentEntity>?>() {}.type
        return gson.fromJson(value, listType)
    }

    @TypeConverter
    fun toString(commentEntities: List<CommentEntity>?): String {
        return gson.toJson(commentEntities)
    }
}