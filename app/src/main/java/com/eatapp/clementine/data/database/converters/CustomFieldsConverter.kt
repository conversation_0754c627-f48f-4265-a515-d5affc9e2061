package com.eatapp.clementine.data.database.converters

import androidx.room.TypeConverter
import com.google.common.reflect.TypeToken
import com.google.gson.GsonBuilder

class CustomFieldsConverter {

    private val gson = GsonBuilder().setObjectToNumberStrategy {
        // Read the JSON number as string to inspect its format
        val numberAsString = it.nextString()

        if (numberAsString.contains('.')) numberAsString.toDouble()
        else numberAsString.toLong()
    }.serializeNulls()
        .create()

    @TypeConverter
    fun fromMap(map: Map<String, Any>?): String {
        return gson.toJson(map)
    }

    @TypeConverter
    fun toMap(mapString: String?): Map<String, Any?>? {
        return gson.fromJson(mapString, object : TypeToken<Map<String, Any?>>() {}.type)
    }
}