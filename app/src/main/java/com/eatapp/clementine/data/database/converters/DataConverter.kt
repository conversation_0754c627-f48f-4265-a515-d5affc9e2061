package com.eatapp.clementine.data.database.converters

import androidx.room.TypeConverter
import com.eatapp.clementine.data.network.response.Data
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type


class DataConverter {

    private val gson = Gson()

    @TypeConverter
    fun fromString(value: String): Data? {
        val listType: Type = object : TypeToken<Data?>() {}.type
        return gson.fromJson(value, listType)
    }

    @TypeConverter
    fun toString(tables: Data?): String {
        return gson.toJson(tables)
    }
}