package com.eatapp.clementine.data.database.converters

import androidx.room.TypeConverter
import com.eatapp.clementine.data.network.response.DataMultiple
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type


class DataMultipleConverter {

    private val gson = Gson()

    @TypeConverter
    fun fromString(value: String): DataMultiple? {
        val listType: Type = object : TypeToken<DataMultiple?>() {}.type
        return gson.fromJson(value, listType)
    }

    @TypeConverter
    fun toString(tables: DataMultiple?): String {
        return gson.toJson(tables)
    }
}