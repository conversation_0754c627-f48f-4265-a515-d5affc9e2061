package com.eatapp.clementine.data.database.converters

import androidx.room.TypeConverter
import com.eatapp.clementine.data.network.response.DataSingle
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type


class DataSingleConverter {

    private val gson = Gson()

    @TypeConverter
    fun fromString(value: String): DataSingle? {
        val listType: Type = object : TypeToken<DataSingle?>() {}.type
        return gson.fromJson(value, listType)
    }

    @TypeConverter
    fun toString(tables: DataSingle?): String {
        return gson.toJson(tables)
    }
}