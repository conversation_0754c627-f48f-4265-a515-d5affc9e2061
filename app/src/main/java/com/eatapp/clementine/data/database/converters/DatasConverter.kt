package com.eatapp.clementine.data.database.converters

import androidx.room.TypeConverter
import com.eatapp.clementine.data.network.response.Data
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type


class DatasConverter {

    private val gson = Gson()

    @TypeConverter
    fun fromString(value: String): List<Data>? {
        val listType: Type = object : TypeToken<List<Data>?>() {}.type
        return gson.fromJson(value, listType)
    }

    @TypeConverter
    fun toString(tables: List<Data>?): String {
        return gson.toJson(tables)
    }
}