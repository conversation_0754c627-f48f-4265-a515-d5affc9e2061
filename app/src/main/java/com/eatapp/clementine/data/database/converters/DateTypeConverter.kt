package com.eatapp.clementine.data.database.converters

import androidx.room.TypeConverter
import java.util.Date

class DateTypeConverter {

    @TypeConverter
    fun fromTimestamp(timestamp: Long?): Date? {
        timestamp?.let {
            return Date(timestamp)
        }
        return null
    }

    @TypeConverter
    fun dateToTimestamp(date: Date?): Long? {
        return date?.time
    }
}