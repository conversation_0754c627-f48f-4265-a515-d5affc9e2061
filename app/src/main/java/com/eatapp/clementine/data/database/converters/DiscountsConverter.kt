package com.eatapp.clementine.data.database.converters

import androidx.room.TypeConverter
import com.eatapp.clementine.data.network.response.pos.Discount
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type

class DiscountsConverter {

    private val gson = Gson()

    @TypeConverter
    fun fromString(value: String): List<Discount>? {
        val listType: Type = object : TypeToken<List<Discount>?>() {}.type
        return gson.fromJson(value, listType)
    }

    @TypeConverter
    fun toString(discounts: List<Discount>?): String {
        return gson.toJson(discounts)
    }
}