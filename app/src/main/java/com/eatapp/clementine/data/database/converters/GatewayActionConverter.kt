package com.eatapp.clementine.data.database.converters

import androidx.room.TypeConverter
import com.eatapp.clementine.data.network.response.payment.GatewayAction
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type

class GatewayActionConverter {

    private val gson = Gson()

    @TypeConverter
    fun fromString(value: String): List<GatewayAction>? {
        val listType: Type = object : TypeToken<List<GatewayAction>?>() {}.type
        return gson.fromJson(value, listType)
    }

    @TypeConverter
    fun toString(gatewayActions: List<GatewayAction>?): String {
        return gson.toJson(gatewayActions)
    }
}