package com.eatapp.clementine.data.database.converters

import androidx.room.TypeConverter
import com.eatapp.clementine.data.network.response.pos.MenuItem
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type

class MenuItemConverter {

    private val gson = Gson()

    @TypeConverter
    fun fromString(value: String): List<MenuItem>? {
        val listType: Type = object : TypeToken<List<MenuItem>?>() {}.type
        return gson.fromJson(value, listType)
    }

    @TypeConverter
    fun toString(menuItems: List<MenuItem>?): String {
        return gson.toJson(menuItems)
    }
}