package com.eatapp.clementine.data.database.converters

import androidx.room.TypeConverter
import com.eatapp.clementine.data.database.entities.OrderEntity
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type

class MintItemsConverter {

    private val gson = Gson()

    @TypeConverter
    fun fromString(value: String): List<OrderEntity.MintOrderItem>? {
        val listType: Type = object : TypeToken<List<OrderEntity.MintOrderItem>?>() {}.type
        return gson.fromJson(value, listType)
    }

    @TypeConverter
    fun toString(menuItems: List<OrderEntity.MintOrderItem>?): String {
        return gson.toJson(menuItems)
    }
}