package com.eatapp.clementine.data.database.converters

import androidx.room.TypeConverter
import com.eatapp.clementine.data.network.response.payment.Payment
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type


class PaymentsConverter {

    private val gson = Gson()

    @TypeConverter
    fun fromString(value: String): List<Payment>? {
        val listType: Type = object : TypeToken<List<Payment>?>() {}.type
        return gson.fromJson(value, listType)
    }

    @TypeConverter
    fun toString(payments: List<Payment>?): String {
        return gson.toJson(payments)
    }
}