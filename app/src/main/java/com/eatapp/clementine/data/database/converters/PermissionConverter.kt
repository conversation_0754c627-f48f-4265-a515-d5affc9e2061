package com.eatapp.clementine.data.database.converters

import androidx.room.TypeConverter
import com.eatapp.clementine.data.network.response.user.Permission
import com.google.gson.Gson

class PermissionConverter {

    private val gson = Gson()

    @TypeConverter
    fun fromString(value: String?): Permission? {
        return gson.fromJson(value, Permission::class.java)
    }

    @TypeConverter
    fun toString(permission: Permission?): String? {
        return gson.toJson(permission)
    }
}