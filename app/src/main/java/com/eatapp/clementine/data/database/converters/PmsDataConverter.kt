package com.eatapp.clementine.data.database.converters

import androidx.room.TypeConverter
import com.eatapp.clementine.data.database.entities.PmsData
import com.google.gson.Gson

class PmsDataConverter {
    private val gson = Gson()

    @TypeConverter
    fun fromString(value: String?): PmsData? {
        return gson.fromJson(value, PmsData::class.java)
    }

    @TypeConverter
    fun toString(pmsData: PmsData?): String? {
        return gson.toJson(pmsData)
    }
}