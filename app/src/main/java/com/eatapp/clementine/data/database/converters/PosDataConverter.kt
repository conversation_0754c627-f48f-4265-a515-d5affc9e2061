package com.eatapp.clementine.data.database.converters

import androidx.room.TypeConverter
import com.eatapp.clementine.data.database.entities.PmsData
import com.eatapp.clementine.data.network.response.guest.GuestPosData
import com.google.gson.Gson

class PosDataConverter {
    private val gson = Gson()

    @TypeConverter
    fun fromString(value: String?): GuestPosData? {
        return gson.fromJson(value, GuestPosData::class.java)
    }

    @TypeConverter
    fun toString(pmsData: GuestPosData?): String? {
        return gson.toJson(pmsData)
    }
}