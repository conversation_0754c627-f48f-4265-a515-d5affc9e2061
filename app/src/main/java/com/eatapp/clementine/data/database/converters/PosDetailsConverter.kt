package com.eatapp.clementine.data.database.converters

import androidx.room.TypeConverter
import com.eatapp.clementine.data.database.entities.PosDetailsEntity
import com.google.gson.GsonBuilder

class PosDetailsConverter {
    private val gson = GsonBuilder()
        .setDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
        .create()

    @TypeConverter
    fun fromString(value: String): PosDetailsEntity? {
        return gson.fromJson(value, PosDetailsEntity::class.java)
    }

    @TypeConverter
    fun toString(details: PosDetailsEntity?): String {
        return gson.toJson(details)
    }
}