package com.eatapp.clementine.data.database.converters

import androidx.room.TypeConverter
import com.eatapp.clementine.data.network.response.reservation.ReservationRelationships
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type


class RelationshipsConverter {

    private val gson = Gson()

    @TypeConverter
    fun fromString(value: String): ReservationRelationships? {
        val listType: Type = object : TypeToken<ReservationRelationships?>() {}.type
        return gson.fromJson(value, listType)
    }

    @TypeConverter
    fun toString(tables: ReservationRelationships?): String {
        return gson.toJson(tables)
    }
}