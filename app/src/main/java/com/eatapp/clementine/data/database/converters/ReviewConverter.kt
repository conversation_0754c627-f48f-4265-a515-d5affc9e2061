package com.eatapp.clementine.data.database.converters

import androidx.room.TypeConverter
import com.eatapp.clementine.data.network.response.reservation.ReviewModel
import com.google.gson.Gson

class ReviewConverter {

    private val gson = Gson()

    @TypeConverter
    fun fromString(value: String?): ReviewModel? {
        return gson.fromJson(value, ReviewModel::class.java)
    }

    @TypeConverter
    fun toString(reviewModel: ReviewModel?): String? {
        return gson.toJson(reviewModel)
    }
}