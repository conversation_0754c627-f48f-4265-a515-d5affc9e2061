package com.eatapp.clementine.data.database.converters

import androidx.room.TypeConverter
import com.eatapp.clementine.data.database.entities.StatusHistoryEntity
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type

class StatusHistoryConverter {

    private val gson = Gson()

    @TypeConverter
    fun fromString(value: String): List<StatusHistoryEntity>? {
        val listType: Type = object : TypeToken<List<StatusHistoryEntity>?>() {}.type
        return gson.fromJson(value, listType)
    }

    @TypeConverter
    fun toString(statuses: List<StatusHistoryEntity>?): String {
        return gson.toJson(statuses)
    }
}