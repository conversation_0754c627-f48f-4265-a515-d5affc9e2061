package com.eatapp.clementine.data.database.converters

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type

class StringListConverter {

    private val gson = Gson()

    @TypeConverter
    fun fromString(value: String): List<String>? {
        val listType: Type = object : TypeToken<List<String>?>() {}.type
        return gson.fromJson(value, listType)
    }

    @TypeConverter
    fun toString(strings: List<String>?): String {
        return gson.toJson(strings)
    }
}