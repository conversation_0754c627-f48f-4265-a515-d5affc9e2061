package com.eatapp.clementine.data.database.converters

import androidx.room.TypeConverter
import com.eatapp.clementine.data.database.entities.TagEntity
import com.google.gson.Gson

class TagCategoryConverter {

    private val gson = Gson()

    @TypeConverter
    fun fromString(value: String?): TagEntity.TagCategory? {
        return gson.fromJson(value, TagEntity.TagCategory::class.java)
    }

    @TypeConverter
    fun toString(reviewModel: TagEntity.TagCategory?): String? {
        return gson.toJson(reviewModel)
    }
}