package com.eatapp.clementine.data.database.converters

import androidx.room.TypeConverter
import com.eatapp.clementine.data.database.entities.TaggingEntity
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type


class TaggingsConverter {

    private val gson = Gson()

    @TypeConverter
    fun fromString(value: String): List<TaggingEntity>? {
        val listType: Type = object : TypeToken<List<TaggingEntity>?>() {}.type
        return gson.fromJson(value, listType)
    }

    @TypeConverter
    fun toString(taggingEntities: List<TaggingEntity>?): String {
        val filteredTaggings = taggingEntities?.filter { it.id != null }
        return gson.toJson(filteredTaggings)
    }
}