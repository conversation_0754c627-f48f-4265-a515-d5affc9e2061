package com.eatapp.clementine.data.database.converters

import androidx.room.TypeConverter
import com.eatapp.clementine.data.network.response.templates.TemplateComponents
import com.google.gson.Gson

class TemplateComponentsConverter {
    private val gson = Gson()

    @TypeConverter
    fun fromString(value: String): TemplateComponents? {
        return gson.fromJson(value, TemplateComponents::class.java)
    }

    @TypeConverter
    fun toString(templateComponents: TemplateComponents): String {
        return gson.toJson(templateComponents)
    }
}