package com.eatapp.clementine.data.database.converters

import androidx.room.TypeConverter
import com.eatapp.clementine.data.database.entities.VoucherAssignmentEntity
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type


class VoucherAssignmentConverter {

    private val gson = Gson()

    @TypeConverter
    fun fromString(value: String): List<VoucherAssignmentEntity>? {
        val listType: Type = object : TypeToken<List<VoucherAssignmentEntity>?>() {}.type
        return gson.fromJson(value, listType)
    }

    @TypeConverter
    fun toString(voucherAssignmentEntities: List<VoucherAssignmentEntity>?): String {
        return gson.toJson(voucherAssignmentEntities)
    }
}