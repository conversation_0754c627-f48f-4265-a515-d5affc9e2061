package com.eatapp.clementine.data.database.converters

import androidx.room.TypeConverter
import com.eatapp.clementine.data.network.response.vouchers.VoucherType
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

class VoucherTypeConverter {

    private val gson = Gson()

    @TypeConverter
    fun fromVoucherTypeList(value: List<VoucherType>?): String? =
        gson.toJson(value)

    @TypeConverter
    fun toVoucherTypeList(value: String?): List<VoucherType>? {
        val type = object : TypeToken<List<VoucherType>>() {}.type
        return gson.fromJson(value, type)
    }
}