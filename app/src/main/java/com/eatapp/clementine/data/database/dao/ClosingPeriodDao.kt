package com.eatapp.clementine.data.database.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Upsert
import com.eatapp.clementine.data.database.entities.ClosingPeriodEntity
import kotlinx.coroutines.flow.Flow
import java.util.Date
import javax.inject.Singleton

@Singleton
@Dao
interface ClosingPeriodDao {

    @Query("SELECT * FROM closing_periods WHERE :restaurantId = restaurantId AND timeRangeBegin BETWEEN :startDate AND :endDate")
    fun closingPeriodsFlow(restaurantId: String, startDate: Date?, endDate: Date?): Flow<List<ClosingPeriodEntity>>

    @Query("DELETE FROM closing_periods WHERE :restaurantId = restaurantId")
    suspend fun deleteClosingPeriods(restaurantId: String)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun saveClosingPeriods(list: List<ClosingPeriodEntity>)

    @Upsert
    suspend fun updateClosingPeriod(closingPeriodEntity: ClosingPeriodEntity)

    @Delete
    suspend fun deleteClosingPeriod(closingPeriodEntity: ClosingPeriodEntity)

    @Query("DELETE FROM closing_periods WHERE restaurantId = :restaurantId AND id = :entityId")
    suspend fun deleteClosingPeriodById(restaurantId: String, entityId: String)
}