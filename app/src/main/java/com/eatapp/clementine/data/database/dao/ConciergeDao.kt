package com.eatapp.clementine.data.database.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Upsert
import com.eatapp.clementine.data.database.entities.ConciergeEntity
import kotlinx.coroutines.flow.Flow
import javax.inject.Singleton

@Singleton
@Dao
interface ConciergeDao {

    @Query("SELECT * FROM concierges WHERE :restaurantId = restaurantId")
    fun conciergesFlow(restaurantId: String): Flow<List<ConciergeEntity>>

    @Query("DELETE FROM concierges WHERE :restaurantId = restaurantId")
    suspend fun deleteConcierges(restaurantId: String)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun saveConcierges(list: List<ConciergeEntity>)

    @Upsert
    suspend fun updateConcierge(conciergeEntity: ConciergeEntity)

    @Delete
    suspend fun deleteConcierge(conciergeEntity: ConciergeEntity)
}