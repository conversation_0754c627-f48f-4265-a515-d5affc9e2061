package com.eatapp.clementine.data.database.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.eatapp.clementine.data.database.entities.ConversationEntity
import kotlinx.coroutines.flow.Flow
import javax.inject.Single<PERSON>

@Singleton
@Dao
interface ConversationsDao {

    @Query("DELETE FROM conversations WHERE restaurantId = :restaurantId")
    suspend fun deleteConversations(restaurantId: String)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun saveConversations(conversations: List<ConversationEntity>)

    @Query("SELECT * FROM conversations WHERE :restaurantId = restaurantId")
    fun conversationsFlow(restaurantId: String): Flow<List<ConversationEntity>>
}