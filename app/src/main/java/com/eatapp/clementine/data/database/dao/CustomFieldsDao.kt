package com.eatapp.clementine.data.database.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.eatapp.clementine.data.database.entities.CustomFieldEntity
import com.eatapp.clementine.data.network.response.custom_fields.CustomFieldComponent
import kotlinx.coroutines.flow.Flow
import javax.inject.Singleton

@Singleton
@Dao
interface CustomFieldsDao {

    @Query("DELETE FROM custom_fields WHERE restaurantId = :restaurantId")
    suspend fun deleteCustomFields(restaurantId: String)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun saveCustomFields(customFields: List<CustomFieldEntity>)

    @Query("SELECT * FROM custom_fields WHERE :restaurantId = restaurantId")
    fun customFieldsFlow(restaurantId: String): Flow<List<CustomFieldEntity>>
}