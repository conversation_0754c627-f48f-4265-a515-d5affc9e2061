package com.eatapp.clementine.data.database.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Upsert
import com.eatapp.clementine.data.database.entities.DayNoteEntity
import kotlinx.coroutines.flow.Flow
import java.util.Date
import javax.inject.Singleton

@Singleton
@Dao
interface DayNoteDao {
    @Query("SELECT * FROM day_notes WHERE :restaurantId = restaurantId AND date = :date")
    fun dayNotesFlow(restaurantId: String, date: Date): Flow<List<DayNoteEntity>>

    @Query("DELETE FROM day_notes WHERE :restaurantId = restaurantId")
    suspend fun deleteDayNotes(restaurantId: String)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun saveDayNotes(list: List<DayNoteEntity>)

    @Upsert
    suspend fun updateDayNote(dayNote: DayNoteEntity)

    @Delete
    suspend fun deleteDayNote(dayNote: DayNoteEntity)

    @Query("SELECT * FROM day_notes WHERE :restaurantId = restaurantId AND id = :id")
    suspend fun dayNoteById(id: String, restaurantId: String): DayNoteEntity
}