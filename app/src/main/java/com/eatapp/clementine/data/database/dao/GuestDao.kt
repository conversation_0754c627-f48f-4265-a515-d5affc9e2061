package com.eatapp.clementine.data.database.dao

import androidx.paging.PagingSource
import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Upsert
import com.eatapp.clementine.data.database.entities.GuestEntity
import kotlinx.coroutines.flow.Flow
import javax.inject.Singleton

@Dao
@Singleton
interface GuestDao {

    @Query("SELECT * FROM guests WHERE :restaurantId = restaurantId ORDER BY firstName ASC ")
    fun guestsFlow(restaurantId: String): Flow<List<GuestEntity>>

    @Query("DELETE FROM guests WHERE :restaurantId = restaurantId")
    suspend fun deleteGuests(restaurantId: String)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun saveGuests(list: List<GuestEntity>)

    @Upsert
    suspend fun updateGuest(guestEntity: GuestEntity)

    @Delete
    suspend fun deleteGuest(guestEntity: GuestEntity)

    @Query("DELETE FROM guests WHERE :guestId = guestId")
    suspend fun deleteGuest(guestId: String)

    @Query("SELECT * FROM guests WHERE restaurantId = :restaurantId AND guestId = :guestId")
    suspend fun getGuest(restaurantId: String, guestId: String): GuestEntity

    @Query("SELECT * FROM guests WHERE restaurantId == :restaurantId AND (:query IS NULL OR :query = '' OR LOWER(content) LIKE '%' || LOWER(:query) || '%') ORDER BY firstName ASC")
    fun getPagingGuests(restaurantId: String, query: String?): PagingSource<Int, GuestEntity>

    @Query("SELECT * FROM guests WHERE restaurantId == :restaurantId AND (:query IS NULL OR :query = '' OR LOWER(content) LIKE '%' || LOWER(:query) || '%') ORDER BY firstName ASC")
    suspend fun guests(restaurantId: String, query: String?): List<GuestEntity>

    @Query("SELECT * FROM guests WHERE restaurantId = :restaurantId ORDER BY firstName ASC, guestId ASC LIMIT :limit")
    suspend fun getFirstPageByFirstNameGuestId(restaurantId: String, limit: Int): List<GuestEntity>

    @Query("SELECT * FROM guests WHERE restaurantId = :restaurantId AND (firstName > :lastFirstName OR (firstName = :lastFirstName AND guestId > :lastGuestId)) ORDER BY firstName ASC, guestId ASC LIMIT :limit")
    suspend fun getNextPageByFirstNameGuestId(
        restaurantId: String,
        lastFirstName: String,
        lastGuestId: String,
        limit: Int
    ): List<GuestEntity>

    @Query(
        """
    SELECT * FROM guests WHERE restaurantId == :restaurantId AND (content LIKE '%' || :query || '%')
    ORDER BY guests.firstName ASC, guests.guestId ASC
    LIMIT :limit
"""
    )
    suspend fun getFirstPageFtsByFirstNameGuestId(
        query: String?,
        limit: Int,
        restaurantId: String
    ): List<GuestEntity>

    @Query(
        """
    SELECT * FROM guests WHERE restaurantId == :restaurantId AND (content LIKE '%' || :query || '%')
      AND (guests.firstName > :lastFirstName OR (guests.firstName = :lastFirstName AND guests.guestId > :lastGuestId))
    ORDER BY guests.firstName ASC, guests.guestId ASC
    LIMIT :limit
"""
    )
    suspend fun getNextPageFtsByFirstNameGuestId(
        query: String,
        lastFirstName: String,
        lastGuestId: String,
        limit: Int,
        restaurantId: String
    ): List<GuestEntity>
}