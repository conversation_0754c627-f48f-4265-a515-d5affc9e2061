package com.eatapp.clementine.data.database.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Upsert
import com.eatapp.clementine.data.database.entities.OnlineSeatingShiftEntity
import kotlinx.coroutines.flow.Flow
import javax.inject.Singleton

@Singleton
@Dao
interface OnlineSeatingShiftDao {

    // Shift type is different for sync and rest api ("Shift"/"online_seating_shift"). When switching between modes,
    // shifts get overwritten because OnConflictStrategy.REPLACE is used.
    // This way we fetch all shifts regardless of type
    @Query(
        """
    SELECT * FROM online_seating_shifts
    WHERE restaurantId = :restaurantId
    AND status = :status
    AND type IN (:types)
"""
    )
    fun onlineSeatingShiftsFlow(
        restaurantId: String,
        status: String = "active",
        types: List<String> = listOf("Shift", "online_seating_shift")
    ): Flow<List<OnlineSeatingShiftEntity>>

    @Query("DELETE FROM online_seating_shifts  WHERE :restaurantId = restaurantId")
    suspend fun deleteOnlineSeatingShifts(restaurantId: String)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun saveOnlineSeatingShifts(list: List<OnlineSeatingShiftEntity>)

    @Upsert
    suspend fun updateOnlineSeatingShift(shift: OnlineSeatingShiftEntity)

    @Delete
    suspend fun deleteOnlineSeatingShift(shift: OnlineSeatingShiftEntity)
}