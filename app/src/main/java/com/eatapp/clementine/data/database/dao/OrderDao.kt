package com.eatapp.clementine.data.database.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Upsert
import com.eatapp.clementine.data.database.entities.OrderEntity
import kotlinx.coroutines.flow.Flow
import javax.inject.Singleton

@Dao
@Singleton
interface OrderDao {

    @Query("SELECT * FROM orders  WHERE :restaurantId = restaurantId")
    fun ordersFlow(restaurantId: String): Flow<List<OrderEntity>>

    @Query("DELETE FROM orders  WHERE :restaurantId = restaurantId")
    suspend fun deleteOrders(restaurantId: String)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun saveOrders(list: List<OrderEntity>)

    @Upsert
    suspend fun updateOrder(orderEntity: OrderEntity)

    @Delete
    suspend fun deleteOrder(orderEntity: OrderEntity)
}