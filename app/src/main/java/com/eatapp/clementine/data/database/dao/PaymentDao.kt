package com.eatapp.clementine.data.database.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Upsert
import com.eatapp.clementine.data.database.entities.PaymentEntity
import kotlinx.coroutines.flow.Flow
import javax.inject.Singleton

@Singleton
@Dao
interface PaymentDao {

    @Query("SELECT * FROM payments WHERE :restaurantId = restaurantId AND reservationId = :reservationId")
    fun paymentsFlow(restaurantId: String, reservationId: String): Flow<List<PaymentEntity>>

    @Query("DELETE FROM payments WHERE :restaurantId = restaurantId")
    suspend fun deletePayments(restaurantId: String)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun savePayments(list: List<PaymentEntity>)

    @Upsert
    suspend fun updatePayment(paymentEntity: PaymentEntity)

    @Delete
    suspend fun deletePayment(paymentEntity: PaymentEntity)

    @Query("SELECT * FROM payments WHERE :restaurantId = restaurantId AND id = :paymentId")
    fun paymentById(restaurantId: String, paymentId: String): Flow<PaymentEntity>
}