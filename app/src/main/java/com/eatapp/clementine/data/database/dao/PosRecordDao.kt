package com.eatapp.clementine.data.database.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Upsert
import com.eatapp.clementine.data.database.entities.PosRecordEntity
import java.util.Date
import javax.inject.Singleton

@Singleton
@Dao
interface PosRecordDao {
    @Query("SELECT * FROM pos_records WHERE :restaurantId = restaurantId AND createdAt BETWEEN :startDate AND :endDate")
    suspend fun posRecordsFlow(restaurantId: String, startDate: Date, endDate: Date): List<PosRecordEntity>

    @Query("DELETE FROM pos_records WHERE :restaurantId = restaurantId")
    suspend fun deletePosRecords(restaurantId: String)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun savePosRecords(list: List<PosRecordEntity>)

    @Upsert
    suspend fun updatePosRecord(posRecordEntity: PosRecordEntity)

    @Delete
    suspend fun deletePosRecord(posRecordEntity: PosRecordEntity)

    @Query("SELECT * FROM pos_records WHERE :restaurantId = restaurantId AND id = :recordId")
    suspend fun posRecord(restaurantId: String, recordId: String): PosRecordEntity?
}