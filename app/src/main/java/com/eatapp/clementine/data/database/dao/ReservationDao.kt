package com.eatapp.clementine.data.database.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import androidx.room.Upsert
import com.eatapp.clementine.data.database.entities.ReservationEntity
import com.eatapp.clementine.data.database.entities.ReservationTableCrossRef
import com.eatapp.clementine.data.database.entities.ReservationWithData
import kotlinx.coroutines.flow.Flow
import java.util.Date
import javax.inject.Singleton

@Dao
@Singleton
interface ReservationDao {

    @Query("SELECT * FROM reservations WHERE restaurantId = :restaurantId AND startTime BETWEEN :startDate AND :endDate")
    fun reservationsFlow(
        restaurantId: String,
        startDate: Date?,
        endDate: Date?
    ): Flow<List<ReservationEntity>>

    @Transaction
    @Query("SELECT * FROM reservations WHERE restaurantId = :restaurantId AND reservationId = :reservationId")
    suspend fun getReservation(restaurantId: String, reservationId: String): ReservationWithData

    @Query("SELECT * FROM reservations WHERE restaurantId = :restaurantId AND reservationId = :reservationId")
    suspend fun reservation(restaurantId: String, reservationId: String): ReservationEntity

    @Query("DELETE FROM reservations WHERE restaurantId = :restaurantId AND startTime BETWEEN :startDate AND :endDate")
    suspend fun deleteReservations(restaurantId: String, startDate: Date?, endDate: Date?)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun saveReservations(list: List<ReservationEntity>)

    @Upsert
    suspend fun updateReservation(reservationEntity: ReservationEntity)

    @Delete
    suspend fun deleteReservation(reservationEntity: ReservationEntity)

    @Query("DELETE FROM reservations WHERE reservationId = :reservationId")
    suspend fun deleteReservation(reservationId: String)

    @Upsert
    suspend fun insertReservationWithTables(reservationTableCrossRef: List<ReservationTableCrossRef>)

    @Transaction
    suspend fun updateReservationTables(reservationId: String, newTableIds: List<String>) {
        deleteTablesForReservation(reservationId)

        val newRefs = newTableIds.map { tableId ->
            ReservationTableCrossRef(reservationId = reservationId, tableId = tableId)
        }
        insertReservationWithTables(newRefs)
    }

    //    @Query("DELETE FROM ReservationShiftCrossRef WHERE reservationId = :reservationId")
//    suspend fun deletePreferencesForReservation(reservationId: String)


//    @Upsert
//    suspend fun insertReservationWithPreferences(reservationTableCrossRef: List<ReservationShiftCrossRef>)

    @Transaction
    @Query("SELECT * FROM reservations WHERE restaurantId = :restaurantId AND startTime BETWEEN :startDate AND :endDate")
    fun reservationWithDataFlow(
        restaurantId: String,
        startDate: Date?,
        endDate: Date?
    ): Flow<List<ReservationWithData>>

    @Query("DELETE FROM ReservationTableCrossRef WHERE reservationId = :reservationId")
    suspend fun deleteTablesForReservation(reservationId: String)

//    @Query("DELETE FROM ReservationShiftCrossRef WHERE reservationId = :reservationId")
//    suspend fun deletePreferencesForReservation(reservationId: String)

    @Transaction
    @Query("SELECT * FROM reservations WHERE guestId == :guestId")
    suspend fun reservations(guestId: String): List<ReservationWithData>
}