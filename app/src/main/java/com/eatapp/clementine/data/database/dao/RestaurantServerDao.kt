package com.eatapp.clementine.data.database.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import androidx.room.Upsert
import com.eatapp.clementine.data.database.entities.RestaurantServerEntity
import com.eatapp.clementine.data.database.entities.RestaurantServerWithTables
import kotlinx.coroutines.flow.Flow
import javax.inject.Singleton

@Singleton
@Dao
interface RestaurantServerDao {
    @Transaction
    @Query("SELECT * FROM restaurant_servers WHERE :restaurantId = restaurantId ORDER BY name ASC")
    fun restaurantServersWithTablesFlow(restaurantId: String): Flow<List<RestaurantServerWithTables>>

    @Query("DELETE FROM restaurant_servers WHERE :restaurantId = restaurantId")
    suspend fun deleteRestaurantServers(restaurantId: String)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun saveRestaurantServers(list: List<RestaurantServerEntity>)

    @Upsert
    suspend fun updateRestaurantServer(restaurantServerEntity: RestaurantServerEntity)

    @Delete
    suspend fun deleteRestaurantServer(restaurantServerEntity: RestaurantServerEntity)

    @Transaction
    @Query("SELECT * FROM restaurant_servers WHERE :restaurantId = restaurantId AND serverId = :serverId")
    suspend fun restaurantServerById(restaurantId: String, serverId: String): RestaurantServerWithTables
}