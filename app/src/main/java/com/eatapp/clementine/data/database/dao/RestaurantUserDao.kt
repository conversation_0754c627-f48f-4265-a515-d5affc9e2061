package com.eatapp.clementine.data.database.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Upsert
import com.eatapp.clementine.data.database.entities.RestaurantUserEntity
import kotlinx.coroutines.flow.Flow
import javax.inject.Singleton

@Singleton
@Dao
interface RestaurantUserDao {
    @Query("SELECT * FROM restaurant_users WHERE :restaurantId = restaurantId")
    fun restaurantUsersFlow(restaurantId: String): Flow<List<RestaurantUserEntity>>

    @Query("DELETE FROM restaurant_users WHERE :restaurantId = restaurantId")
    suspend fun deleteRestaurantUsers(restaurantId: String)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun saveRestaurantUsers(list: List<RestaurantUserEntity>)

    @Upsert
    suspend fun updateRestaurantUser(restaurantUserEntity: RestaurantUserEntity)

    @Delete
    suspend fun deleteRestaurantUser(restaurantUserEntity: RestaurantUserEntity)

    @Query("SELECT * FROM restaurant_users WHERE restaurantId = :restaurantId AND taker = 1")
    fun takers(restaurantId: String): Flow<List<RestaurantUserEntity>>
}