package com.eatapp.clementine.data.database.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import androidx.room.Upsert
import com.eatapp.clementine.data.database.entities.RoomEntity
import com.eatapp.clementine.data.database.entities.RoomWithTables
import kotlinx.coroutines.flow.Flow
import javax.inject.Singleton

@Singleton
@Dao
interface RoomDao {

    @Transaction
    @Query("SELECT * FROM rooms WHERE :restaurantId = restaurantId")
    fun roomsWithTablesFlow(restaurantId: String): Flow<List<RoomWithTables>>

    @Query("SELECT * FROM rooms WHERE :restaurantId = restaurantId")
    suspend fun rooms(restaurantId: String): List<RoomWithTables>

    @Query("DELETE FROM rooms WHERE :restaurantId = restaurantId")
    suspend fun deleteRooms(restaurantId: String)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun saveRooms(list: List<RoomEntity>)

    @Upsert
    suspend fun updateRoom(roomEntity: RoomEntity)

    @Delete
    suspend fun deleteRoom(roomEntity: RoomEntity)
}