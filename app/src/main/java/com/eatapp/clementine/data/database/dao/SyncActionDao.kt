package com.eatapp.clementine.data.database.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.eatapp.clementine.data.network.body.SyncAction
import kotlinx.coroutines.flow.Flow
import javax.inject.Singleton

@Singleton
@Dao
interface SyncActionDao {

    @Query("SELECT * FROM sync_actions WHERE :restaurantId = restaurantId")
    fun syncActions(restaurantId: String): Flow<List<SyncAction>>

    @Query("SELECT * FROM sync_actions WHERE restaurantId = :restaurantId AND (state = :inSyncState OR state = :notSyncedState)")
    fun notSyncedActions(
        restaurantId: String,
        inSyncState: SyncAction.State = SyncAction.State.IN_SYNC,
        notSyncedState: SyncAction.State = SyncAction.State.NOT_SYNCED
    ): Flow<List<SyncAction>>

    @Query("UPDATE sync_actions SET state = :newState WHERE (state = :inSyncState OR state = :notSyncedState)")
    suspend fun updateAllNotSyncedActionsToSynced(
        inSyncState: SyncAction.State = SyncAction.State.IN_SYNC,
        notSyncedState: SyncAction.State = SyncAction.State.NOT_SYNCED,
        newState: SyncAction.State = SyncAction.State.SYNCED
    )

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun saveSyncAction(syncAction: SyncAction)

    @Delete
    suspend fun deleteSyncAction(syncAction: SyncAction)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun saveSyncActions(list: List<SyncAction>)
}