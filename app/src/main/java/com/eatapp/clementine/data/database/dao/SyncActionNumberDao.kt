package com.eatapp.clementine.data.database.dao

import androidx.room.Dao
import androidx.room.Query
import androidx.room.Upsert
import com.eatapp.clementine.data.database.entities.SyncActionNumberEntity
import javax.inject.Singleton

@Dao
@Singleton
interface SyncActionNumberDao {

    @Query("""
    SELECT IFNULL((
        SELECT actionNumber 
        FROM action_number 
        WHERE restaurantId = :restaurantId 
        LIMIT 1
    ), :defaultValue)
""")
    suspend fun syncActionNumber(restaurantId: String, defaultValue: Int = 0): Int

    @Upsert
    suspend fun updateActionNumber(actionNumber: SyncActionNumberEntity)
}