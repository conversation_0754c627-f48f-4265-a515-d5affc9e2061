package com.eatapp.clementine.data.database.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import androidx.room.Upsert
import com.eatapp.clementine.data.database.entities.TableEntity
import kotlinx.coroutines.flow.Flow
import javax.inject.Singleton

@Dao
@Singleton
interface TableDao {

    @Query("SELECT * FROM tables WHERE :restaurantId = restaurantId")
    fun tablesFlow(restaurantId: String): Flow<List<TableEntity>>

    @Query("DELETE FROM tables WHERE :restaurantId = restaurantId")
    suspend fun deleteTables(restaurantId: String)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun saveTables(list: List<TableEntity>)

    @Upsert
    suspend fun updateTable(tableEntity: TableEntity)

    @Delete
    suspend fun deleteTable(tableEntity: TableEntity)

    @Query("UPDATE tables SET restaurantServerId = :serverId WHERE tableId IN (:tableIds)")
    suspend fun assignServerToTables(serverId: String, tableIds: List<String>)

    @Query("UPDATE tables SET restaurantServerId = NULL WHERE restaurantServerId = :serverId")
    suspend fun unassignAllTablesFromServer(serverId: String)

    @Query("UPDATE tables SET restaurantServerId = NULL WHERE restaurantServerId = :serverId AND tableId NOT IN (:tableIds)")
    suspend fun unassignOldTablesFromServer(serverId: String, tableIds: List<String>)

    @Transaction
    suspend fun updateServerAssignments(serverId: String, newTableIds: List<String>) {
        if (newTableIds.isEmpty()) {
            // If the list is empty, just unassign all tables from this server.
            unassignAllTablesFromServer(serverId)
        } else {
            unassignOldTablesFromServer(serverId, newTableIds)
            assignServerToTables(serverId, newTableIds)
        }
    }

    @Query("SELECT * FROM tables WHERE tableId IN (:ids)")
    suspend fun tablesByIds(ids: List<String>): List<TableEntity>
}