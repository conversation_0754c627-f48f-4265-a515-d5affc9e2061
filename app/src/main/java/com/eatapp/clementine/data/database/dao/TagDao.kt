package com.eatapp.clementine.data.database.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Upsert
import com.eatapp.clementine.data.database.entities.TagEntity
import kotlinx.coroutines.flow.Flow
import javax.inject.Singleton

@Dao
@Singleton
interface TagDao {

    @Query("SELECT * FROM tags WHERE restaurantId = :restaurantId")
    fun tagsFlow(restaurantId: String): Flow<List<TagEntity>>

    @Upsert
    suspend fun updateTag(tagEntity: TagEntity)

    @Delete
    suspend fun deleteTag(tagEntity: TagEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun saveTags(tagEntities: List<TagEntity>)

    @Query("SELECT * FROM tags WHERE context LIKE '%\"guest\"%' AND restaurantId = :restaurantId AND name = :name")
    suspend fun guestTagByName(restaurantId: String, name: String): TagEntity?

    @Query("SELECT * FROM tags WHERE context LIKE '%\"reservation\"%' AND restaurantId = :restaurantId AND name = :name")
    suspend fun reservationTagByName(restaurantId: String, name: String): TagEntity?

    @Query("DELETE FROM tags WHERE restaurantId = :restaurantId")
    suspend fun deleteTags(restaurantId: String)
}