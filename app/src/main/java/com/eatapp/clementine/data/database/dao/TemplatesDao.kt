package com.eatapp.clementine.data.database.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.eatapp.clementine.data.database.entities.MessageTemplateEntity
import com.eatapp.clementine.data.database.entities.WhatsappTemplateEntity
import kotlinx.coroutines.flow.Flow
import javax.inject.Singleton

@Singleton
@Dao
interface TemplatesDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun saveWhatsappTemplates(templates: List<WhatsappTemplateEntity>)

    @Query("SELECT * FROM whatsapp_template WHERE :restaurantId = restaurantId")
    fun whatsappTemplatesFlow(restaurantId: String): Flow<List<WhatsappTemplateEntity>>

    @Query("DELETE FROM whatsapp_template WHERE restaurantId = :restaurantId")
    suspend fun deleteWhatsappTemplates(restaurantId: String)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun saveMessageTemplates(templates: List<MessageTemplateEntity>)

    @Query("SELECT * FROM message_template WHERE :restaurantId = restaurantId")
    fun messageTemplatesFlow(restaurantId: String): Flow<List<MessageTemplateEntity>>

    @Query("DELETE FROM message_template WHERE restaurantId = :restaurantId")
    suspend fun deleteMessageTemplates(restaurantId: String)
}