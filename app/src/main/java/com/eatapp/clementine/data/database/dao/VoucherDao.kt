package com.eatapp.clementine.data.database.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.eatapp.clementine.data.database.entities.MessageTemplateEntity
import com.eatapp.clementine.data.database.entities.VoucherEntity
import com.eatapp.clementine.data.database.entities.WhatsappTemplateEntity
import com.eatapp.clementine.data.network.response.vouchers.Voucher
import kotlinx.coroutines.flow.Flow
import javax.inject.Singleton

@Singleton
@Dao
interface VoucherDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun saveVouchers(vouchers: List<VoucherEntity>)

    @Query("SELECT * FROM vouchers WHERE :restaurantId = restaurantId")
    fun vouchersFlow(restaurantId: String): Flow<List<VoucherEntity>>

    @Query("DELETE FROM vouchers WHERE restaurantId = :restaurantId")
    suspend fun deleteVouchers(restaurantId: String)
}