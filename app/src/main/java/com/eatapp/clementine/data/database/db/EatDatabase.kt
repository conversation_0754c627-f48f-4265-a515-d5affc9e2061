package com.eatapp.clementine.data.database.db

import androidx.room.Database
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import com.eatapp.clementine.data.database.converters.CustomFieldsConverter
import com.eatapp.clementine.data.database.converters.DateTypeConverter
import com.eatapp.clementine.data.database.converters.StringListConverter
import com.eatapp.clementine.data.database.converters.TaggingsConverter
import com.eatapp.clementine.data.database.dao.ClosingPeriodDao
import com.eatapp.clementine.data.database.dao.ConciergeDao
import com.eatapp.clementine.data.database.dao.ConversationsDao
import com.eatapp.clementine.data.database.dao.CustomFieldsDao
import com.eatapp.clementine.data.database.dao.DayNoteDao
import com.eatapp.clementine.data.database.dao.GuestDao
import com.eatapp.clementine.data.database.dao.OnlineSeatingShiftDao
import com.eatapp.clementine.data.database.dao.OrderDao
import com.eatapp.clementine.data.database.dao.PaymentDao
import com.eatapp.clementine.data.database.dao.PosRecordDao
import com.eatapp.clementine.data.database.dao.ReservationDao
import com.eatapp.clementine.data.database.dao.RestaurantServerDao
import com.eatapp.clementine.data.database.dao.RestaurantUserDao
import com.eatapp.clementine.data.database.dao.RoomDao
import com.eatapp.clementine.data.database.dao.SyncActionDao
import com.eatapp.clementine.data.database.dao.SyncActionNumberDao
import com.eatapp.clementine.data.database.dao.TableDao
import com.eatapp.clementine.data.database.dao.TagDao
import com.eatapp.clementine.data.database.dao.TemplatesDao
import com.eatapp.clementine.data.database.dao.VoucherDao
import com.eatapp.clementine.data.database.entities.ClosingPeriodEntity
import com.eatapp.clementine.data.database.entities.ConciergeEntity
import com.eatapp.clementine.data.database.entities.ConversationEntity
import com.eatapp.clementine.data.database.entities.CustomFieldEntity
import com.eatapp.clementine.data.database.entities.DayNoteEntity
import com.eatapp.clementine.data.database.entities.GuestEntity
import com.eatapp.clementine.data.database.entities.MessageTemplateEntity
import com.eatapp.clementine.data.database.entities.OnlineSeatingShiftEntity
import com.eatapp.clementine.data.database.entities.OrderEntity
import com.eatapp.clementine.data.database.entities.PaymentEntity
import com.eatapp.clementine.data.database.entities.PosRecordEntity
import com.eatapp.clementine.data.database.entities.ReservationEntity
import com.eatapp.clementine.data.database.entities.ReservationTableCrossRef
import com.eatapp.clementine.data.database.entities.RestaurantServerEntity
import com.eatapp.clementine.data.database.entities.RestaurantUserEntity
import com.eatapp.clementine.data.database.entities.RoomEntity
import com.eatapp.clementine.data.database.entities.SyncActionNumberEntity
import com.eatapp.clementine.data.database.entities.TableEntity
import com.eatapp.clementine.data.database.entities.TagEntity
import com.eatapp.clementine.data.database.entities.VoucherEntity
import com.eatapp.clementine.data.database.entities.WhatsappTemplateEntity
import com.eatapp.clementine.data.network.body.SyncAction
import javax.inject.Singleton

@Database(
    entities = [ReservationEntity::class,
        TableEntity::class,
        ReservationTableCrossRef::class,
        GuestEntity::class,
        ClosingPeriodEntity::class,
        ConciergeEntity::class,
        DayNoteEntity::class,
        OnlineSeatingShiftEntity::class,
        OrderEntity::class,
        PaymentEntity::class,
        PosRecordEntity::class,
        RestaurantServerEntity::class,
        RestaurantUserEntity::class,
        SyncActionNumberEntity::class,
        SyncAction::class,
        RoomEntity::class,
        TagEntity::class,
        WhatsappTemplateEntity::class,
        MessageTemplateEntity::class,
        VoucherEntity::class,
        ConversationEntity::class,
        CustomFieldEntity::class],
    version = 11,
    exportSchema = false
)
@TypeConverters(
    DateTypeConverter::class,
    StringListConverter::class,
    CustomFieldsConverter::class,
    TaggingsConverter::class
)
@Singleton
abstract class EatDatabase : RoomDatabase() {
    abstract fun reservationDao(): ReservationDao
    abstract fun tableDao(): TableDao
    abstract fun guestDao(): GuestDao
    abstract fun closingPeriodDao(): ClosingPeriodDao
    abstract fun conciergeDao(): ConciergeDao
    abstract fun dayNoteDao(): DayNoteDao
    abstract fun onlineSeatingShiftDao(): OnlineSeatingShiftDao
    abstract fun orderDao(): OrderDao
    abstract fun paymentDao(): PaymentDao
    abstract fun posRecordDao(): PosRecordDao
    abstract fun restaurantServerDao(): RestaurantServerDao
    abstract fun restaurantUserDao(): RestaurantUserDao
    abstract fun roomDao(): RoomDao
    abstract fun syncActionNumberDao(): SyncActionNumberDao
    abstract fun syncActionDao(): SyncActionDao
    abstract fun tagDao(): TagDao
    abstract fun templatesDao(): TemplatesDao
    abstract fun voucherDao(): VoucherDao
    abstract fun conversationsDao(): ConversationsDao
    abstract fun customFieldsDao(): CustomFieldsDao
}