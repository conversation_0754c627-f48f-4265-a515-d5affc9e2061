package com.eatapp.clementine.data.database.entities


import androidx.room.Entity
import androidx.room.PrimaryKey
import com.eatapp.clementine.data.network.response.closing.Closing
import com.eatapp.clementine.data.network.response.closing.ClosingAttributes
import com.google.gson.annotations.SerializedName
import java.util.Date

@Entity(tableName = "closing_periods")
data class ClosingPeriodEntity(
    @PrimaryKey
    @SerializedName("id")
    val id: String,
    @SerializedName("closing_types")
    val closingTypes: List<String>,
    @SerializedName("table_ids")
    val tableIds: List<String>,
    @SerializedName("time_range_begin")
    val timeRangeBegin: Date?,
    @SerializedName("time_range_end")
    val timeRangeEnd: Date?,
    @SerializedName("restaurant_id")
    override var restaurantId: String? = null
): RestaurantEntity() {

    fun toClosingPeriodModel(): Closing {
        return Closing(
            id,
            ClosingAttributes(
                closingTypes,
                timeRangeBegin ?: Date(),
                timeRangeEnd ?: Date(),
                tableIds,
            )
        )
    }
}