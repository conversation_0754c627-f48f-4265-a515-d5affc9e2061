package com.eatapp.clementine.data.database.entities


import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName

@Entity(tableName = "concierges")
data class ConciergeEntity(
    @PrimaryKey
    @SerializedName("id")
    val conciergeId: String,
    @SerializedName("description")
    val description: String?,
    @SerializedName("email")
    val email: String,
    @SerializedName("name")
    val name: String,
    @SerializedName("restaurant_id")
    override var restaurantId: String? = null
): RestaurantEntity()