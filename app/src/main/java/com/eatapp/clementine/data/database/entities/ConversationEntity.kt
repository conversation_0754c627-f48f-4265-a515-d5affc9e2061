package com.eatapp.clementine.data.database.entities

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.eatapp.clementine.data.network.response.conversation.Conversation
import com.eatapp.clementine.data.network.response.conversation.ConversationAttributes
import com.eatapp.clementine.internal.eatDateISO8601Timezone1DateFormatter
import com.google.gson.annotations.SerializedName
import java.util.Date

@Entity(tableName = "conversations")
data class ConversationEntity(
    @PrimaryKey
    val id: String,
    val type: String,
    val guestId: String,
    val phone: String?,
    val email: String?,
    val createdAt: Date?,
    val content: String,
    val profileImageUrl: String?,
    val contactId: String,
    var unreadMessagesCount: Int,
    val fullName: String,
    @SerializedName("restaurant_id")
    override var restaurantId: String?
) : RestaurantEntity() {
    fun toConversation(): Conversation {
        return Conversation(
            id = id,
            type = type,
            attributes = ConversationAttributes(
                guestId = guestId,
                phone = phone,
                email = email,
                createdAt = createdAt?.let { date ->
                    eatDateISO8601Timezone1DateFormatter().format(date)
                } ?: "",
                content = content,
                profileImageUrl = profileImageUrl,
                contactId = contactId,
                unreadMessagesCount = unreadMessagesCount,
                fullName = fullName
            )
        )
    }
}
