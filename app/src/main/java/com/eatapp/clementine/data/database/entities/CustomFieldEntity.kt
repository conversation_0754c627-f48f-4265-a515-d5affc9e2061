package com.eatapp.clementine.data.database.entities

import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.eatapp.clementine.data.database.converters.AnyConverter
import com.eatapp.clementine.data.network.response.custom_fields.CustomField
import com.eatapp.clementine.data.network.response.custom_fields.CustomFieldAttributes
import com.eatapp.clementine.data.network.response.custom_fields.CustomFieldComponent
import com.eatapp.clementine.data.network.response.custom_fields.CustomFieldType
import com.google.gson.annotations.SerializedName

@Entity(
    tableName = "custom_fields",
    indices = [Index(value = ["restaurantId", "component"])]
)
@TypeConverters(
    AnyConverter::class
)
data class CustomFieldEntity(
    @PrimaryKey
    val id: String,
    val component: CustomFieldComponent,
    val name: String,
    val status: String,
    val label: String,
    val type: CustomFieldType?,
    val options: List<String>?,
    val deleted: Boolean,
    val value: Any?,
    @SerializedName("restaurant_id")
    override var restaurantId: String?
) : RestaurantEntity() {
    fun toCustomField(): CustomField {
        return CustomField(
            id = id,
            attributes = CustomFieldAttributes(
                component = component,
                name = name,
                status = status,
                label = label,
                type = type,
                options = options
            ),
            deleted = deleted,
            value = value
        )
    }
}
