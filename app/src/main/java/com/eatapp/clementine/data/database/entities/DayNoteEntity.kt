package com.eatapp.clementine.data.database.entities


import androidx.room.Entity
import androidx.room.PrimaryKey
import com.eatapp.clementine.data.network.response.daynote.DayNoteAttributes
import com.eatapp.clementine.data.network.response.daynote.DayNoteData
import com.google.gson.annotations.SerializedName
import java.util.Date


@Entity(tableName = "day_notes")
data class DayNoteEntity(
    @PrimaryKey
    @SerializedName("id")
    val id: String,
    @SerializedName("content")
    var content: String?,
    @SerializedName("date")
    val date: Date?,
    @SerializedName("restaurant_id")
    override var restaurantId: String? = null
): RestaurantEntity() {

    fun toDayNoteModel(): DayNoteData {
        return DayNoteData(
            id = this.id,
            attributes = DayNoteAttributes(
                content = this.content,
                date = date
            )
        )
    }
}