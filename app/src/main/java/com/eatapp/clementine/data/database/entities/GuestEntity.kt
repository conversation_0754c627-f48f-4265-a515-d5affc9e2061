package com.eatapp.clementine.data.database.entities


import android.os.Parcelable
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.eatapp.clementine.data.database.converters.PmsDataConverter
import com.eatapp.clementine.data.database.converters.PosDataConverter
import com.eatapp.clementine.data.database.converters.VoucherAssignmentConverter
import com.eatapp.clementine.data.network.body.GuestBody
import com.eatapp.clementine.data.network.response.guest.GuestAttributes
import com.eatapp.clementine.data.network.response.guest.Guest
import com.eatapp.clementine.data.network.response.guest.GuestPosData
import com.eatapp.clementine.data.network.response.guest.GuestVisitData
import com.eatapp.clementine.internal.managers.TagType
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize
import kotlinx.parcelize.RawValue
import java.util.Date

@Entity(
    tableName = "guests",
    indices = [
        Index(value = ["restaurantId"]),
        Index(value = ["restaurantId", "firstName"]),
        Index(value = ["restaurantId", "firstName", "guestId"]),
        Index(value = ["firstName", "guestId"])
    ]
)
@TypeConverters(
    PmsDataConverter::class,
    PosDataConverter::class,
    VoucherAssignmentConverter::class
)
@Parcelize
data class GuestEntity(
    @PrimaryKey
    @SerializedName("id")
    var guestId: String,
    @SerializedName("anniversary")
    var anniversary: Date?,
    @SerializedName("content")
    val content: String?,
    @SerializedName("birthday")
    var birthday: Date?,
    @SerializedName("custom_fields")
    var customFields: Map<String, @RawValue Any>?,
    @SerializedName("email")
    var email: String?,
    @SerializedName("first_name")
    var firstName: String?,
    @SerializedName("last_name")
    var lastName: String?,
    @SerializedName("marketing_accepted")
    var marketingAccepted: Boolean,
    @SerializedName("notes")
    var notes: String?,
    @SerializedName("organisation")
    val organisation: String?,
    @SerializedName("phone")
    var phone: String?,
    @SerializedName("pos_data")
    val posData: GuestPosData?,
    @SerializedName("taggings")
    var taggingEntities: List<TaggingEntity>?,
    @SerializedName("updated_at")
    val updatedAt: Date?,
    @SerializedName("loyalty_points")
    var loyaltyPoints: Int,
    @SerializedName("demo")
    val demo: Boolean?,
    @SerializedName("voucher_assignments")
    val voucherAssignmentEntities: List<VoucherAssignmentEntity>? = null,
    @SerializedName("restaurant_id")
    override var restaurantId: String? = null
) : Parcelable, RestaurantEntity() {

    fun toGuestModel(): Guest {

        return Guest(
            this.guestId,
            relationships = null,
            GuestAttributes(
                anniversary = anniversary,
                birthday = birthday,
                email = email,
                firstName = firstName,
                lastName = lastName,
                notes = notes,
                phone = phone,
                updatedAt = updatedAt,
                posData = posData,
                visitData = GuestVisitData(
                    0,
                    0,
                    0,
                    0,
                    noShowCount = 0,
                    upcomingReservationsCount = 0,
                    reviewsAverageAmbienceRating = 0.0,
                    reviewsAverageFoodRating = 0.0,
                    reviewsAverageRating = 0.0,
                    reviewsAverageServiceRating = 0.0,
                    reviewsCount = 0
                ),
                marketingAccepted = marketingAccepted,
                organisation = organisation,
                restaurantId = restaurantId!!,
                customFields = customFields?.toMutableMap() ?: mutableMapOf(),
                loyaltyPoints = loyaltyPoints,
                demo = demo ?: false,
            ),
            tags = null,
            taggings = this.taggingEntities?.map { it.toTaggingModel(TagType.Guest) }?.toMutableList(),
            header = null,
            isHeader = false,
            unreadMessagesCount = 0,
            voucherAssignments = voucherAssignmentEntities?.map { it.toVoucherAssignmentModel() }
                ?.toMutableList()
        )
    }

    fun updateWith(body: GuestBody, taggingEntities: List<TaggingEntity>?) {
        firstName = body.firstName
        lastName = body.lastName
        anniversary = body.anniversary
        birthday = body.birthday
        email = body.email
        phone = body.phone
        notes = body.notes
        marketingAccepted = body.marketingAccepted
        customFields = body.customFields.toMutableMap()
        this.taggingEntities = taggingEntities
        this.loyaltyPoints += body.addLoyaltyPoints ?: 0
        this.loyaltyPoints -= body.redeemLoyaltyPoints ?: 0
    }
}

@Parcelize
data class PmsData(
    val stay: String?,
    val notes: String?,
    val primary: Boolean?,
    @SerializedName("stay_id")
    val stayId: String?,
    val packages: List<String>?,
    @SerializedName("checkin_at")
    val checkInAt: Date?,
    @SerializedName("checkout_at")
    val checkoutAt: Date?,
    @SerializedName("adults")
    val noOfAdults: Int?,
    @SerializedName("room_numbers")
    val roomNumbers: List<String>?,
    @SerializedName("children")
    val noOfChildren: Int?
) : Parcelable