package com.eatapp.clementine.data.database.entities

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.eatapp.clementine.data.network.response.templates.MessageTemplate
import com.eatapp.clementine.data.network.response.templates.MessageTemplateAttributes
import com.google.gson.annotations.SerializedName

@Entity(tableName = "message_template")
data class MessageTemplateEntity(
    @PrimaryKey
    @SerializedName("id")
    val id: String,
    @SerializedName("type")
    val type: String,
    @SerializedName("body")
    val body: String,
    @SerializedName("channel")
    val channel: Int,
    @SerializedName("default")
    val isDefault: Boolean,
    @SerializedName("external_template_id")
    val externalTemplateId: String?,
    @SerializedName("message_type")
    val messageType: String,
    @SerializedName("name")
    val name: String,
    @SerializedName("reservation_origin")
    val reservationOrigin: String?,
    @SerializedName("message_scope")
    val messageScope: String,
    @SerializedName("updated_at")
    val updatedAt: String,
    @SerializedName("restaurant_id")
    override var restaurantId: String?
) : RestaurantEntity() {
    fun toMessageTemplate(): MessageTemplate {
        return MessageTemplate(
            id,
            type,
            MessageTemplateAttributes(
                body,
                channel,
                isDefault,
                externalTemplateId,
                messageType,
                name,
                reservationOrigin,
                messageScope,
                updatedAt
            )
        )
    }
}