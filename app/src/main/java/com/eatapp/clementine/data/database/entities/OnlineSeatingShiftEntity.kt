package com.eatapp.clementine.data.database.entities


import android.os.Parcelable
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.eatapp.clementine.data.network.response.shift.Shift
import com.eatapp.clementine.data.network.response.shift.ShiftAttributes
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize
import java.util.Date

@Entity(tableName = "online_seating_shifts")
@Parcelize
data class OnlineSeatingShiftEntity(
    @PrimaryKey
    @SerializedName("id")
    val shiftId: String,
    @SerializedName("availability_type")
    val availabilityType: String?,
    @SerializedName("color")
    val color: String?,
    @SerializedName("covers")
    val covers: Int,
    @SerializedName("days_of_week")
    val daysOfWeek: List<String>?,
    @SerializedName("display_name")
    val displayName: String?,
    @SerializedName("end_date")
    val endDate: Date?,
    @SerializedName("first_seating")
    val firstSeating: Int,
    @SerializedName("interval")
    val interval: Int,
    @SerializedName("last_seating")
    val lastSeating: Int,
    @SerializedName("max_covers_per_reservation")
    val maxCoversPerReservation: Int,
    @SerializedName("min_covers_per_reservation")
    val minCoversPerReservation: Int,
    @SerializedName("name")
    val name: String?,
    @SerializedName("notes")
    val notes: String?,
    @SerializedName("notice_period")
    val noticePeriod: Int,
    @SerializedName("pacing")
    val pacing: Int?,
    @SerializedName("same_pacing")
    val samePacing: Boolean?,
    @SerializedName("shift_type")
    val shiftType: String?,
    @SerializedName("start_date")
    val startDate: Date?,
    @SerializedName("status")
    val status: String?,
    @SerializedName("type")
    val type: String?,
    @SerializedName("use_pacing")
    val usePacing: Boolean,
    @SerializedName("updated_at")
    val updatedAt: Date?,
    @SerializedName("restaurant_id")
    override var restaurantId: String? = null
): Parcelable, RestaurantEntity() {
    fun toShift(): Shift {
        return Shift(
            id = this.shiftId,
            type = this.type,
            attributes = ShiftAttributes(
                availabilityType = this.availabilityType ?: "",
                color = this.color,
                covers = this.covers,
                daysOfWeek = this.daysOfWeek ?: emptyList(),
                endDate = this.endDate,
                firstSeating = this.firstSeating,
                interval = this.interval,
                lastSeating = this.lastSeating,
                maxCoversPerReservation = this.maxCoversPerReservation,
                minCoversPerReservation = this.minCoversPerReservation,
                name = this.name,
                notes = this.notes,
                noticePeriod = this.noticePeriod,
                pacing = this.pacing ?: 0,
                samePacing = this.samePacing ?: false,
                shiftType = this.shiftType,
                startDate = this.startDate,
                status = this.status,
                updatedAt = this.updatedAt,
                usePacing = this.usePacing
            ),
            fakeId = ""
        )
    }
}