package com.eatapp.clementine.data.database.entities


import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.eatapp.clementine.data.database.converters.MintItemsConverter
import com.google.gson.annotations.SerializedName
import java.util.Date

@Entity(tableName = "orders")
@TypeConverters(
    MintItemsConverter::class
)
data class OrderEntity(
    @PrimaryKey
    @SerializedName("id")
    val id: String,
    @SerializedName("covers")
    val covers: Int,
    @SerializedName("created_at")
    val createdAt: String,
    @SerializedName("currency")
    val currency: String,
    @SerializedName("delivery_address")
    val deliveryAddress: String?,
    @SerializedName("delivery_door")
    val deliveryDoor: String?,
    @SerializedName("delivery_instructions")
    val deliveryInstructions: String?,
    @SerializedName("delivery_location")
    val deliveryLocation: String?,
    @SerializedName("delivery_total")
    val deliveryTotal: String,
    @SerializedName("email")
    val email: String?,
    @SerializedName("exchange_rate")
    val exchangeRate: String,
    @SerializedName("first_name")
    val firstName: String?,
    @SerializedName("grand_total")
    val grandTotal: String,
    @SerializedName("grand_total_usd")
    val grandTotalUsd: String,
    @SerializedName("guest_id")
    val guestId: String?,
    @SerializedName("items_subtotal")
    val itemsSubtotal: String,
    @SerializedName("key")
    val key: String,
    @SerializedName("last_name")
    val lastName: String?,
    @SerializedName("latitude")
    val latitude: Double?,
    @SerializedName("longitude")
    val longitude: Double?,
    @SerializedName("menu_name")
    val menuName: String,
    @SerializedName("mint_menu_id")
    val mintMenuId: String,
    @SerializedName("mint_order_items")
    val mintOrderItems: List<MintOrderItem>,
    @SerializedName("offer_by")
    val offerBy: String?,
    @SerializedName("offer_id")
    val offerId: String?,
    @SerializedName("offer_label")
    val offerLabel: String?,
    @SerializedName("offer_total")
    val offerTotal: String,
    @SerializedName("order_number")
    val orderNumber: String,
    @SerializedName("origin")
    val origin: String,
    @SerializedName("payment_option")
    val paymentOption: String,
    @SerializedName("payment_status")
    val paymentStatus: String,
    @SerializedName("phone")
    val phone: String?,
    @SerializedName("preperation_time")
    val preperationTime: Int,
    @SerializedName("private_notes")
    val privateNotes: String?,
    @SerializedName("reservation_id")
    val reservationId: String?,
    @SerializedName("special_requests")
    val specialRequests: String?,
    @SerializedName("start_time")
    val startTime: Date,
    @SerializedName("status")
    val status: String,
    @SerializedName("table_id")
    val tableId: String?,
    @SerializedName("table_number")
    val tableNumber: String?,
    @SerializedName("type")
    val type: String,
    @SerializedName("updated_at")
    val updatedAt: String,
    @SerializedName("vat_rate")
    val vatRate: String,
    @SerializedName("vat_subtotal")
    val vatSubtotal: String,
    @SerializedName("restaurant_id")
    override var restaurantId: String? = null
): RestaurantEntity() {
    data class MintOrderItem(
        @SerializedName("category")
        val category: String,
        @SerializedName("created_at")
        val createdAt: String,
        @SerializedName("grand_total")
        val grandTotal: String,
        @SerializedName("id")
        val id: String,
        @SerializedName("mint_menu_item_id")
        val mintMenuItemId: String,
        @SerializedName("mint_order_id")
        val mintOrderId: String,
        @SerializedName("modifiers")
        val modifiers: List<MintOrderItem>,
        @SerializedName("modifiers_subtotal")
        val modifiersSubtotal: String,
        @SerializedName("name")
        val name: String,
        @SerializedName("price")
        val price: String,
        @SerializedName("qty")
        val qty: Int,
        @SerializedName("special_requests")
        val specialRequests: String,
        @SerializedName("status")
        val status: String?,
        @SerializedName("updated_at")
        val updatedAt: String
    ) {
        data class Modifier(
            @SerializedName("id")
            val id: String,
            @SerializedName("mint_item_id")
            val mintItemId: String,
            @SerializedName("mint_modifier_group_id")
            val mintModifierGroupId: String,
            @SerializedName("mint_modifier_group_name")
            val mintModifierGroupName: String,
            @SerializedName("name")
            val name: String,
            @SerializedName("preperation_time")
            val preperationTime: Int,
            @SerializedName("price")
            val price: String,
            @SerializedName("qty")
            val qty: Int,
            @SerializedName("status")
            val status: String,
            @SerializedName("subtotal")
            val subtotal: String
        )
    }
}