package com.eatapp.clementine.data.database.entities


import android.os.Parcelable
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.eatapp.clementine.data.database.converters.GatewayActionConverter
import com.eatapp.clementine.data.network.response.Data
import com.eatapp.clementine.data.network.response.DataSingle
import com.eatapp.clementine.data.network.response.payment.GatewayAction
import com.eatapp.clementine.data.network.response.payment.PaymentAttributes
import com.eatapp.clementine.data.network.response.payment.Payment
import com.eatapp.clementine.data.network.response.payment.PaymentRelationships
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize
import java.util.Date

@Entity(
    tableName = "payments",
    indices = [
        Index(value = ["reservationId"])
    ]
)
@TypeConverters(
    GatewayActionConverter::class
)
@Parcelize
data class PaymentEntity(
    @PrimaryKey
    @SerializedName("id")
    val id: String,
    @SerializedName("amount")
    val amount: Double,
    @SerializedName("auto_cancel_at")
    val autoCancelAt: Date?,
    @SerializedName("cancel_url")
    val cancelUrl: String?,
    @SerializedName("capture_url")
    val captureUrl: String?,
    @SerializedName("charge_strategy")
    val chargeStrategy: String,
    @SerializedName("created_at")
    val createdAt: Date,
    @SerializedName("currency")
    val currency: String,
    @SerializedName("description")
    val description: String?,
    @SerializedName("gateway")
    val gateway: String,
    @SerializedName("gateway_actions")
    val gatewayActions: List<GatewayAction>,
    @SerializedName("guest_email")
    val guestEmail: String?,
    @SerializedName("guest_first_name")
    val guestFirstName: String?,
    @SerializedName("guest_last_name")
    val guestLastName: String?,
    @SerializedName("guest_phone")
    val guestPhone: String?,
    @SerializedName("may_send_reminder")
    val maySendReminder: Boolean,
    @SerializedName("may_update")
    val mayUpdate: Boolean,
    @SerializedName("notes")
    val notes: String?,
    @SerializedName("payment_rule_id")
    val paymentRuleId: String?,
    @SerializedName("payment_widget_url")
    val paymentWidgetUrl: String?,
    @SerializedName("refund_amount")
    val refundAmount: Double,
    @SerializedName("refund_balance")
    val refundBalance: Double,
    @SerializedName("refund_url")
    val refundUrl: String?,
    @SerializedName("reservation_id")
    val reservationId: String,
    @SerializedName("status")
    val status: String,
    @SerializedName("updated_at")
    val updatedAt: Date,
    @SerializedName("void_url")
    val voidUrl: String?,
    @SerializedName("restaurant_id")
    override var restaurantId: String? = null
) : Parcelable, RestaurantEntity() {
    fun toPaymentModel(): Payment {
        return Payment(
            id = this.id,
            attributes = PaymentAttributes(
                amount = this.amount,
                currency = this.currency,
                description = this.description,
                gateway = this.gateway,
                guestEmail = this.guestEmail,
                guestFirstName = this.guestFirstName,
                guestLastName = this.guestLastName,
                guestPhone = this.guestPhone,
                paymentWidgetUrl = this.paymentWidgetUrl,
                reservationId = this.reservationId,
                status = this.status,
                notes = this.notes,
                refundBalance = this.refundBalance,
                refundAmount = this.refundAmount,
                createdAt = this.createdAt,
                updatedAt = this.updatedAt,
                expiresAt = null,
                autoCancelAt = this.autoCancelAt,
                gatewayActions = this.gatewayActions,
                captureUrl = this.captureUrl,
                refundUrl = this.refundUrl,
                cancelUrl = this.cancelUrl,
                voidUrl = this.voidUrl,
                maySendReminder = this.maySendReminder,
                mayUpdate = this.mayUpdate,
                ruleId = this.paymentRuleId,
                chargeStrategy = this.chargeStrategy
            ),
            relationships = PaymentRelationships(DataSingle(paymentRuleId?.let { Data(it, "payment_rule") })),
            amountUI = null
        )
    }
}