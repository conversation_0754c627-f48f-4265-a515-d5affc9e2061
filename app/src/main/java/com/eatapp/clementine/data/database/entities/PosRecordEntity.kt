package com.eatapp.clementine.data.database.entities

import android.os.Parcelable
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.eatapp.clementine.data.database.converters.DiscountsConverter
import com.eatapp.clementine.data.database.converters.MenuItemConverter
import com.eatapp.clementine.data.database.converters.PaymentsConverter
import com.eatapp.clementine.data.database.converters.PosDetailsConverter
import com.eatapp.clementine.data.network.response.pos.Discount
import com.eatapp.clementine.data.network.response.pos.MenuItem
import com.eatapp.clementine.data.network.response.pos.PosRecordAttributes
import com.eatapp.clementine.data.network.response.pos.PosRecord
import com.eatapp.clementine.data.network.response.pos.PosRecordSubAttributes
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize
import java.util.Date

@Parcelize
@Entity(
    tableName = "pos_records",
    indices = [
        Index(value = ["reservationId"]),
        Index(value = ["restaurantId", "createdAt"])
    ]
)
@TypeConverters(
    PaymentsConverter::class,
    MenuItemConverter::class,
    DiscountsConverter::class,
    PosDetailsConverter::class
)
data class PosRecordEntity(
    @PrimaryKey @SerializedName("id") val id: String,
    @SerializedName("manual_match") val manualMatch: Boolean,
    @SerializedName("pos_service_id") val posServiceId: String,
    @SerializedName("reservation_id") val reservationId: String?,
    @SerializedName("created_at") val createdAt: Date,
    @SerializedName("updated_at") val updatedAt: Date,
    @SerializedName("data") val details: PosDetailsEntity,
    @SerializedName("restaurant_id")
    override var restaurantId: String? = null
) : Parcelable, RestaurantEntity() {

    fun toPosRecordModel(): PosRecord {
        return PosRecord(
            this.id,
            "pos_record",
            PosRecordAttributes(
                createdAt,
                manualMatch,
                posServiceId,
                reservationId ?: "",
                updatedAt,
                PosRecordSubAttributes(
                    commercialTotal = details.commercialTotal,
                    employeeFirstName = details.employeeFirstName,
                    employeeLastName = details.employeeLastName,
                    menuItems = details.menuItems,
                    ticketClosedAt = details.ticketClosedAt,
                    ticketId = details.ticketId,
                    ticketOpenedAt = details.ticketOpenedAt,
                    voidedItems = details.voidedItems
                )
            ),
            null
        )
    }
}

@Parcelize
data class PosDetailsEntity(
    @SerializedName("ticket_id") val ticketId: String?,
    @SerializedName("ticket_name") val ticketName: String?,
    @SerializedName("ticket_number") val ticketNumber: String?,
    @SerializedName("ticket_open") val ticketOpen: Boolean,
    @SerializedName("ticket_opened_at") val ticketOpenedAt: Date?,
    @SerializedName("ticket_closed_at") val ticketClosedAt: Date?,
    @SerializedName("ticket_void") val ticketVoid: Boolean,
    @SerializedName("table_id") val tableId: String?,
    @SerializedName("table_pos_id") val tablePosId: String?,
    @SerializedName("table_name") val tableName: String?,
    @SerializedName("commercial_total") val commercialTotal: Double?,
    @SerializedName("commercial_guest_count") val commercialGuestCount: Int?,
    @SerializedName("commercial_payment_type") val commercialPaymentType: String?,
    @SerializedName("commercial_payment_comments") val commercialPaymentComments: String?,
    @SerializedName("commercial_tips") val commercialTips: Double?,
    @SerializedName("commercial_revenue_center_id") val commercialRevenueCenterId: String?,
    @SerializedName("commercial_revenue_center_pos_id") val commercialRevenueCenterPosId: String?,
    @SerializedName("commercial_revenue_center_name") val commercialRevenueCenterName: String?,
    @SerializedName("employee_id") val employeeId: String?,
    @SerializedName("employee_pos_id") val employeePosId: String?,
    @SerializedName("employee_check_name") val employeeCheckName: String?,
    @SerializedName("employee_first_name") val employeeFirstName: String?,
    @SerializedName("employee_last_name") val employeeLastName: String?,
    @SerializedName("order_type_id") val orderTypeId: String?,
    @SerializedName("order_type_pos_id") val orderTypePosId: String?,
    @SerializedName("order_type_name") val orderTypeName: String?,
    @SerializedName("discounts") val discounts: List<Discount>?,
    @SerializedName("menu_items") val menuItems: List<MenuItem>?,
    @SerializedName("voided_items") val voidedItems: List<MenuItem>?
) : Parcelable
