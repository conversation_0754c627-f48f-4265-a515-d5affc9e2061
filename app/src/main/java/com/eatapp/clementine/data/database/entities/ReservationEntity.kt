package com.eatapp.clementine.data.database.entities


import android.os.Parcelable
import androidx.room.Embedded
import androidx.room.Entity
import androidx.room.Index
import androidx.room.Junction
import androidx.room.PrimaryKey
import androidx.room.Relation
import androidx.room.TypeConverters
import com.eatapp.clementine.data.database.converters.ChannelConverter
import com.eatapp.clementine.data.database.converters.CommentsConverter
import com.eatapp.clementine.data.database.converters.ReviewConverter
import com.eatapp.clementine.data.database.converters.StatusHistoryConverter
import com.eatapp.clementine.data.database.converters.VoucherAssignmentConverter
import com.eatapp.clementine.data.network.body.ReservationBody
import com.eatapp.clementine.data.network.response.comment.CommentModel
import com.eatapp.clementine.data.network.response.reservation.ChannelAttributes
import com.eatapp.clementine.data.network.response.reservation.ChannelModel
import com.eatapp.clementine.data.network.response.reservation.ReservationAttributes
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.network.response.reservation.ReviewModel
import com.eatapp.clementine.data.network.response.reservation.StatusHistoryModel
import com.eatapp.clementine.data.network.response.tagging.TaggingAttributes
import com.eatapp.clementine.data.network.response.tagging.Tagging
import com.eatapp.clementine.data.network.response.vouchers.VoucherAssignmentAttributes
import com.eatapp.clementine.data.network.response.vouchers.VoucherAssignmentModel
import com.eatapp.clementine.data.network.response.vouchers.VoucherType
import com.eatapp.clementine.internal.formatNonLocal
import com.eatapp.clementine.internal.managers.TagType
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize
import kotlinx.parcelize.RawValue
import java.util.Date

@Entity(
    tableName = "reservations",
    indices = [Index(value = ["restaurantId", "startTime"])]
)
@TypeConverters(
    StatusHistoryConverter::class,
    CommentsConverter::class,
    ReviewConverter::class,
    VoucherAssignmentConverter::class,
    ChannelConverter::class
)
@Parcelize
data class ReservationEntity(
    @PrimaryKey
    @SerializedName("id")
    var reservationId: String,
    @SerializedName("comments")
    var commentEntities: List<CommentEntity>? = null,
    @SerializedName("covers")
    var covers: Int,
    @SerializedName("created_by")
    var createdBy: String? = null,
    @SerializedName("custom_fields")
    var customFields: MutableMap<String, @RawValue Any>? = null,
    @SerializedName("duration")
    var duration: Int,
    @SerializedName("guest_id")
    var guestId: String? = null,
    @SerializedName("guest_name")
    var guestName: String? = null,
    @SerializedName("key")
    val key: String? = null,
    @SerializedName("lock_status")
    val lockStatus: String? = null,
    @SerializedName("notes")
    var notes: String? = null,
    @SerializedName("online")
    val online: Boolean = false,
    @SerializedName("review")
    val review: ReviewModel? = null,
    @SerializedName("start_time")
    var startTime: Date,
    @SerializedName("status")
    var status: String,
    @SerializedName("status_history")
    val statusHistoryEntity: List<StatusHistoryEntity>? = null,
    @SerializedName("table_ids")
    var tableIds: List<String>? = null,
    @SerializedName("taggings")
    var taggingEntities: List<TaggingEntity>? = null,
    @SerializedName("created_at")
    val createdAt: String,
    @SerializedName("updated_at")
    val updatedAt: String? = null,
    @SerializedName("wait_list_duration")
    var waitQuote: Int,
    @SerializedName("wait_list_position")
    val waitListPosition: Int? = null,
    @SerializedName("wait_list_queued_at")
    var waitListQueuedAt: Date? = null,
    @SerializedName("wait_list_status")
    val waitListStatus: String? = null,
    @SerializedName("wait_list_table_ready_at")
    val waitListTableReadyAt: String? = null,
    @SerializedName("walk_in")
    var walkIn: Boolean = false,
    @SerializedName("edited_by_id")
    var editedBy: String? = null,
    @SerializedName("restaurant_id")
    override var restaurantId: String? = null,
    @SerializedName("source")
    var source: String? = null,
    val channelEntity: ChannelEntity? = null,
    @SerializedName("voucher_assignments")
    val voucherAssignmentEntities: List<VoucherAssignmentEntity>? = null,
    val demo: Boolean? = false
) : Parcelable, RestaurantEntity() {

    fun updateWith(body: ReservationBody, taggings: List<Tagging>?) {
        body.covers?.let { covers = it }
        body.createdBy?.let { createdBy = it }
        body.duration?.let { duration = it }
        guestName = body.guestName
        guestId = body.guestId
        notes = body.notes
        startTime = formatNonLocal().parse(body.startTime!!)!!
        body.status?.let { status = it }
        body.walkIn?.let { walkIn = it }
        body.waitlistDuration?.let { waitQuote = it }
        waitListQueuedAt = body.waitlistQueuedAt
        body.editedBy?.let { editedBy = it }
        source = body.source
        customFields = body.customFields.toMutableMap()
        commentEntities = body.comments?.map { it.toComment() }
        tableIds = body.tableIds
        this.taggingEntities = taggings?.map { it.toEntity() }
    }

    fun toReservationModel(
        guestEntity: GuestEntity? = null,
        tableEntityModels: List<TableEntity>? = null,
        paymentEntities: List<PaymentEntity>? = null,
        posRecordEntities: List<PosRecordEntity>
    ): Reservation {
        return Reservation(
            this.reservationId,
            null,
            ReservationAttributes(
                covers,
                createdAt,
                createdBy,
                duration,
                key,
                source,
                notes,
                online,
                startTime,
                status,
                guestName,
                mutableListOf(),
                updatedAt,
                walkIn,
                lockStatus ?: "",
                waitQuote,
                waitListQueuedAt,
                waitListTableReadyAt,
                waitListStatus,
                review,
                commentEntities?.map { it.toCommentModel() }?.toMutableList(),
                restaurantId!!,
                customFields ?: mutableMapOf(),
                editedBy,
                waitListPosition,
                statusHistoryEntity?.map { it.toStatusHistoryModel() }?.toMutableList(),
                demo ?: false
            ),
            guestEntity?.toGuestModel(),
            this.channelEntity?.toChannelModel(),
            taggingEntities?.map { it.toTaggingModel(TagType.Reservation) }?.toMutableList(),
            tableEntityModels?.map { it.toTableModel() },
            paymentEntities?.map { it.toPaymentModel() }?.toMutableList(),
            this.voucherAssignmentEntities?.map { it.toVoucherAssignmentModel() }?.toMutableList(),
            posRecordEntities.map { it.toPosRecordModel() }
        )
    }
}

@Parcelize
data class CommentEntity(
    @SerializedName("comment")
    val comment: String,
    @SerializedName("name")
    val name: String,
    @SerializedName("time")
    val time: Date
) : Parcelable {
    fun toCommentModel(
        color: String? = null,
        editable: Boolean = false,
        isInEditMode: Boolean = false
    ): CommentModel {
        return CommentModel(
            comment = this.comment,
            name = this.name,
            time = this.time,
            color = color,
            editable = editable,
            isInEditMode = isInEditMode
        )
    }
}

@Parcelize
data class StatusHistoryEntity(
    @SerializedName("status")
    val status: String,
    @SerializedName("timestamp")
    val timestamp: String?
) : Parcelable {
    fun toStatusHistoryModel(): StatusHistoryModel {
        return StatusHistoryModel(
            status,
            timestamp,
            false
        )
    }
}

@Parcelize
data class TaggingEntity(
    @SerializedName("category")
    val category: String?,
    @SerializedName("icon")
    val icon: String,
    @SerializedName("id")
    val id: String?,
    @SerializedName("name")
    val name: String,
    @SerializedName("tagger")
    val tagger: String?,
    var color: String?
) : Parcelable {

    fun toTaggingModel(type: TagType): Tagging {
        return Tagging(
            attributes = TaggingAttributes(
                category = this.category,
                icon = this.icon,
                name = this.name,
                tagger = this.tagger
            ),
            id = this.id,
            type = type.name,
            color = this.color
        )
    }
}

@Parcelize
data class ChannelEntity(
    val id: String,
    @SerializedName("display_name")
    val displayName: String
) : Parcelable {
    fun toChannelModel(): ChannelModel {
        return ChannelModel(
            id,
            ChannelAttributes(
                displayName
            )
        )
    }
}

@Parcelize
data class VoucherAssignmentEntity(
    val id: String,
    @SerializedName("voucher_id")
    val voucherId: String,
    val name: String?,
    val volume: Int?,
    @SerializedName("expires_at")
    val expiresAt: Date?,
    val status: String?,
    @SerializedName("redeemed_at")
    val redeemedAt: Date?,
    val context: List<VoucherType>?,
    @SerializedName("reservation_id")
    val reservationId: String?,
    val description: String?,
    val code: String?
) : Parcelable {
    fun toVoucherAssignmentModel(): VoucherAssignmentModel {
        return VoucherAssignmentModel(
            id,
            VoucherAssignmentAttributes(
                name,
                voucherId,
                volume,
                expiresAt,
                context,
                status,
                reservationId,
                description,
                code,
                redeemedAt
            )
        )
    }
}

@Entity(
    primaryKeys = ["reservationId", "tableId"],
    indices = [
        Index(value = ["reservationId"]),
        Index(value = ["tableId"])
    ]
)
data class ReservationTableCrossRef(
    val reservationId: String,
    val tableId: String
)

//@Entity(primaryKeys = ["reservationId", "shiftId"])
//data class ReservationShiftCrossRef(
//    val reservationId: String,
//    @ColumnInfo(index = true)
//    val shiftId: String
//)

@Parcelize
data class ReservationWithData(

    @Embedded
    val reservationEntity: ReservationEntity,

    @Relation(
        parentColumn = "reservationId",
        entityColumn = "tableId",
        associateBy = Junction(ReservationTableCrossRef::class)
    )
    val tableEntities: List<TableEntity>?,

    @Relation(
        parentColumn = "guestId",
        entityColumn = "guestId"
    )
    val guestEntity: GuestEntity?,

    @Relation(
        parentColumn = "reservationId",
        entityColumn = "reservationId"
    )
    val paymentEntities: List<PaymentEntity>?,

    @Relation(
        parentColumn = "reservationId",
        entityColumn = "reservationId"
    )
    val posRecordEntities: List<PosRecordEntity>,

//    @Relation(
//        parentColumn = "reservationId",
//        entityColumn = "shiftId",
//        associateBy = Junction(ReservationShiftCrossRef::class)
//    )
//    val preferences: List<OnlineSeatingShift>?
) : Parcelable {

    fun toReservationModel(): Reservation {
        return this.reservationEntity.toReservationModel(guestEntity, tableEntities, paymentEntities, posRecordEntities)
    }
}