package com.eatapp.clementine.data.database.entities


import androidx.room.Embedded
import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.Relation
import com.eatapp.clementine.data.network.response.server.Server
import com.eatapp.clementine.data.network.response.server.ServerAttributes
import com.google.gson.annotations.SerializedName

@Entity(tableName = "restaurant_servers")
data class RestaurantServerEntity(
    @PrimaryKey
    @SerializedName("id")
    val serverId: String,
    @SerializedName("color")
    val color: String,
    @SerializedName("name")
    val name: String,
    @SerializedName("restaurant_id")
    override var restaurantId: String? = null
): RestaurantEntity() {
    fun toServerModel(tableEntities: List<TableEntity>?): Server {
        return Server(
            this.serverId,
            null,
            ServerAttributes(
                this.color,
                this.name
            ),
            tables = tableEntities?.map { it.toTableModel() }?.toMutableList()
        )
    }
}

data class RestaurantServerWithTables(
    @Embedded
    val server: RestaurantServerEntity,

    @Relation(
        parentColumn = "serverId",
        entityColumn = "restaurantServerId"
    )
    val tableEntities: List<TableEntity>?
) {
    fun toServerModel(): Server {
        return server.toServerModel(tableEntities)
    }
}