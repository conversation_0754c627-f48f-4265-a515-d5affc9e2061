package com.eatapp.clementine.data.database.entities


import androidx.room.Entity
import androidx.room.PrimaryKey
import com.eatapp.clementine.data.network.response.user.User
import com.google.gson.annotations.SerializedName

@Entity(tableName = "restaurant_users")
data class RestaurantUserEntity(
    @PrimaryKey
    @SerializedName("id")
    val id: String,
    @SerializedName("color")
    val color: String?,
    @SerializedName("email")
    val email: String?,
    @SerializedName("login")
    val login: <PERSON><PERSON><PERSON>,
    @SerializedName("name")
    val name: String,
    @SerializedName("pin_code")
    val pinCode: String?,
    @SerializedName("refund")
    val refund: Boolean,
    @SerializedName("refund_pin_required")
    val refundPinRequired: Boolean,
    @SerializedName("role")
    val role: String,
    @SerializedName("server")
    val server: <PERSON><PERSON><PERSON>,
    @SerializedName("taker")
    val taker: <PERSON><PERSON><PERSON>,
    @SerializedName("taker_pin_required")
    val takerPinRequired: Bo<PERSON>an,
    @SerializedName("restaurant_id")
    override var restaurantId: String? = null
): RestaurantEntity() {
    fun toUserModel(): User {
        return User(
            id = this.id,
            color = this.color ?: "",
            email = this.email ?: "",
            login = this.login,
            name = this.name,
            permissions = listOf(), // Assuming permissions not available in RestaurantUser, set as needed
            pinCode = this.pinCode,
            role = this.role,
            server = this.server,
            taker = this.taker,
            allowRefund = this.refund,
            refundPinRequired = this.refundPinRequired,
            takerPinRequired = this.takerPinRequired
        )
    }

}