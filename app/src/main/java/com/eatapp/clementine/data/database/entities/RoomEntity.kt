package com.eatapp.clementine.data.database.entities


import androidx.room.Embedded
import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.Relation
import com.eatapp.clementine.data.network.response.room.RoomAttributes
import com.eatapp.clementine.data.network.response.room.Room
import com.google.gson.annotations.SerializedName
import java.util.Date

@Entity(tableName = "rooms")
data class RoomEntity(
    @PrimaryKey
    @SerializedName("id")
    val roomId: String,
    @SerializedName("background_image_sid")
    val backgroundImageSid: String?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("updated_at")
    val updatedAt: Date?,
    @SerializedName("restaurant_id")
    override var restaurantId: String? = null
): RestaurantEntity() {

    fun toRoomModel(tableEntities: List<TableEntity>? = null): Room {
        return Room(
            id = this.roomId,
            attributes = RoomAttributes(
                name = this.name,
                backgroundImageId = this.backgroundImageSid ?: "",
                updatedAt = this.updatedAt
            ),
            tables = tableEntities?.map { it.toTableModel() }
        )
    }
}

data class RoomWithTables(
    @Embedded
    val roomEntity: RoomEntity,

    @Relation(
        parentColumn = "roomId",
        entityColumn = "roomId"
    )

    val tableEntities: List<TableEntity>?
) {
    fun toRoomModel(): Room {
       return this.roomEntity.toRoomModel(tableEntities)
    }
}