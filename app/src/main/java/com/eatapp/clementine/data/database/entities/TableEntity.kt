package com.eatapp.clementine.data.database.entities


import android.os.Parcelable
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.eatapp.clementine.data.network.response.room.TableAttributes
import com.eatapp.clementine.data.network.response.room.Table
import com.eatapp.clementine.data.network.response.room.TableType
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize
import java.util.Date

@Entity(tableName = "tables")
@Parcelize
data class TableEntity(
    @PrimaryKey
    @SerializedName("id")
    val tableId: String,
    @SerializedName("color")
    val color: String?,
    @SerializedName("max_covers")
    val maxCovers: Int,
    @SerializedName("min_covers")
    val minCovers: Int,
    @SerializedName("number")
    val number: String,
    @SerializedName("pos_service_table_id")
    val posServiceTableId: String?,
    @SerializedName("restaurant_server_id")
    val restaurantServerId: String?,
    @SerializedName("room_id")
    val roomId: String,
    @SerializedName("rotation")
    val rotation: Int,
    @SerializedName("shape")
    val shape: String,
    @SerializedName("size")
    val size: String,
    @SerializedName("sold_online")
    val soldOnline: Boolean,
    @SerializedName("type")
    val type: String?,
    @SerializedName("updated_at")
    val updatedAt: Date?,
    @SerializedName("x")
    val x: Double,
    @SerializedName("y")
    val y: Double,
    @SerializedName("restaurant_id")
    override var restaurantId: String? = null
): Parcelable, RestaurantEntity() {
    fun toTableModel(): Table {
        return Table(
            id = this.tableId,
            attributes = TableAttributes(
                maxCovers = this.maxCovers,
                minCovers = this.minCovers,
                number = this.number,
                type = TableType.entries.find { it.type == this.type } ?: TableType.Table,
                color = this.color,
                posServiceTableId = this.posServiceTableId,
                restaurantServerId = this.restaurantServerId,
                roomId = this.roomId,
                rotation = this.rotation,
                shape = this.shape,
                size = this.size,
                soldOnline = this.soldOnline,
                updatedAt = this.updatedAt ?: Date(),
                x = this.x,
                y = this.y
            )
        )
    }

}