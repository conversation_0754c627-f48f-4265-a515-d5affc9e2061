package com.eatapp.clementine.data.database.entities


import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.eatapp.clementine.data.database.converters.TagCategoryConverter
import com.eatapp.clementine.data.network.response.tag.Tag
import com.google.gson.annotations.SerializedName

@Entity(tableName = "tags")
@TypeConverters(
    TagCategoryConverter::class
)
data class TagEntity(
    @PrimaryKey
    @SerializedName("id")
    val id: String,
    @SerializedName("context")
    val context: List<String>,
    @SerializedName("description")
    val description: String?,
    @SerializedName("icon")
    val icon: String?,
    @SerializedName("importance")
    val importance: Int,
    @SerializedName("name")
    val name: String,
    @SerializedName("tag_category")
    val tagCategory: TagCategory?,
    @SerializedName("restaurant_id")
    override var restaurantId: String?
) : RestaurantEntity() {

    fun toTagModel(): Tag {
        val categoryModel = tagCategory?.let {
            Tag.TagCategoryModel(
                id = "",
                attributes = Tag.TagCategoryModel.CategoryAttributes(
                    name = it.name,
                    color = it.color,
                    description = it.description
                )
            )
        }
        return Tag(
            id = this.id,
            attributes = Tag.TagAttributes(
                context = this.context,
                description = this.description,
                icon = this.icon,
                importance = this.importance,
                name = this.name,
            ),
            relationships = null,
            category = categoryModel
        )
    }

    data class TagCategory(
        val name: String,
        val color: String,
        val description: String? = null
    )
}