package com.eatapp.clementine.data.database.entities

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.eatapp.clementine.data.database.converters.VoucherTypeConverter
import com.eatapp.clementine.data.network.response.vouchers.Voucher
import com.eatapp.clementine.data.network.response.vouchers.VoucherType
import com.google.gson.annotations.SerializedName
import java.util.Date

@Entity(tableName = "vouchers")
@TypeConverters(
    VoucherTypeConverter::class
)
data class VoucherEntity(
    @PrimaryKey
    @SerializedName("id")
    val id: String,
    @SerializedName("code")
    val code: String?,
    @SerializedName("context")
    val context: List<VoucherType>?,
    @SerializedName("created_at")
    val createdAt: Date?,
    @SerializedName("description")
    val description: String?,
    @SerializedName("expires_at")
    val expiresAt: Date?,
    @SerializedName("key")
    val key: String?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("status")
    val status: String?,
    @SerializedName("updated_at")
    val updatedAt: Date?,
    @SerializedName("volume")
    val volume: Int?,
    @SerializedName("restaurant_id")
    override var restaurantId: String?
) : RestaurantEntity() {

    fun toVoucher(): Voucher {
        return Voucher(
            Voucher.Attributes(
                code,
                context,
                createdAt,
                description,
                expiresAt,
                key,
                name,
                status,
                updatedAt,
                volume
            ),
            id
        )
    }
}
