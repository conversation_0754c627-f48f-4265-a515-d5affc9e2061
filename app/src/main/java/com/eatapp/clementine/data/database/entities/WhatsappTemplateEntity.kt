package com.eatapp.clementine.data.database.entities

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.eatapp.clementine.data.database.converters.TemplateComponentsConverter
import com.eatapp.clementine.data.network.response.templates.TemplateComponents
import com.eatapp.clementine.data.network.response.templates.WhatsappTemplate
import com.eatapp.clementine.data.network.response.templates.WhatsappTemplateAttributes
import com.google.gson.annotations.SerializedName

@Entity(tableName = "whatsapp_template")
@TypeConverters(
    TemplateComponentsConverter::class
)
data class WhatsappTemplateEntity(
    @PrimaryKey
    @SerializedName("id")
    val id: String,
    @SerializedName("type")
    val type: String,
    @SerializedName("status")
    val status: String,
    @SerializedName("message_type")
    val messageType: String,
    @SerializedName("name")
    val name: String,
    @SerializedName("language")
    val language: String,
    @SerializedName("category")
    val category: String,
    @SerializedName("components")
    val components: TemplateComponents,
    @SerializedName("updated_at")
    val updatedAt: String,
    @SerializedName("connected_to_event")
    val connectedToEvent: Boolean,
    @SerializedName("belongs_to_restaurant")
    val belongsToRestaurant: Boolean,
    @SerializedName("restaurant_id")
    override var restaurantId: String?
): RestaurantEntity() {
    fun toWhatsappTemplate(): WhatsappTemplate {
        return WhatsappTemplate(
            id,
            type,
            WhatsappTemplateAttributes(
                status,
                messageType,
                name,
                language,
                category,
                components,
                updatedAt,
                connectedToEvent,
                belongsToRestaurant
            )
        )
    }
}