package com.eatapp.clementine.data.datasource.closing

import com.eatapp.clementine.data.network.body.ClosingBody
import com.eatapp.clementine.data.network.response.closing.Closing
import kotlinx.coroutines.flow.Flow
import java.util.Date

interface ClosingDataSource {
    fun closings(date: Date): Flow<List<Closing>>
    suspend fun insertClosing(closingBody: ClosingBody)
    suspend fun deleteClosing(closingId: String)
}