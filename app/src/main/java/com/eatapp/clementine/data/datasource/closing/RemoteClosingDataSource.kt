package com.eatapp.clementine.data.datasource.closing

import com.eatapp.clementine.data.network.EatApiRestaurant
import com.eatapp.clementine.data.network.body.ClosingBody
import com.eatapp.clementine.data.network.response.closing.Closing
import com.eatapp.clementine.internal.simpleDate
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.util.Date
import javax.inject.Inject

class RemoteClosingDataSource @Inject constructor(
    private val apiService: EatApiRestaurant
) : ClosingDataSource {
    override fun closings(date: Date): Flow<List<Closing>> {
        return flow {
           val response = apiService.closingsAsync(simpleDate(date))
            emit(response.closings)
        }
    }

    override suspend fun insertClosing(closingBody: ClosingBody) {
        apiService.insertClosingAsync(closingBody)
    }

    override suspend fun deleteClosing(closingId: String) {
        apiService.deleteClosingAsync(closingId)
    }
}