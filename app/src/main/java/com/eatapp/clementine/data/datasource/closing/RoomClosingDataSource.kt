package com.eatapp.clementine.data.datasource.closing

import com.eatapp.clementine.data.database.dao.ClosingPeriodDao
import com.eatapp.clementine.data.database.entities.ClosingPeriodEntity
import com.eatapp.clementine.data.network.body.ClosingBody
import com.eatapp.clementine.data.network.body.SyncAction
import com.eatapp.clementine.data.network.response.closing.Closing
import com.eatapp.clementine.data.repository.SyncActionRepository
import com.eatapp.clementine.internal.endOfTheEatDay
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.internal.startOfTheEatDay
import com.eatapp.clementine.internal.sync.EntitySyncActionDataMapper
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import java.util.Date
import java.util.UUID
import javax.inject.Inject

class RoomClosingDataSource @Inject constructor(
    private val eatManager: EatManager,
    private val closingPeriodDao: ClosingPeriodDao,
    private val syncActionRepository: SyncActionRepository
) : ClosingDataSource {

    override fun closings(date: Date): Flow<List<Closing>> {
        return closingPeriodDao.closingPeriodsFlow(
            eatManager.restaurantId(),
            startOfTheEatDay(date),
            endOfTheEatDay(date)
        ).map {
            it.map { it.toClosingPeriodModel() }
        }
    }

    override suspend fun insertClosing(closingBody: ClosingBody) {
        val entity = ClosingPeriodEntity(
            UUID.randomUUID().toString(),
            closingBody.closingTypes,
            closingBody.tableIds,
            closingBody.timeRangeBegin,
            closingBody.timeRangeEnd,
            eatManager.restaurantId()
        )
        val data = EntitySyncActionDataMapper.dataClassToSerializedMap(entity)

        val syncAction = SyncAction(
            SyncAction.Action.CREATE_CLOSING_PERIOD.value,
            data,
            eatManager.restaurantId()
        )

        syncActionRepository.saveSyncAction(syncAction)
        closingPeriodDao.updateClosingPeriod(entity)
    }

    override suspend fun deleteClosing(closingId: String) {
        val data = mapOf(
            "id" to closingId
        )

        val syncAction = SyncAction(
            SyncAction.Action.DELETE_CLOSING_PERIOD.value,
            data,
            eatManager.restaurantId()
        )

        syncActionRepository.saveSyncAction(syncAction)
        closingPeriodDao.deleteClosingPeriodById(eatManager.restaurantId(), closingId)
    }
}