package com.eatapp.clementine.data.datasource.daynote

import com.eatapp.clementine.data.network.EatApiRestaurant
import com.eatapp.clementine.data.network.response.daynote.DayNoteData
import com.eatapp.clementine.internal.simpleDate
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.util.Date
import javax.inject.Inject

class RemoteDayNoteDataSource @Inject constructor(
    private val apiService: EatApiRestaurant
) : DayNoteDataSource {
    override fun dayNote(date: Date): Flow<List<DayNoteData>> {
        return flow {
            val response = apiService.dayNoteAsync(simpleDate(date))
            emit(response.data)
        }
    }

    override suspend fun insertDayNote(date: Date, content: String): DayNoteData {
        return apiService.insertDayNoteAsync(simpleDate(date), content).data
    }

    override suspend fun updateDayNote(noteId: String, content: String): DayNoteData {
        return apiService.updateDayNoteAsync(noteId, content).data
    }
}