package com.eatapp.clementine.data.datasource.daynote

import com.eatapp.clementine.data.database.dao.DayNoteDao
import com.eatapp.clementine.data.database.dao.SyncActionDao
import com.eatapp.clementine.data.database.entities.DayNoteEntity
import com.eatapp.clementine.data.network.body.SyncAction
import com.eatapp.clementine.data.network.response.daynote.DayNoteData
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.internal.simpleDate
import com.eatapp.clementine.internal.startOfTheDay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import java.util.Date
import java.util.UUID
import javax.inject.Inject

class RoomDayNoteDataSource @Inject constructor(
    private val eatManager: EatManager,
    private val dayNoteDao: DayNoteDao,
    private val syncActionDao: SyncActionDao
) : DayNoteDataSource {

    override fun dayNote(date: Date): Flow<List<DayNoteData>> {
        return dayNoteDao.dayNotesFlow(
            eatManager.restaurantId(),
            startOfTheDay(date)
        ).map {
            it.map { it.toDayNoteModel() }
        }
    }

    override suspend fun insertDayNote(date: Date, content: String): DayNoteData {
        val dayNote = DayNoteEntity(
            UUID.randomUUID().toString(),
            content,
            date,
            eatManager.restaurantId()
        )
        val data = mapOf(
            "id" to dayNote.id,
            "content" to content,
            "date" to simpleDate(date)
        )
        val syncAction = SyncAction(
            SyncAction.Action.CREATE_DAY_NOTE.value,
            data,
            eatManager.restaurantId()
        )
        syncActionDao.saveSyncAction(syncAction)
        dayNoteDao.updateDayNote(dayNote)
        return dayNote.toDayNoteModel()
    }

    override suspend fun updateDayNote(noteId: String, content: String): DayNoteData {
        val entity = dayNoteDao.dayNoteById(noteId, eatManager.restaurantId())
        entity.content = content

        val data = mapOf(
            "id" to noteId,
            "content" to content
        )

        val syncAction = SyncAction(
            SyncAction.Action.UPDATE_DAY_NOTE.value,
            data,
            eatManager.restaurantId()
        )
        syncActionDao.saveSyncAction(syncAction)
        dayNoteDao.updateDayNote(entity)
        return entity.toDayNoteModel()
    }
}