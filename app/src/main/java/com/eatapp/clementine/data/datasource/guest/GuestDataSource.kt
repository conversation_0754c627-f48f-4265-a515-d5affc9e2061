package com.eatapp.clementine.data.datasource.guest

import androidx.paging.PagingData
import com.eatapp.clementine.data.network.body.GuestBody
import com.eatapp.clementine.data.network.response.guest.Guest
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.network.response.tagging.Tagging
import kotlinx.coroutines.flow.Flow

interface GuestDataSource {
    fun paginatedGuests(
        restaurantId: String,
        query: String?,
    ): Flow<PagingData<Guest>>

    suspend fun updateGuest(
        guestId: String,
        guestBody: GuestBody,
        taggings: List<Tagging>?
    ): Guest

    suspend fun createGuest(guestBody: GuestBody): Guest
    suspend fun deleteGuest(guestId: String)
    suspend fun reservations(guestId: String): List<Reservation>
    suspend fun guests(query: String?, page: Int, limit: Int): List<Guest>
}