package com.eatapp.clementine.data.datasource.guest

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.eatapp.clementine.data.database.dao.GuestDao
import com.eatapp.clementine.data.database.entities.GuestEntity

/**
This class was implemented to improve guest search speed. When native PagingSource<Int, Guest>
is used, it uses COUNT internally which dramatically slows down queries.
 */
class GuestKeysetPagingSource(
    private val guestDao: GuestDao,
    private val restaurantId: String,
    private val query: String? = null,
    private val pageSize: Int = 30
) : PagingSource<Pair<String?, String>, GuestEntity>() {

    override suspend fun load(params: LoadParams<Pair<String?, String>>): LoadResult<Pair<String?, String>, GuestEntity> {
        return try {
            val data = if (query.isNullOrBlank()) {
                if (params.key == null) {
                    guestDao.getFirstPageByFirstNameGuestId(restaurantId, pageSize)
                } else {
                    val (lastFirstName, lastGuestId) = params.key!!
                    guestDao.getNextPageByFirstNameGuestId(
                        restaurantId,
                        lastFirstName!!,
                        lastGuestId,
                        pageSize
                    )
                }
            } else {
                if (params.key == null) {
                    guestDao.getFirstPageFtsByFirstNameGuestId(query, pageSize, restaurantId)
                } else {
                    val (lastFirstName, lastGuestId) = params.key!!
                    guestDao.getNextPageFtsByFirstNameGuestId(
                        query,
                        lastFirstName!!,
                        lastGuestId,
                        pageSize,
                        restaurantId
                    )
                }
            }

            val last = data.lastOrNull()
            val nextKey = if (last != null) Pair(last.firstName, last.guestId) else null
            LoadResult.Page(
                data = data,
                prevKey = null,
                nextKey = nextKey
            )
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Pair<String?, String>, GuestEntity>): Pair<String?, String>? =
        null
}
