package com.eatapp.clementine.data.datasource.guest

import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import androidx.paging.PagingSource
import androidx.paging.PagingState
import androidx.paging.map
import com.eatapp.clementine.data.network.EatApiRestaurant
import com.eatapp.clementine.data.network.body.AssignVouchersRequest
import com.eatapp.clementine.data.network.body.GuestBody
import com.eatapp.clementine.data.network.body.RedeemVoucherAssignmentRequest
import com.eatapp.clementine.data.network.response.guest.Guest
import com.eatapp.clementine.data.network.response.guest.GuestResponse
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.network.response.tagging.Tagging
import com.eatapp.clementine.data.repository.TagRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import javax.inject.Inject

class RemoteGuestDataSource @Inject constructor(
    private val eatApiService: EatApiRestaurant,
    private val tagRepository: TagRepository
) : GuestDataSource {

    override fun paginatedGuests(
        restaurantId: String,
        query: String?,
    ): Flow<PagingData<Guest>> {
        return Pager(
            PagingConfig(pageSize = 30)
        ) {
            GuestRemotePagingSource(query, eatApiService)
        }.flow.map { pagingData ->
            pagingData.map { tagRepository.addTagColors(it) }
        }.distinctUntilChanged()
    }

    override suspend fun updateGuest(
        guestId: String,
        guestBody: GuestBody,
        taggings: List<Tagging>?
    ): Guest {
        val guest = eatApiService.updateGuestAsync(guestId, guestBody).guest
        tagRepository.addTagColors(guest)
        return guest
    }

    override suspend fun createGuest(guestBody: GuestBody): Guest {
        val guest = eatApiService.createGuestAsync(guestBody).guest
        tagRepository.addTagColors(guest)
        return guest
    }

    override suspend fun deleteGuest(guestId: String) {
        eatApiService.deleteGuestAsync(guestId)
    }

    override suspend fun reservations(guestId: String): List<Reservation> {
        val reservations = eatApiService.guestReservationsAsync(1000, guestId).reservations
        reservations.forEach {
            it.guest?.let { guest ->
                tagRepository.addTagColors(guest)
            }
            tagRepository.addTagColors(it)
        }
        return reservations
    }

    suspend fun updateVouchersForGuest(
        guestId: String,
        body: AssignVouchersRequest
    ): GuestResponse {
        return eatApiService.updateVouchersForGuest(
            guestId,
            body
        )
    }

    suspend fun redeemVoucherForGuest(
        guestId: String,
        body: RedeemVoucherAssignmentRequest
    ): GuestResponse {
        return eatApiService.redeemVoucherForGuest(
            guestId,
            body
        )
    }

    suspend fun guest(guestId: String): Guest {
        val guest = eatApiService.guest(guestId).guest
        tagRepository.addTagColors(guest)
        return guest
    }

    override suspend fun guests(query: String?, page: Int, limit: Int): List<Guest> {
        val guests = eatApiService.guestsAsync(query, page, limit).guests
        guests.forEach {
            tagRepository.addTagColors(it)
        }
        return guests
    }
}

class GuestRemotePagingSource(
    private val query: String? = null,
    private val remoteApi: EatApiRestaurant
) : PagingSource<Int, Guest>() {

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, Guest> {
        val page = params.key ?: 1
        return try {
            val response = remoteApi.guestsAsync(
                query = query,
                page = page,
                limit = params.loadSize
            )

            val guests = response.guests
            val meta = response.meta
            val links = response.links

            LoadResult.Page(
                data = guests,
                prevKey = if (meta.currentPage == 1 || links.prev == null) null else meta.currentPage - 1,
                nextKey = if (links.next == true) meta.currentPage + 1 else null
            )
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, Guest>): Int? {
        return state.anchorPosition?.let { anchorPosition ->
            val anchorPage = state.closestPageToPosition(anchorPosition)
            anchorPage?.prevKey?.plus(1) ?: anchorPage?.nextKey?.minus(1)
        }
    }
}