package com.eatapp.clementine.data.datasource.guest

import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import androidx.paging.map
import com.eatapp.clementine.data.database.dao.GuestDao
import com.eatapp.clementine.data.database.dao.ReservationDao
import com.eatapp.clementine.data.network.body.GuestBody
import com.eatapp.clementine.data.network.body.SyncAction
import com.eatapp.clementine.data.network.response.guest.Guest
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.network.response.tagging.Tagging
import com.eatapp.clementine.data.repository.SyncActionRepository
import com.eatapp.clementine.data.repository.TagRepository
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.internal.sync.EntitySyncActionDataMapper
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import java.util.UUID
import javax.inject.Inject

class RoomGuestDataSource @Inject constructor(
    private val guestDao: GuestDao,
    private val reservationDao: ReservationDao,
    private val tagRepository: TagRepository,
    private val eatManager: EatManager,
    private val syncActionRepository: SyncActionRepository
) : GuestDataSource {

    private var currentPagingSource: GuestKeysetPagingSource? = null

    override fun paginatedGuests(
        restaurantId: String,
        query: String?,
    ): Flow<PagingData<Guest>> {
        return Pager(
            PagingConfig(pageSize = 30, enablePlaceholders = false)
        ) {
            GuestKeysetPagingSource(guestDao, restaurantId, query).also { currentPagingSource = it }
        }.flow.map {
            it.map {
                tagRepository.addTagColors(it)
                it.toGuestModel()
            }
        }.distinctUntilChanged()
    }

    override suspend fun updateGuest(
        guestId: String,
        guestBody: GuestBody,
        taggings: List<Tagging>?
    ): Guest {

        val data = EntitySyncActionDataMapper.dataClassToSerializedMap(guestBody).toMutableMap()
        data["id"] = guestId

        val syncAction = SyncAction(
            SyncAction.Action.UPDATE_GUEST.value,
            data,
            eatManager.restaurantId()
        )

        syncActionRepository.saveSyncAction(syncAction)

        val guest = guestDao.getGuest(eatManager.restaurantId(), guestId)
        guest.updateWith(guestBody, taggings?.map { it.toEntity() })

        guestDao.updateGuest(guest)
        return guest.toGuestModel()
    }

    override suspend fun createGuest(guestBody: GuestBody): Guest {

        val guest = Guest()
        guest.id = UUID.randomUUID().toString()
        guest.updateWith(guestBody)

        guestDao.updateGuest(guest.toGuest())

        val data = EntitySyncActionDataMapper.dataClassToSerializedMap(guestBody).toMutableMap()

        val syncAction = SyncAction(
            SyncAction.Action.CREATE_GUEST.value,
            data,
            eatManager.restaurantId()
        )

        syncActionRepository.saveSyncAction(syncAction)
        return guest
    }

    override suspend fun deleteGuest(guestId: String) {
        val data = mapOf(
            "id" to guestId
        )

        val syncAction = SyncAction(
            SyncAction.Action.DELETE_GUEST.value,
            data,
            eatManager.restaurantId()
        )
        syncActionRepository.saveSyncAction(syncAction)
        guestDao.deleteGuest(guestId)
    }

    override suspend fun reservations(guestId: String): List<Reservation> {
        return reservationDao.reservations(guestId).map {
            tagRepository.addTagColors(it.reservationEntity)
            it.toReservationModel()
        }
    }

    override suspend fun guests(query: String?, page: Int, limit: Int): List<Guest> {
        val guests = guestDao.guests(eatManager.restaurantId(), query)
        guests.forEach {
            tagRepository.addTagColors(it)
        }

        return guests.map { it.toGuestModel() }
    }

    fun invalidatePagingSource() {
        currentPagingSource?.invalidate()
    }
}