package com.eatapp.clementine.data.datasource.payments

import com.eatapp.clementine.data.network.body.CreatePaymentBody
import com.eatapp.clementine.data.network.body.EditPaymentBody
import com.eatapp.clementine.data.network.response.message.MessagesResponse
import com.eatapp.clementine.data.network.response.payment.Payment
import com.eatapp.clementine.data.network.response.payment.PaymentResponse
import com.eatapp.clementine.data.network.response.payment.PaymentRulesResponse
import com.eatapp.clementine.data.repository.PaymentActionType
import kotlinx.coroutines.flow.Flow

interface PaymentDataSource {
    fun payments(reservationId: String): Flow<List<Payment>>
    fun payment(paymentId: String): Flow<Payment>
    suspend fun updatePayment(paymentId: String, actionType: PaymentActionType)
    suspend fun refundPayment(
        paymentId: String,
        refundAmount: Double,
        userId: String,
        pin: String?
    ): PaymentResponse

    suspend fun sendReminder(paymentId: String): MessagesResponse
    suspend fun paymentRules(): PaymentRulesResponse
    suspend fun createPayment(createPaymentBody: CreatePaymentBody): PaymentResponse
    suspend fun editPayment(paymentId: String, editPaymentBody: EditPaymentBody): Payment
}