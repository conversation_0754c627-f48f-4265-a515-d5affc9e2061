package com.eatapp.clementine.data.datasource.payments

import com.eatapp.clementine.data.network.EatApiPaymentsService
import com.eatapp.clementine.data.network.EatApiRestaurant
import com.eatapp.clementine.data.network.body.CreatePaymentBody
import com.eatapp.clementine.data.network.body.EditPaymentBody
import com.eatapp.clementine.data.network.response.message.MessagesResponse
import com.eatapp.clementine.data.network.response.payment.Payment
import com.eatapp.clementine.data.network.response.payment.PaymentResponse
import com.eatapp.clementine.data.network.response.payment.PaymentRulesResponse
import com.eatapp.clementine.data.repository.PaymentActionType
import com.eatapp.clementine.internal.managers.EatManager
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

class RemotePaymentDataSource @Inject constructor(
    private val paymentsService: EatApiPaymentsService,
    private val apiService: EatApiRestaurant,
    private val eatManager: EatManager
) : PaymentDataSource {

    override fun payments(reservationId: String): Flow<List<Payment>> {
        return flow {
            val response = apiService.reservationAsync(eatManager.restaurantId(), reservationId)
            emit(response.reservation.payments)
        }
    }

    override fun payment(paymentId: String): Flow<Payment> {
        return flow {
            val response = paymentsService.loadPayment(paymentId)
            emit(response.payment)
        }
    }

    override suspend fun updatePayment(
        paymentId: String,
        actionType: PaymentActionType
    ) {
        paymentsService.updatePayment(paymentId, actionType.path)
    }

    override suspend fun refundPayment(
        paymentId: String,
        refundAmount: Double,
        userId: String,
        pin: String?
    ): PaymentResponse {
        return paymentsService.refundPayment(
            paymentId,
            refundAmount = refundAmount,
            userId = userId,
            pin = pin
        )
    }

    override suspend fun sendReminder(paymentId: String): MessagesResponse {
        return paymentsService.sendReminder(paymentId)
    }

    override suspend fun paymentRules(): PaymentRulesResponse {
        return paymentsService.loadPaymentRules()
    }

    override suspend fun createPayment(createPaymentBody: CreatePaymentBody): PaymentResponse {
        return paymentsService.createPayment(createPaymentBody)
    }

    override suspend fun editPayment(
        paymentId: String,
        editPaymentBody: EditPaymentBody
    ): Payment {
        return paymentsService.editPayment(paymentId, editPaymentBody).payment
    }
}