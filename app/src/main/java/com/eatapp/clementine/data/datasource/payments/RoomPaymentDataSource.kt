package com.eatapp.clementine.data.datasource.payments

import com.eatapp.clementine.data.database.dao.PaymentDao
import com.eatapp.clementine.data.network.body.CreatePaymentBody
import com.eatapp.clementine.data.network.body.EditPaymentBody
import com.eatapp.clementine.data.network.response.message.MessagesResponse
import com.eatapp.clementine.data.network.response.payment.Payment
import com.eatapp.clementine.data.network.response.payment.PaymentResponse
import com.eatapp.clementine.data.network.response.payment.PaymentRulesResponse
import com.eatapp.clementine.data.repository.PaymentActionType
import com.eatapp.clementine.internal.managers.EatManager
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject

class RoomPaymentDataSource @Inject constructor(
    private val paymentDao: PaymentDao,
    private val eatManager: EatManager
) : PaymentDataSource {

    override fun payments(reservationId: String): Flow<List<Payment>> {
        return paymentDao.paymentsFlow(
            eatManager.restaurantId(),
            reservationId
        ).map {
            it.map {
                it.toPaymentModel()
            }
        }
    }

    override fun payment(paymentId: String): Flow<Payment> {
        return paymentDao.paymentById(
            eatManager.restaurantId(),
            paymentId
        ).map { it.toPaymentModel() }
    }

    override suspend fun updatePayment(
        paymentId: String,
        actionType: PaymentActionType
    ) {
        throw IllegalStateException("Method shouldn't be called in offline mode")
    }

    override suspend fun refundPayment(
        paymentId: String,
        refundAmount: Double,
        userId: String,
        pin: String?
    ): PaymentResponse {
        throw IllegalStateException("Method shouldn't be called in offline mode")
    }

    override suspend fun sendReminder(paymentId: String): MessagesResponse {
        throw IllegalStateException("Method shouldn't be called in offline mode")
    }

    override suspend fun paymentRules(): PaymentRulesResponse {
        throw IllegalStateException("Method shouldn't be called in offline mode")
    }

    override suspend fun createPayment(createPaymentBody: CreatePaymentBody): PaymentResponse {
        throw IllegalStateException("Method shouldn't be called in offline mode")
    }

    override suspend fun editPayment(
        paymentId: String,
        editPaymentBody: EditPaymentBody
    ): Payment {
        throw IllegalStateException("Method shouldn't be called in offline mode")
    }
}