package com.eatapp.clementine.data.datasource.posrecord

import com.eatapp.clementine.data.network.EatApiPosService
import com.eatapp.clementine.data.network.response.pos.PosRecord
import com.eatapp.clementine.internal.simpleDate
import java.util.Date
import javax.inject.Inject

class RemotePosRecordDataSource @Inject constructor(
    private val posService: EatApiPosService
): PosRecordDataSource {
    override suspend fun posRecord(posRecordId: String): PosRecord {
        return posService.posRecordAsync(posRecordId).posRecord
    }

    override suspend fun posRecords(date: Date): List<PosRecord> {
        return posService.posRecordsAsync(simpleDate(date)).posRecords
    }
}