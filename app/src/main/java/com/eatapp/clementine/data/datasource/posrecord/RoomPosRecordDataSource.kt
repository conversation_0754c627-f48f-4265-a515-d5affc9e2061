package com.eatapp.clementine.data.datasource.posrecord

import com.eatapp.clementine.data.database.dao.PosRecordDao
import com.eatapp.clementine.data.network.response.pos.PosRecord
import com.eatapp.clementine.internal.endOfTheEatDay
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.internal.startOfTheDay
import java.util.Date
import javax.inject.Inject

class RoomPosRecordDataSource @Inject constructor(
    private val posRecordDao: PosRecordDao,
    private val eatManager: EatManager
) : PosRecordDataSource {

    override suspend fun posRecord(posRecordId: String): PosRecord? {
        return posRecordDao.posRecord(eatManager.restaurantId(), posRecordId)?.toPosRecordModel()
    }

    override suspend fun posRecords(date: Date): List<PosRecord> {
        return posRecordDao.posRecordsFlow(
            eatManager.restaurantId(),
            startOfTheDay(date),
            endOfTheEatDay(date)
        ).map {
            it.toPosRecordModel()
        }
    }
}