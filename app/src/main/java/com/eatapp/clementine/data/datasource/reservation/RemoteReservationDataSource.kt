package com.eatapp.clementine.data.datasource.reservation

import com.eatapp.clementine.data.database.dao.TableDao
import com.eatapp.clementine.data.network.EatApiRestaurant
import com.eatapp.clementine.data.network.body.AssignVouchersRequest
import com.eatapp.clementine.data.network.body.RedeemVoucherAssignmentRequest
import com.eatapp.clementine.data.network.body.ReservationBody
import com.eatapp.clementine.data.network.body.StatusBody
import com.eatapp.clementine.data.network.response.notification.NotificationsResponse
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.network.response.reservation.ReservationResponse
import com.eatapp.clementine.data.network.response.survey.SurveyData
import com.eatapp.clementine.data.network.response.tagging.Tagging
import com.eatapp.clementine.data.repository.MessagingRepository
import com.eatapp.clementine.data.repository.TagRepository
import com.eatapp.clementine.internal.managers.TagType
import com.eatapp.clementine.internal.simpleDate
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.util.Date
import javax.inject.Inject

class RemoteReservationDataSource @Inject constructor(
    private val apiService: EatApiRestaurant,
    private val tagRepository: TagRepository,
    private val tableDao: TableDao,
    private val messagingRepository: MessagingRepository
) : ReservationDataSource {

    override fun reservations(
        restaurantId: String,
        startDate: Date?,
        endDate: Date?
    ): Flow<List<Reservation>> {
        return flow {
            val reservations =
                apiService.reservationsAsync(1000, simpleDate(startDate!!)).reservations
            processTables(reservations)
            appendTagColors(reservations)
            reservations.forEach {
                processUnreadMessages(it)
            }
            emit(reservations)
        }
    }

    override suspend fun reservation(restaurantId: String, eatId: String): Reservation {
        val reservation = apiService.reservationAsync(restaurantId, eatId).reservation
        appendTagColors(listOf(reservation))
        processTables(reservation)
        processUnreadMessages(reservation)
        return apiService.reservationAsync(restaurantId, eatId).reservation
    }

    override suspend fun updateReservation(
        restaurantId: String,
        reservationId: String,
        reservationBody: ReservationBody,
        taggings: List<Tagging>?
    ): Reservation {
        val reservation = apiService.updateReservationAsync(
            restaurantId,
            reservationId,
            reservationBody
        ).reservation
        appendTagColors(listOf(reservation))
        processTables(reservation)
        processUnreadMessages(reservation)
        return reservation
    }

    override suspend fun deleteReservation(reservationId: String) {
        apiService.deleteReservationAsync(reservationId)
    }

    override suspend fun createReservation(
        reservationBody: ReservationBody,
        taggings: List<Tagging>?
    ): Reservation {
        val reservation = apiService.createReservationAsync(reservationBody).reservation
        processTables(reservation)
        processUnreadMessages(reservation)
        appendTagColors(listOf(reservation))
        return reservation
    }

    suspend fun surveys(): SurveyData =
        apiService.surveysAsync()

    suspend fun updateVouchersForReservation(
        restaurantId: String,
        reservationId: String,
        body: AssignVouchersRequest
    ): ReservationResponse {
        return apiService.updateVouchersForReservation(restaurantId, reservationId, body)
    }

    suspend fun redeemVoucherForReservation(
        restaurantId: String,
        reservationId: String,
        body: RedeemVoucherAssignmentRequest
    ): ReservationResponse {
        return apiService.redeemVoucherForReservation(
            restaurantId,
            reservationId,
            body
        )
    }

    suspend fun tableReady(reservationId: String): ReservationResponse {
        return apiService.tableReady(reservationId)
    }

    suspend fun notifications(
        page: Int,
        limit: Int
    ): NotificationsResponse {
        return apiService.notificationsAsync(
            page,
            limit
        )
    }

    override suspend fun updateReservationStatus(
        restaurantId: String,
        reservationId: String,
        statusBody: StatusBody
    ) {
        apiService.updateReservationStatusAsync(restaurantId, reservationId, statusBody)
    }

    private fun appendTagColors(reservations: List<Reservation>) {
        reservations.forEach { reservation ->
            tagRepository.addTagColors(reservation)
            reservation.guest?.let {
                tagRepository.addTagColors(it)
            }
        }
    }

    private suspend fun processTables(reservations: List<Reservation>) {
        reservations.forEach {
            processTables(it)
        }
    }

    private suspend fun processTables(reservation: Reservation) {
        val tableIds = reservation.relationships?.tables?.data?.map { it.id }
        tableIds?.let {
            val tables = tableDao.tablesByIds(tableIds)
            reservation.tables = tables.map { it.toTableModel() }
        }
    }

    private fun processUnreadMessages(reservation: Reservation) {
        reservation.guest?.unreadMessagesCount =
            messagingRepository.conversations.value?.find { conversation ->
                conversation.guestId == reservation.guest?.id
            }?.unreadMessagesCount ?: 0
    }
}