package com.eatapp.clementine.data.datasource.reservation

import com.eatapp.clementine.data.network.body.ReservationBody
import com.eatapp.clementine.data.network.body.StatusBody
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.network.response.tagging.Tagging
import kotlinx.coroutines.flow.Flow
import java.util.Date

interface ReservationDataSource {

    fun reservations(
        restaurantId: String,
        startDate: Date?,
        endDate: Date?
    ): Flow<List<Reservation>>

    suspend fun reservation(restaurantId: String, eatId: String): Reservation

    suspend fun updateReservation(
        restaurantId: String,
        reservationId: String,
        reservationBody: ReservationBody,
        taggings: List<Tagging>?
    ): Reservation

    suspend fun deleteReservation(reservationId: String)

    suspend fun createReservation(
        reservationBody: ReservationBody,
        taggings: List<Tagging>?
    ): Reservation

    suspend fun updateReservationStatus(
        restaurantId: String,
        reservationId: String,
        statusBody: StatusBody
    )
}