package com.eatapp.clementine.data.datasource.reservation

import com.eatapp.clementine.internal.sync.EntitySyncActionDataMapper
import com.eatapp.clementine.data.database.dao.ReservationDao
import com.eatapp.clementine.data.database.entities.ReservationEntity
import com.eatapp.clementine.data.database.entities.ReservationWithData
import com.eatapp.clementine.data.network.body.ReservationBody
import com.eatapp.clementine.data.network.body.StatusBody
import com.eatapp.clementine.data.network.body.SyncAction
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.network.response.tagging.Tagging
import com.eatapp.clementine.data.repository.SyncActionRepository
import com.eatapp.clementine.data.repository.TagRepository
import com.eatapp.clementine.internal.formatISO8601Timezone
import com.eatapp.clementine.internal.formatNonLocal
import com.eatapp.clementine.internal.managers.EatManager
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import java.util.Date
import java.util.UUID
import javax.inject.Inject

class RoomReservationDataSource @Inject constructor(
    private val reservationDao: ReservationDao,
    private val tagsRepository: TagRepository,
    private val eatManager: EatManager,
    private val syncActionRepository: SyncActionRepository
) : ReservationDataSource {

    override fun reservations(
        restaurantId: String,
        startDate: Date?,
        endDate: Date?,
    ): Flow<List<Reservation>> {
        return reservationDao.reservationWithDataFlow(restaurantId, startDate, endDate)
            .map {
                appendTagColors(it)
                it.map {
                    it.toReservationModel()
                }
            }
    }

    override suspend fun reservation(restaurantId: String, eatId: String): Reservation {
        return reservationDao.getReservation(restaurantId, eatId).toReservationModel()
    }

    override suspend fun updateReservation(
        restaurantId: String,
        reservationId: String,
        reservationBody: ReservationBody,
        taggings: List<Tagging>?
    ): Reservation {
        val data =
            EntitySyncActionDataMapper.dataClassToSerializedMap(reservationBody).toMutableMap()
        data["id"] = reservationId

        val syncAction = SyncAction(
            action = SyncAction.Action.UPDATE_RESERVATION.value,
            data = data,
            restaurantId = eatManager.restaurantId()
        )

        syncActionRepository.saveSyncAction(syncAction)

        val reservation = reservationDao.reservation(restaurantId, reservationId)

        reservation.updateWith(reservationBody, taggings)
        reservationDao.updateReservation(reservation)

        reservationDao.updateReservationTables(
            reservationId,
            reservationBody.tableIds ?: emptyList()
        )

        val updated = reservationDao.getReservation(restaurantId, reservationId)
        return updated.toReservationModel()
    }

    override suspend fun deleteReservation(reservationId: String) {
        val data = mapOf(
            "id" to reservationId
        )

        val syncAction = SyncAction(
            SyncAction.Action.DELETE_RESERVATION.value,
            data,
            eatManager.restaurantId()
        )

        syncActionRepository.saveSyncAction(syncAction)
        reservationDao.deleteReservation(reservationId)
    }

    override suspend fun createReservation(
        reservationBody: ReservationBody,
        taggings: List<Tagging>?
    ): Reservation {

        val reservationEntity = ReservationEntity(
            UUID.randomUUID().toString(),
            covers = reservationBody.covers ?: 2,
            createdBy = reservationBody.createdBy,
            updatedAt = Date().formatISO8601Timezone(),
            createdAt = Date().formatISO8601Timezone(),
            taggingEntities = taggings?.map { it.toEntity() },
            duration = reservationBody.duration!!,
            guestId = reservationBody.guestId,
            guestName = reservationBody.guestName,
            notes = reservationBody.notes,
            startTime = formatNonLocal().parse(reservationBody.startTime!!)!!,
            status = reservationBody.status!!,
            tableIds = reservationBody.tableIds,
            walkIn = reservationBody.walkIn!!,
            waitQuote = reservationBody.waitlistDuration!!,
            commentEntities = reservationBody.comments?.map { it.toComment() },
            customFields = reservationBody.customFields.toMutableMap(),
            editedBy = reservationBody.editedBy,
            source = reservationBody.source,
            restaurantId = eatManager.restaurantId()
        )
        val data =
            EntitySyncActionDataMapper.dataClassToSerializedMap(reservationBody).toMutableMap()

        val syncAction = SyncAction(
            SyncAction.Action.CREATE_RESERVATION.value,
            data,
            eatManager.restaurantId()
        )

        syncActionRepository.saveSyncAction(syncAction)
        reservationDao.updateReservation(reservationEntity)
        reservationDao.updateReservationTables(
            reservationEntity.reservationId,
            reservationBody.tableIds ?: emptyList()
        )

        val updated =
            reservationDao.getReservation(
                eatManager.restaurantId(),
                reservationEntity.reservationId
            )
        return updated.toReservationModel()
    }

    override suspend fun updateReservationStatus(
        restaurantId: String,
        reservationId: String,
        statusBody: StatusBody
    ) {
        val data = mapOf(
            "id" to reservationId,
            "status" to statusBody.status
        )

        val syncAction = SyncAction(
            action = SyncAction.Action.UPDATE_RESERVATION.value,
            data = data,
            restaurantId = eatManager.restaurantId()
        )

        syncActionRepository.saveSyncAction(syncAction)

        val reservation = reservationDao.reservation(restaurantId, reservationId)
        reservation.status = statusBody.status
        reservationDao.updateReservation(reservation)
    }

    private fun appendTagColors(reservations: List<ReservationWithData>): List<ReservationWithData> {
        reservations.forEach { reservation ->
            tagsRepository.addTagColors(reservation.reservationEntity)
            reservation.guestEntity?.let {
                tagsRepository.addTagColors(it)
            }
        }

        return reservations
    }
}