package com.eatapp.clementine.data.datasource.server

import com.eatapp.clementine.data.database.dao.RestaurantServerDao
import com.eatapp.clementine.data.database.dao.TableDao
import com.eatapp.clementine.data.network.EatApiRestaurant
import com.eatapp.clementine.data.network.body.ServerBody
import com.eatapp.clementine.data.network.response.server.Server
import com.eatapp.clementine.internal.managers.EatManager
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

class RemoteServerDataSource @Inject constructor(
    private val apiService: EatApiRestaurant,
    private val serverDao: RestaurantServerDao,
    private val eatManager: EatManager,
    private val tableDao: TableDao
) : ServerDataSource {

    override suspend fun servers() {
        val servers = apiService.serversAsync().servers
        serverDao.deleteRestaurantServers(eatManager.restaurantId())
        serverDao.saveRestaurantServers(servers.map { it.toRestaurantServer(eatManager.restaurantId()) })
    }

    override suspend fun updateServer(serverId: String, body: ServerBody) {
        val server = apiService.updateServerAsync(serverId, body).server
        serverDao.updateRestaurantServer(server.toRestaurantServer(eatManager.restaurantId()))
        tableDao.updateServerAssignments(serverId, server.relationships?.tables?.data?.map { it.id } ?: emptyList())
    }
}