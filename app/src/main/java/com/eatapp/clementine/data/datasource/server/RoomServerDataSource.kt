package com.eatapp.clementine.data.datasource.server

import com.eatapp.clementine.data.database.dao.RestaurantServerDao
import com.eatapp.clementine.data.database.dao.SyncActionDao
import com.eatapp.clementine.data.database.dao.TableDao
import com.eatapp.clementine.data.network.body.ServerBody
import com.eatapp.clementine.data.network.body.SyncAction
import com.eatapp.clementine.internal.managers.EatManager
import javax.inject.Inject

class RoomServerDataSource @Inject constructor(
    private val restaurantServerDao: RestaurantServerDao,
    private val tableDao: TableDao,
    private val eatManager: EatManager,
    private val syncActionDao: SyncActionDao,
) : ServerDataSource {

    override suspend fun updateServer(serverId: String, body: ServerBody) {
        val current = restaurantServerDao.restaurantServerById(
            eatManager.restaurantId(), serverId
        )
        tableDao.updateServerAssignments(serverId, body.tableIds)

        val currentTableIds = current.tableEntities?.map { it.tableId } ?: emptyList()
        val tablesToRemove = currentTableIds.minus(body.tableIds.toSet())
        val tablesToAdd = body.tableIds.minus(currentTableIds.toSet())

        val actionsToRemove = tablesToRemove.map {
            createSyncAction(it, null)
        }

        val actionsToAdd = tablesToAdd.map {
            createSyncAction(it, serverId)
        }

        syncActionDao.saveSyncActions(actionsToRemove + actionsToAdd)
    }

    /*
     Used only for rest api mode, since otherwise server updates will be propagated via sync
     */
    override suspend fun servers() {}

    private fun createSyncAction(
        it: String,
        serverId: String?
    ): SyncAction {
        val data = mapOf(
            "id" to it,
            "restaurant_server_id" to serverId,
        )

        return SyncAction(
            SyncAction.Action.UPDATE_TABLE.value,
            data,
            eatManager.restaurantId()
        )
    }
}