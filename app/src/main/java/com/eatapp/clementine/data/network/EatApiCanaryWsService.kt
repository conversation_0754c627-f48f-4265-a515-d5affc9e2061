package com.eatapp.clementine.data.network

import com.eatapp.clementine.data.network.interceptor.EatInterceptor
import com.eatapp.clementine.internal.Endpoints
import com.google.gson.Gson
import com.jakewharton.retrofit2.adapter.kotlin.coroutines.CoroutineCallAdapterFactory
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import retrofit2.http.GET
import retrofit2.http.Path
import java.util.concurrent.TimeUnit

interface EatApiCanaryWsService {

    @GET("canary/{key}")
    suspend fun canaryAsync(
        @Path("key") key: String
    )

    companion object {

        operator fun invoke(
            eatInterceptor: EatInterceptor,
            gson: Gson
        ): EatApiCanaryWsService {

            val requestInterceptor = Interceptor{chain ->

                val url = chain.request()
                    .url
                    .newBuilder()
                    .build()

                val request = chain.request()
                    .newBuilder()
                    .url(url)
                    .build()

                return@Interceptor chain.proceed(request)
            }

            val okHttpClient = OkHttpClient.Builder()
                .addInterceptor(requestInterceptor)
                .addInterceptor(eatInterceptor)
                .connectTimeout(40, TimeUnit.SECONDS)
                .readTimeout(40, TimeUnit.SECONDS)
                .build()

            return Retrofit.Builder()
                .client(okHttpClient)
                .baseUrl(Endpoints.canaryEndpoint)
                .addCallAdapterFactory(CoroutineCallAdapterFactory())
                .addConverterFactory(GsonConverterFactory.create(gson))
                .build()
                .create(EatApiCanaryWsService::class.java)
        }
    }
}