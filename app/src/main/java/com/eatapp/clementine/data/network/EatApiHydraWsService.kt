package com.eatapp.clementine.data.network

import com.eatapp.clementine.data.network.interceptor.EatInterceptor
import com.eatapp.clementine.data.network.response.websocket.WebsocketMessage
import com.eatapp.clementine.internal.Endpoints
import com.google.gson.Gson
import com.jakewharton.retrofit2.adapter.kotlin.coroutines.CoroutineCallAdapterFactory
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import retrofit2.http.GET
import java.util.concurrent.TimeUnit

interface EatApiHydraWsService {

    @GET("hydra")
    suspend fun hydraAsync(): WebsocketMessage

    companion object {

        operator fun invoke(
            eatInterceptor: EatInterceptor,
            gson: Gson
        ): EatApiHydraWsService {

            val requestInterceptor = Interceptor{chain ->

                val url = chain.request()
                    .url
                    .newBuilder()
                    .build()

                val request = chain.request()
                    .newBuilder()
                    .url(url)
                    .build()

                return@Interceptor chain.proceed(request)
            }

            val okHttpClient = OkHttpClient.Builder()
                .addInterceptor(requestInterceptor)
                .addInterceptor(eatInterceptor)
                .connectTimeout(40, TimeUnit.SECONDS)
                .readTimeout(40, TimeUnit.SECONDS)
                .build()

            return Retrofit.Builder()
                .client(okHttpClient)
                .baseUrl(Endpoints.hydraEndpoint)
                .addCallAdapterFactory(CoroutineCallAdapterFactory())
                .addConverterFactory(GsonConverterFactory.create(gson))
                .build()
                .create(EatApiHydraWsService::class.java)
        }
    }
}