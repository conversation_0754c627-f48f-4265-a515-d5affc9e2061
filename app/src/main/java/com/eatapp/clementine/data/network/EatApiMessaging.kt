package com.eatapp.clementine.data.network

import com.eatapp.clementine.data.network.response.conversation.ConversationsResponse
import com.eatapp.clementine.data.network.response.message.MessagesResponse
import com.eatapp.clementine.internal.Constants
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query
import retrofit2.http.Header

interface EatApiMessaging {

    @GET("messages")
    suspend fun messages(
        @Header(Constants.RESTAURANT_ID_HEADER) restaurantId: String,
        @Query("guest_id") guestId: String? = null,
        @Query("reservation_id") reservationId: String? = null,
        @Query("limit") limit: Int = 999
    ): MessagesResponse

    @GET("conversations")
    suspend fun conversations(
        @Query("limit") limit: Int = 30
    ): ConversationsResponse

    @POST("sms")
    suspend fun sendSms(
        @Header(Constants.RESTAURANT_ID_HEADER) restaurantId: String,
        @Query("reservation_id") reservationId: String? = null,
        @Query("guest_id") guestId: String? = null,
        @Query("template_id") messageTemplateId: String? = null,
        @Query("text") text: String? = null
    ): Any

    @POST("emails")
    suspend fun sendEmail(
        @Header(Constants.RESTAURANT_ID_HEADER) restaurantId: String,
        @Query("reservation_id") reservationId: String? = null,
        @Query("guest_id") guestId: String? = null,
        @Query("template_id") messageTemplateId: String? = null,
        @Query("text") text: String? = null
    ): Any

    @POST("whatsapp")
    suspend fun sendWhatsapp(
        @Header(Constants.RESTAURANT_ID_HEADER) restaurantId: String,
        @Query("reservation_id") reservationId: String? = null,
        @Query("guest_id") guestId: String? = null,
        @Query("template_id") messageTemplateId: String? = null,
        @Query("text") text: String? = null
    ): Any

    @POST("messages/read")
    suspend fun markAsRead(
        @Header(Constants.RESTAURANT_ID_HEADER) restaurantId: String,
        @Query("guest_id") guestId: String
    ): Any
}