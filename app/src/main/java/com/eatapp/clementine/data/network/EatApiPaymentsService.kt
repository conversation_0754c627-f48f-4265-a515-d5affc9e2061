package com.eatapp.clementine.data.network

import com.eatapp.clementine.data.network.body.CreatePaymentBody
import com.eatapp.clementine.data.network.body.EditPaymentBody
import com.eatapp.clementine.data.network.response.message.MessagesResponse
import com.eatapp.clementine.data.network.response.payment.PaymentResponse
import com.eatapp.clementine.data.network.response.payment.PaymentRulesResponse
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.PATCH
import retrofit2.http.POST
import retrofit2.http.Path
import retrofit2.http.Query

interface EatApiPaymentsService {

    @GET("payments/{paymentId}")
    suspend fun loadPayment(
        @Path("paymentId") paymentId: String
    ): PaymentResponse

    @POST("payments/{paymentId}/{actionType}")
    suspend fun updatePayment(
        @Path("paymentId") paymentId: String,
        @Path("actionType") actionType: String
    ): PaymentResponse

    @POST("payments/{paymentId}/{actionType}")
    suspend fun refundPayment(
        @Path("paymentId") paymentId: String,
        @Path("actionType") actionType: String = "refund",
        @Query("refund_amount") refundAmount: Double,
        @Query("restaurant_user_id") userId: String,
        @Query("pin_code") pin: String?
    ): PaymentResponse

    @POST("payments/{paymentId}/messages")
    suspend fun sendReminder(
        @Path("paymentId") paymentId: String,
        @Query("message_type") messageType: String = "payment_reminder"
    ): MessagesResponse

    @GET("payment_rules")
    suspend fun loadPaymentRules(): PaymentRulesResponse

    @POST("payments")
    suspend fun createPayment(@Body body: CreatePaymentBody): PaymentResponse

    @PATCH("payments/{paymentId}")
    suspend fun editPayment(@Path("paymentId") paymentId: String, @Body body: EditPaymentBody): PaymentResponse
}