package com.eatapp.clementine.data.network


import com.eatapp.clementine.data.network.response.pos.PosRecordResponse
import com.eatapp.clementine.data.network.response.pos.PosRecordsResponse
import retrofit2.http.GET
import retrofit2.http.Path
import retrofit2.http.Query


interface EatApiPosService {

    @GET("pos_records/{pos_record_id}")
    suspend fun posRecordAsync(
        @Path("pos_record_id") posRecordId: String
    ): PosRecordResponse

    @GET("pos_records")
    suspend fun posRecordsAsync(
        @Query("created_on_date") date: String
    ): PosRecordsResponse
}