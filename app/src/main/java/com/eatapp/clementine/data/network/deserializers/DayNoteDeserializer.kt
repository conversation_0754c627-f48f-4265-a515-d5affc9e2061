package com.eatapp.clementine.data.network.deserializers

import com.eatapp.clementine.data.database.entities.DayNoteEntity
import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.google.gson.JsonNull
import com.google.gson.JsonObject
import com.google.gson.JsonParseException
import com.google.gson.JsonSerializationContext
import com.google.gson.JsonSerializer
import java.lang.reflect.Type
import java.text.SimpleDateFormat
import java.util.Locale

class DayNoteDeserializer: JsonSerializer<DayNoteEntity>, JsonDeserializer<DayNoteEntity> {

    private val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.US)

    override fun serialize(
        src: DayNoteEntity?,
        typeOfSrc: Type?,
        context: JsonSerializationContext?
    ): JsonElement {
        val jsonObject = JsonObject()
        src?.let {
            jsonObject.addProperty("id", it.id)
            jsonObject.addProperty("content", it.content)
            jsonObject.addProperty("date", it.date?.let { date -> dateFormat.format(date) })
        }
        return jsonObject
    }

    override fun deserialize(
        json: JsonElement?,
        typeOfT: Type?,
        context: JsonDeserializationContext?
    ): DayNoteEntity {
        val jsonObject = json?.asJsonObject ?: throw JsonParseException("Invalid JSON for DayNote")

        val id = jsonObject.get("id").asString
        val content =  jsonObject.get("content")?.takeIf { it !is JsonNull }?.asString
        val dateStr = jsonObject.get("date")?.asString
        val date = dateStr?.let { dateFormat.parse(it) }

        return DayNoteEntity(
            id = id,
            content = content,
            date = date
        )
    }
}