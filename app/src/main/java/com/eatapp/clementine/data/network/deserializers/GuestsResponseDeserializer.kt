package com.eatapp.clementine.data.network.deserializers

import com.eatapp.clementine.data.network.response.guest.GuestResponse
import com.eatapp.clementine.data.network.response.guest.GuestsResponse
import com.eatapp.clementine.internal.DateTypeAdapter
import com.eatapp.clementine.internal.managers.EatManager
import com.google.gson.GsonBuilder
import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import java.lang.reflect.Type
import java.util.Date
import javax.inject.Inject

class GuestsResponseDeserializer @Inject constructor(
    private val eatManager: EatManager,
    private val dateTypeAdapter: DateTypeAdapter
): JsonDeserializer<GuestsResponse> {

    override fun deserialize(
        json: JsonElement?,
        typeOfT: Type?,
        context: JsonDeserializationContext?
    ): GuestsResponse {
        val builder = GsonBuilder()
            .registerTypeAdapter(Date::class.java, dateTypeAdapter)
            .setObjectToNumberStrategy {
                // Read the JSON number as string to inspect its format
                val numberAsString = it.nextString()

                if (numberAsString.contains('.')) numberAsString.toDouble()
                else numberAsString.toLong()
            }
            .serializeNulls()
        val gson = builder.create()
        val guestsResponse = gson.fromJson(json, GuestsResponse::class.java)
        eatManager.processGuests(guestsResponse)
        return guestsResponse
    }
}

class GuestResponseDeserializer @Inject constructor(
    private val eatManager: EatManager,
    private val dateTypeAdapter: DateTypeAdapter
): JsonDeserializer<GuestResponse> {

    override fun deserialize(
        json: JsonElement?,
        typeOfT: Type?,
        context: JsonDeserializationContext?
    ): GuestResponse {
        val builder = GsonBuilder()
            .registerTypeAdapter(Date::class.java, dateTypeAdapter)
            .setObjectToNumberStrategy {
                // Read the JSON number as string to inspect its format
                val numberAsString = it.nextString()

                if (numberAsString.contains('.')) numberAsString.toDouble()
                else numberAsString.toLong()
            }
            .serializeNulls()
        val gson = builder.create()
        val guestResponse = gson.fromJson(json, GuestResponse::class.java)
        eatManager.processGuest(guestResponse.guest, guestResponse.included)
        return guestResponse
    }
}