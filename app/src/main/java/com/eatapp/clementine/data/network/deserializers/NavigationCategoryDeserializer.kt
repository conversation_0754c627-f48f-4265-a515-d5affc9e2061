package com.eatapp.clementine.data.network.deserializers

import com.eatapp.clementine.data.network.response.apiresources.NavigationItemCategory
import com.eatapp.clementine.data.network.response.apiresources.NavigationItemModel
import com.eatapp.clementine.data.network.response.apiresources.NavigationItemSubCategory
import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.google.gson.JsonNull
import java.lang.reflect.Type

class NavigationCategoryDeserializer : JsonDeserializer<NavigationItemCategory> {
    override fun deserialize(
        json: JsonElement,
        typeOfT: Type,
        context: JsonDeserializationContext
    ): NavigationItemCategory {
        val jsonObject = json.asJsonObject

        val model = NavigationItemCategory()

        model.title = jsonObject.get("title")?.takeIf { it !is JsonNull }?.asString ?: ""
        model.icon = jsonObject.get("icon")?.takeIf { it !is JsonNull }?.asString ?: ""
        model.path = jsonObject.get("path")?.takeIf { it !is JsonNull }?.asString ?: ""

        if (!jsonObject.has("items")) {
            return model
        }

        val itemsJsonArray = jsonObject.getAsJsonArray("items")

        val tempItems: List<NavigationItemSubCategory>? = itemsJsonArray?.map {
            context.deserialize(it, NavigationItemSubCategory::class.java)
        }

        when {
            tempItems.isNullOrEmpty() -> return model
            tempItems.size == 1 -> {
                // If there's only one subcategory, use its items as the model's items
                model.items = tempItems[0].items
            }
            tempItems[0].items.isNullOrEmpty() -> {
                // If the first subcategory has no items, treat all entries as NavigationItemModel
                model.items = itemsJsonArray.map {
                    context.deserialize(it, NavigationItemModel::class.java)
                }
            }
            else -> {
                // Otherwise, it's a list of subcategories
                model.subCategories = tempItems
            }
        }

        return model
    }
}
