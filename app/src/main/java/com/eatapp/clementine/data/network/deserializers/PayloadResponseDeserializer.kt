package com.eatapp.clementine.data.network.deserializers

import android.util.Log
import com.eatapp.clementine.data.database.entities.ClosingPeriodEntity
import com.eatapp.clementine.data.database.entities.ConciergeEntity
import com.eatapp.clementine.data.database.entities.DayNoteEntity
import com.eatapp.clementine.data.database.entities.GuestEntity
import com.eatapp.clementine.data.database.entities.OnlineSeatingShiftEntity
import com.eatapp.clementine.data.database.entities.OrderEntity
import com.eatapp.clementine.data.database.entities.PaymentEntity
import com.eatapp.clementine.data.database.entities.PosRecordEntity
import com.eatapp.clementine.data.database.entities.ReservationEntity
import com.eatapp.clementine.data.database.entities.RestaurantServerEntity
import com.eatapp.clementine.data.database.entities.RestaurantUserEntity
import com.eatapp.clementine.data.database.entities.RoomEntity
import com.eatapp.clementine.data.database.entities.TableEntity
import com.eatapp.clementine.data.database.entities.TagEntity
import com.eatapp.clementine.data.network.body.Payload
import com.eatapp.clementine.data.network.body.SyncChangeType
import com.eatapp.clementine.data.network.body.SyncObjectType
import com.eatapp.clementine.internal.managers.SyncManager
import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import java.lang.reflect.Type

class PayloadDeserializer : JsonDeserializer<Payload?> {

    private val typeToClassMap = mapOf(
        SyncObjectType.RESERVATION.type to ReservationEntity::class.java,
        SyncObjectType.TABLE.type to TableEntity::class.java,
        SyncObjectType.GUEST.type to GuestEntity::class.java,
        SyncObjectType.CLOSING_PERIOD.type to ClosingPeriodEntity::class.java,
        SyncObjectType.CONCIERGE.type to ConciergeEntity::class.java,
        SyncObjectType.DAY_NOTE.type to DayNoteEntity::class.java,
        SyncObjectType.SHIFT.type to OnlineSeatingShiftEntity::class.java,
        SyncObjectType.PAYMENT.type to PaymentEntity::class.java,
        SyncObjectType.POS_RECORD.type to PosRecordEntity::class.java,
        SyncObjectType.RESTAURANT_SERVER.type to RestaurantServerEntity::class.java,
        SyncObjectType.USER.type to RestaurantUserEntity::class.java,
        SyncObjectType.ROOM.type to RoomEntity::class.java,
        SyncObjectType.ORDER.type to OrderEntity::class.java,
        SyncObjectType.TAG.type to TagEntity::class.java
    )

    override fun deserialize(
        json: JsonElement,
        typeOfT: Type,
        context: JsonDeserializationContext
    ): Payload? {

        val jsonObject = json.asJsonObject

        val changeType = jsonObject.get("change_type").asString
        val objectType = jsonObject.get("object_type").asString

        try {
            val data = typeToClassMap[objectType]?.let { clazz ->
                context.deserialize<Any>(jsonObject.getAsJsonObject("data"), clazz)
            }

            return Payload(
                changeType = SyncChangeType.fromType(changeType),
                objectType = SyncObjectType.fromType(objectType),
                data = data
            )
        } catch (e: Exception) {
            Log.e(SyncManager.LOG_TAG, "Failed to process payload for type $objectType ${jsonObject.getAsJsonObject("data")}", e)
            return null
        }
    }
}