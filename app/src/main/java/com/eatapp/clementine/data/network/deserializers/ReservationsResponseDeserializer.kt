package com.eatapp.clementine.data.network.deserializers

import com.eatapp.clementine.data.network.response.reservation.ReservationResponse
import com.eatapp.clementine.data.network.response.reservation.ReservationsResponse
import com.eatapp.clementine.internal.DateTypeAdapter
import com.eatapp.clementine.internal.managers.EatManager
import com.google.gson.GsonBuilder
import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import java.lang.reflect.Type
import java.util.Date
import javax.inject.Inject

class ReservationsResponseDeserializer @Inject constructor(
    private val eatManager: EatManager,
    private val dateTypeAdapter: DateTypeAdapter
) : JsonDeserializer<ReservationsResponse> {

    override fun deserialize(
        json: JsonElement?,
        typeOfT: Type?,
        context: JsonDeserializationContext?
    ): ReservationsResponse {
        val builder = GsonBuilder()
            .registerTypeAdapter(Date::class.java, dateTypeAdapter)
            .setObjectToNumberStrategy {
                // Read the JSON number as string to inspect its format
                val numberAsString = it.nextString()

                if (numberAsString.contains('.')) numberAsString.toDouble()
                else numberAsString.toLong()
            }
            .serializeNulls()
        val gson = builder.create()
        val reservationsResponse = gson.fromJson(json, ReservationsResponse::class.java)
        eatManager.processReservations(reservationsResponse)
        return reservationsResponse
    }
}

class ReservationResponseDeserializer @Inject constructor(
    private val eatManager: EatManager,
    private val dateTypeAdapter: DateTypeAdapter
) : JsonDeserializer<ReservationResponse> {

    override fun deserialize(
        json: JsonElement?,
        typeOfT: Type?,
        context: JsonDeserializationContext?
    ): ReservationResponse {
        val builder = GsonBuilder()
            .registerTypeAdapter(Date::class.java, dateTypeAdapter)
            .setObjectToNumberStrategy {
                // Read the JSON number as string to inspect its format
                val numberAsString = it.nextString()

                if (numberAsString.contains('.')) numberAsString.toDouble()
                else numberAsString.toLong()
            }
            .serializeNulls()
        val gson = builder.create()
        val reservationsResponse = gson.fromJson(json, ReservationResponse::class.java)
        eatManager.processReservation(reservationsResponse.reservation, reservationsResponse.included)
        return reservationsResponse
    }
}
