package com.eatapp.clementine.data.network.deserializers

import com.eatapp.clementine.data.network.response.IncludedResponse
import com.eatapp.clementine.data.network.response.restaurant.Restaurant
import com.eatapp.clementine.data.network.response.restaurant.RestaurantResponse
import com.eatapp.clementine.internal.managers.includedTypeGroupRestaurant
import com.google.gson.Gson
import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import java.lang.reflect.Type
import javax.inject.Inject

class RestaurantResponseDeserializer @Inject constructor() : JsonDeserializer<RestaurantResponse> {

    override fun deserialize(
        json: JsonElement?,
        typeOfT: Type?,
        context: JsonDeserializationContext?
    ): RestaurantResponse {
        val restaurantResponse = Gson().fromJson(json, RestaurantResponse::class.java)
        processRestaurant(restaurantResponse.data, restaurantResponse.included)
        return restaurantResponse
    }

    private fun processRestaurant(restaurant: Restaurant, included: List<IncludedResponse>) {
        included.filter { it.type == includedTypeGroupRestaurant && restaurant.relationships.groupRestaurants.data?.find { restaurant -> restaurant.id == it.id } != null }
            .forEach {
                val parsedRestaurant = Gson().fromJson(Gson().toJson(it), Restaurant::class.java)
                if (restaurant.groupRestaurants == null) {
                    restaurant.groupRestaurants = mutableListOf()
                }
                restaurant.groupRestaurants?.add(parsedRestaurant)
            }
    }
}