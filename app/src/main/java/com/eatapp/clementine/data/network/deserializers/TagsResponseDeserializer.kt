package com.eatapp.clementine.data.network.deserializers

import com.eatapp.clementine.data.network.response.tag.Tag
import com.eatapp.clementine.data.network.response.tag.TagsResponse
import com.google.gson.Gson
import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import java.lang.reflect.Type
import javax.inject.Inject

class TagsResponseDeserializer @Inject constructor() : JsonDeserializer<TagsResponse> {
    override fun deserialize(
        json: JsonElement?,
        typeOfT: Type?,
        context: JsonDeserializationContext?
    ): TagsResponse {
        val response = Gson().fromJson(json, TagsResponse::class.java)
        processTags(response.tags, response.included)
        return response
    }

    private fun processTags(tags: List<Tag>, included: List<Tag.TagCategoryModel>) {
        val categoryMap = included.associateBy { it.id }
        tags.forEach { tag ->
            tag.relationships?.tagCategory?.data?.id?.let {
                tag.category = categoryMap[it]
            }
        }
    }
}