package com.eatapp.clementine.data.network.interceptor

import android.content.Context
import android.content.Intent
import android.net.ConnectivityManager
import android.util.Log
import com.eatapp.clementine.BuildConfig
import com.eatapp.clementine.internal.Constants
import com.eatapp.clementine.internal.NoConnectivityException
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.ui.launch.LaunchActivity
import dagger.hilt.android.qualifiers.ApplicationContext
import okhttp3.Interceptor
import okhttp3.Response
import java.net.HttpURLConnection
import javax.inject.Inject

class EatInterceptorImpl @Inject constructor(
    @ApplicationContext private val context: Context, private val eatManager: EatManager
) : EatInterceptor {

    override fun intercept(chain: Interceptor.Chain): Response {

        if (!isOnline()) throw NoConnectivityException()

        val restaurantId = eatManager.restaurantId()

        val builder = chain.request().newBuilder()

        if (eatManager.token().isNotEmpty()) {
            builder.addHeader("Authorization", "Bearer ${eatManager.token()}")
        }

        builder.addHeader(
            "User-Agent",
            String.format("%s %s", BuildConfig.USER_AGENT, BuildConfig.VERSION_NAME)
        )

        if (chain.request().header(Constants.RESTAURANT_ID_HEADER) == null) {
            builder.addHeader(Constants.RESTAURANT_ID_HEADER, restaurantId)
        }

        val request = builder.build()

        val response = chain.proceed(request)

        // Redirect to login screen when token is expired
        if (response.code == 401) {
            eatManager.flush()
            val intent = Intent(context, LaunchActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            }
            context.startActivity(intent)
        }

        // Not so pretty solution to avoid Retrofit throwing NPE for 204 no-content body
        return if (response.code == 204) {
            response.newBuilder().code(200).build()
        } else {
            response
        }
    }

    private fun isOnline(): Boolean {

        val connectivityManager: ConnectivityManager =
            context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

        val networkInfo = connectivityManager.activeNetworkInfo

        return networkInfo != null && networkInfo.isConnected
    }
}