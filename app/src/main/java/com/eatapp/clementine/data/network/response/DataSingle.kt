package com.eatapp.clementine.data.network.response


import android.os.Parcelable
import androidx.room.TypeConverters
import com.eatapp.clementine.data.database.converters.DataConverter
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@TypeConverters(DataConverter::class)
@Parcelize
data class DataSingle(
    @SerializedName("data")
    val data: Data?
) : Parcelable