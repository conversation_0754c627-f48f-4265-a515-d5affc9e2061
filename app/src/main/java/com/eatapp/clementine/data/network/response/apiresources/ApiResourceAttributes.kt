package com.eatapp.clementine.data.network.response.apiresources

import com.eatapp.clementine.R
import com.google.gson.annotations.SerializedName

enum class NavigationItemType {
    @SerializedName("app")
    APP,
    @SerializedName("path")
    PATH
}

enum class NavigationItemNativeScreens(val path: String, val destination: Int) {
    FLOOR("floor", R.id.navigation_overview),
    GUESTS("guests", R.id.navigation_guests),
    REPORTS("reports", R.id.navigation_reports),
    PRINT_SETTINGS("layout-settings#printer", R.id.printer_fragment);

    companion object {
        fun destination(path: String): Int {
            return entries.first { it.path == path }.destination
        }
    }

}

data class ApiResourceAttributes(
    val endpoints: ApiResourcesEndpoint,
    val navigation: List<NavigationItemCategory>,
    val sources: List<Source>,
    val countries: List<Country>
)