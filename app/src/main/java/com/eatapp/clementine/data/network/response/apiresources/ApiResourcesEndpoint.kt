package com.eatapp.clementine.data.network.response.apiresources

import com.google.gson.annotations.SerializedName

data class ApiResourcesEndpoint (
    @SerializedName("restaurant_api")
    var restaurantEndpoint: EndpointsVersion,

    @SerializedName("payment_services_api")
    var paymentServicesEndpoint: EndpointsVersion,

    @SerializedName("pos_services_api")
    var posServicesEndpoint: EndpointsVersion,

    @SerializedName("hubspot_services_api")
    var hubspotServicesEndpoint: EndpointsVersion,

    @SerializedName ("websockets_api")
    var websocketEndpoint: ApiResourceWebsocket,

    @SerializedName("admin_dashboard")
    var adminEndpoint: EndpointBaseUrl,

    @SerializedName("messaging_api")
    var messagingEndpoint: EndpointsVersion
)
