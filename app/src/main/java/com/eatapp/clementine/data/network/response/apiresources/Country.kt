package com.eatapp.clementine.data.network.response.apiresources

import androidx.annotation.DrawableRes
import com.google.gson.annotations.SerializedName

data class Country(
    val name: String,
    val code: String,
    @SerializedName("country_code")
    val phonePrefix: String,
    @DrawableRes
    var icon: Int? = null,
    var selected: Boolean = false
) {
    val phoneCode: String
        get() {
            return "+$phonePrefix"
        }
}
