package com.eatapp.clementine.data.network.response.apiresources

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

data class NavigationItemCategory(
    var title: String,
    var icon: String?,
    var path: String,
    var subCategories: List<NavigationItemSubCategory>?,
    var items: List<NavigationItemModel>?,
    var expanded: Boolean = false,
    var selected: Boolean = false
) {
    constructor() : this("", "", "", null, null)
}

data class NavigationItemSubCategory(
    var title: String,
    var path: String,
    var items: List<NavigationItemModel>?
)

@Parcelize
data class NavigationItemModel(
    var title: String,
    var type: NavigationItemType,
    var path: String,
    var selected: Boolean = false
): Parcelable

