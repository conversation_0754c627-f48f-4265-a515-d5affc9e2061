package com.eatapp.clementine.data.network.response.apiresources

import com.eatapp.clementine.internal.labelized
import com.google.gson.annotations.SerializedName

data class Source(
    @SerializedName("source_type")
    val sourceType: String,
    @SerializedName("logo")
    var logo: String?,
    @SerializedName("name")
    val name: String
) {
    val displayName: String
        get() {
            return name.labelized
        }
}