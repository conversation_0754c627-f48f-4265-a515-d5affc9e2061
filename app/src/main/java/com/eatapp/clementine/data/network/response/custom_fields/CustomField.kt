package com.eatapp.clementine.data.network.response.custom_fields

import android.os.Parcelable
import com.eatapp.clementine.data.database.entities.CustomFieldEntity
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

data class CustomFieldResponse(
    @SerializedName("data")
    val customFields: List<CustomField>
)

data class CustomField(
    val id: String,
    val attributes: CustomFieldAttributes,
    var deleted: Boolean = false,
    var value: Any?
) {
    var label: String
        get() {
            return attributes.label
        }
        set(value) {
            attributes.label = value
        }

    val type: CustomFieldType?
        get() = attributes.type

    fun toEntity(restaurantId: String): CustomFieldEntity {
        return CustomFieldEntity(
            id = id,
            component = attributes.component,
            name = attributes.name,
            status = attributes.status,
            label = attributes.label,
            type = attributes.type,
            options = attributes.options,
            deleted = deleted,
            value = value?.toString(),
            restaurantId
        )
    }
}

data class CustomFieldAttributes(
    val component: CustomFieldComponent,
    val name: String,
    val status: String,
    var label: String,
    val type: CustomFieldType?,
    var options: List<String>?
)

enum class CustomFieldType {
    @SerializedName("text")
    TEXT,

    @SerializedName("boolean")
    BOOLEAN,

    @SerializedName("countable")
    COUNTABLE,

    @SerializedName("multi")
    MULTI
}

@Parcelize
enum class CustomFieldComponent : Parcelable {
    @SerializedName("reservation_custom_field")
    RESERVATION,

    @SerializedName("guest_custom_field")
    GUEST
}