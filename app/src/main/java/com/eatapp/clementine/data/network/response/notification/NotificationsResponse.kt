package com.eatapp.clementine.data.network.response.notification

import android.text.format.DateUtils
import com.eatapp.clementine.data.network.response.Meta
import com.eatapp.clementine.data.network.response.room.Table
import com.eatapp.clementine.internal.Status
import com.eatapp.clementine.internal.eatDateISO8601Timezone1DateFormatter
import com.eatapp.clementine.internal.isYesterday
import com.google.gson.annotations.SerializedName
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

data class NotificationsResponse(
    @SerializedName("data")
    val data: List<Notification>,
    @SerializedName("meta")
    val meta: Meta,
)

data class Notification(
    @SerializedName("id")
    val id: String,
    @SerializedName("type")
    val type: String,
    @SerializedName("attributes")
    val attributes: NotificationAttributes
) {

    private val notifiedAt: Date
        get() {
            return attributes.notifiedAt?.let {
                eatDateISO8601Timezone1DateFormatter().parse(it)
            } ?: Date()
        }

    val notifiedAtGroup: String
        get() {
            val dateFormat = SimpleDateFormat("MMM dd, yyyy", Locale.US)
            return when {
                DateUtils.isToday(notifiedAt.time) -> "Today"
                notifiedAt.isYesterday() -> "Yesterday"
                else -> dateFormat.format(notifiedAt)
            }
        }

    val reservation: NotificationReservation?
        get() {
           return attributes.reservation
        }
        
    val notifiedAtS: String
        get() {
            val now = Date()
            val diffInMillis = now.time - notifiedAt.time
            val diffInMinutes = diffInMillis / (1000 * 60)
            val diffInHours = diffInMinutes / 60

            return when {
                diffInMinutes < 60 -> "${diffInMinutes}m"
                diffInHours < 24 -> {
                    val remainingMinutes = diffInMinutes % 60
                    "${diffInHours}h ${remainingMinutes}m"
                }
                else -> {
                    val timeFormat = SimpleDateFormat("hh:mm a", Locale.US)
                    timeFormat.format(notifiedAt)
                }
            }
        }

    fun copyDeep(): Notification {
        return Notification(
            id = this.id,
            type = this.type,
            attributes = this.attributes.copyDeep()
        )
    }

}

data class NotificationAttributes(
    @SerializedName("title")
    val title: String,
    @SerializedName("message")
    val message: String,
    @SerializedName("notifiable_id")
    val notifiableId: String,
    @SerializedName("notifiable_type")
    val notifiableType: String,
    @SerializedName("actions")
    val actions: List<NotificationAction>,
    @SerializedName("notified_at")
    val notifiedAt: String?,
    @SerializedName("status")
    val status: String,
    @SerializedName("reservation")
    var reservation: NotificationReservation?
) {
    fun copyDeep(): NotificationAttributes {
        return NotificationAttributes(
            title = this.title,
            message = this.message,
            notifiableId = this.notifiableId,
            notifiableType = this.notifiableType,
            actions = this.actions,
            notifiedAt = this.notifiedAt,
            status = this.status,
            reservation = this.reservation?.copyDeep()
        )
    }
}

data class NotificationAction(
    val type: String,
    val url: String
)

data class NotificationReservation(
    @SerializedName("start_time")
    val startTime: Date,
    @SerializedName("guest_name")
    var guestName: String?,
    @SerializedName("covers")
    var covers: Int,
    @SerializedName("table_ids")
    var tableIds: List<String>,
    @SerializedName("status")
    var status: String,
    @SerializedName("source")
    var source: String?,
    @SerializedName("payment_status")
    var paymentStatus: String?,
    @SerializedName("no_show_count")
    var noShowCount: Int,
    @SerializedName("total_visits_count")
    var totalVisitsCount: Int,

    var animateStatusChange: Boolean = false,
    var tables: List<Table>?

) {

    val timeHourS: String
         get() {
            return SimpleDateFormat("hh:mm", Locale.US).format(startTime)
        }

    val timeMarkerS: String
        get() {
            return SimpleDateFormat("a", Locale.US).format(startTime)
        }

    val dateS: String
        get() {
            val dateFormat =  SimpleDateFormat("d MMM", Locale.US)
            return when {
                DateUtils.isToday(startTime.time) -> "Today"
                else -> dateFormat.format(startTime)
            }
        }

    val guestNameS: String
         get() {
             guestName?.let { return it } ?: run { return "--" }
        }

    val tablesS: String?
        get() {
            return if (tables?.isEmpty() == true) "--" else tables?.joinToString { it.number }
        }

    val statusIcon: Int
        get() { return Status.getIcon(status) }

    val statusColor: Int
        get() { return Status.getColor(status) }

    fun copyDeep(): NotificationReservation {
        return NotificationReservation(
            startTime = this.startTime,
            guestName = this.guestName,
            covers = this.covers,
            tableIds = this.tableIds,
            status = this.status,
            source = this.source,
            paymentStatus = this.paymentStatus,
            noShowCount = this.noShowCount,
            totalVisitsCount = this.totalVisitsCount,
            tables = this.tables
        )
    }
}