package com.eatapp.clementine.data.network.response.payment


import com.google.gson.annotations.SerializedName

data class PaymentRule(
    @SerializedName("attributes")
    val attributes: PaymentRuleAttributes,
    @SerializedName("id")
    val id: String,
    @SerializedName("type")
    val type: String
)

data class PaymentRuleAttributes(
    @SerializedName("amount")
    val amount: Double,
    @SerializedName("auto_cancel_period")
    val autoCancelPeriod: Int,
    @SerializedName("charge_type")
    val chargeType: String,
    @SerializedName("cover_threshold")
    val coverThreshold: Int,
    @SerializedName("name")
    val name: String,
    @SerializedName("rule_type")
    val ruleType: String,
    @SerializedName("status")
    val status: String,
    @SerializedName("terms")
    val terms: String
)