package com.eatapp.clementine.data.network.response.product

import com.google.gson.annotations.SerializedName

data class Message(
    @SerializedName("level")
    val level: ProductMessageLevel,
    @SerializedName("name")
    val name: ProductMessageType?,
    @SerializedName("priority")
    val priority: Int,
    @SerializedName("quota")
    val quota: Int,
    @SerializedName("pop_up")
    val popUp: Alert?,
    @SerializedName("banner")
    val banner: Alert?
)

enum class ProductMessageType(val type: String?) {
    @SerializedName("billing_blocked")
    BillingBlocked("billing_blocked"),
    @SerializedName("billing_warning")
    BillingWarning("billing_warning"),
    @SerializedName("subscription_expired")
    SubscriptionExpired("subscription_expired"),
    @SerializedName("subscription_expiring")
    SubscriptionExpiring("subscription_expiring"),
    @SerializedName("trial_expired")
    TrialExpired("trial_expired"),
    @SerializedName("trial_active")
    TrialActive("trial_active"),
    @SerializedName("covers_exceeded")
    CoversExceeded("covers_exceeded"),
    @SerializedName("covers_approaching")
    CoversApproaching("covers_approaching"),
    @SerializedName("sms_exceeded")
    SmsExceeded("sms_exceeded"),
    @SerializedName("sms_approaching")
    SmsApproaching("sms_approaching"),
    @SerializedName("upgrade")
    Upgrade("upgrade")
}

enum class ProductMessageLevel(val level: String) {
    @SerializedName("warning")
    Warning("warning"),
    @SerializedName("alert")
    Alert("alert")
}