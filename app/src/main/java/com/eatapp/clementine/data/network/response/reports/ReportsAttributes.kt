package com.eatapp.clementine.data.network.response.reports


import com.google.gson.annotations.SerializedName

data class ReportsAttributes(
    @SerializedName("by_source")
    val bySource: BySource,
    @SerializedName("by_status")
    val byStatus: ByStatus,
    @SerializedName("total_reservations")
    val totalReservations: TotalReservations,
    @SerializedName("walkins")
    val walkins: Walkins
)