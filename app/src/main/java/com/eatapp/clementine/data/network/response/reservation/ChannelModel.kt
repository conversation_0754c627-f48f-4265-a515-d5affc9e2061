package com.eatapp.clementine.data.network.response.reservation

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class ChannelModel(
    val id: String,
    @SerializedName("attributes")
    val attributes: ChannelAttributes
) : Parcelable {
    val displayName: String?
        get() {
            return attributes.displayName
        }
}