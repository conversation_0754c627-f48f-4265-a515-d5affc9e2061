package com.eatapp.clementine.data.network.response.reservation


import android.os.Parcelable
import androidx.room.Ignore
import com.eatapp.clementine.data.network.response.comment.CommentModel
import com.eatapp.clementine.internal.ceilToQuarter
import com.eatapp.clementine.internal.formatISO8601Timezone
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize
import kotlinx.parcelize.RawValue
import java.util.Date

@Parcelize
data class ReservationAttributes(
    @SerializedName("covers")
    var covers: Int,
    @SerializedName("created_at")
    val createdAt: String,
    @SerializedName("created_by")
    var createdBy: String?,
    @SerializedName("duration")
    var duration: Int,
    @SerializedName("key")
    val key: String?,
    @SerializedName("source")
    var source: String?,
    @SerializedName("notes")
    var notes: String?,
    @SerializedName("online")
    val online: <PERSON><PERSON><PERSON>,
    @SerializedName("start_time")
    var startTime: Date,
    @SerializedName("status")
    var status: String,
    @SerializedName("guest_name")
    var tempName: String?,
    @SerializedName("tags")
    var tags: MutableList<String>?,
    @SerializedName("updated_at")
    val updatedAt: String?,
    @SerializedName("walk_in")
    var walkIn: Boolean,
    @SerializedName("lock_status")
    var lockStatus: String,
    @SerializedName("wait_list_duration")
    var waitQuote: Int,
    @SerializedName("wait_list_queued_at")
    var waitlistQueuedAt: Date?,
    @SerializedName("wait_list_table_ready_at")
    var waitlistTableReadyAt: String?,
    @SerializedName("wait_list_status")
    var waitlistStatus: String?,
    @Ignore
    @SerializedName("review")
    var review: ReviewModel?,
    @Ignore
    @SerializedName("comments")
    var comments: MutableList<CommentModel>?,
    @SerializedName("restaurant_id")
    val restaurantId: String,
    @SerializedName("custom_fields")
    var customFields: MutableMap<String, @RawValue Any>,
    @SerializedName("edited_by_id")
    var editedBy: String?,
    @SerializedName("wait_list_position")
    val waitlistPosition: Int? = null,
    @SerializedName("status_history")
    val statusHistory: MutableList<StatusHistoryModel>?,
    @SerializedName("demo")
    val demo: Boolean = false
) : Parcelable {

    constructor(date: Date?) : this(
        2,  Date().formatISO8601Timezone(), null, 5400, "", null,
        "", false, ceilToQuarter(date ?: Date()), "not_confirmed", "",
        mutableListOf(),  Date().formatISO8601Timezone(), false, "", 0, ceilToQuarter(date ?: Date()), null,
        "", null, mutableListOf(), "", hashMapOf(), null, null, mutableListOf()
    )
}