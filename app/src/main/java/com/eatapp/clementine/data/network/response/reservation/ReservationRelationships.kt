package com.eatapp.clementine.data.network.response.reservation


import android.os.Parcelable
import androidx.room.TypeConverters
import com.eatapp.clementine.data.database.converters.DataMultipleConverter
import com.eatapp.clementine.data.database.converters.DataSingleConverter
import com.eatapp.clementine.data.network.response.DataMultiple
import com.eatapp.clementine.data.network.response.DataSingle
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@TypeConverters(DataSingleConverter::class, DataMultipleConverter::class)
@Parcelize
data class ReservationRelationships(
    @SerializedName("guest")
    val guest: DataSingle?,
    @SerializedName("channel")
    val channel: DataSingle?,
    @SerializedName("tables")
    val tables: DataMultiple?,
    @SerializedName("taggings")
    val taggings: DataMultiple?,
    @SerializedName("payments")
    val payments: DataMultiple?,
    @SerializedName("pos_record")
    val posRecord: DataSingle?,
    @SerializedName("concierge")
    val concierge: DataSingle? = null,
    @SerializedName("voucher_assignments")
    val voucherAssignments: DataMultiple?
) : Parcelable {
    constructor(): this(null, null, null, null, null, null, null, null)
}