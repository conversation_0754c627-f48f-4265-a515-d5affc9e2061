package com.eatapp.clementine.data.network.response.restaurant

import com.google.gson.annotations.SerializedName

data class ReservationStatus(
    @SerializedName("code")
    val code: String,
    @SerializedName("color")
    val color: String,
    @SerializedName("for_past_reservation")
    val forPastReservation: <PERSON><PERSON><PERSON>,
    @SerializedName("for_reservation_day")
    val forReservationDay: <PERSON><PERSON><PERSON>,
    @SerializedName("freemium")
    val freemium: <PERSON><PERSON><PERSON>,
    @SerializedName("icon")
    val icon: String,
    @SerializedName("name")
    val name: String,
    @SerializedName("next_statuses")
    val nextStatuses: List<ReservationStatus>,
    @SerializedName("position")
    val position: Int,
    @SerializedName("status")
    val useStatus: UseStatus
)

enum class UseStatus {
    @SerializedName("active")
    ACTIVE,
    @SerializedName("hidden")
    HIDDEN
}