package com.eatapp.clementine.data.network.response.restaurant


import com.eatapp.clementine.data.network.response.product.Message

data class Restaurant(
    val id: String,
    val type: String,
    val attributes: RestaurantAttributes,
    val relationships: RestaurantRelationships,
    var groupRestaurants: MutableList<Restaurant>?,
    var isSelected: Boolean
) {

    companion object {
        const val externalPaymentGateway = "external"
    }

    val contactEmail: String
        get() {
            return attributes.contactEmail
        }

    val countryCode: String
        get() {
            return attributes.countryCode
        }

    val currency: String?
        get() {
            return attributes.currency
        }

    val customTags: List<String>
        get() {
            return attributes.customTags
        }

    val imageUrl: String?
        get() {
            return attributes.imageUrl
        }

    val name: String
        get() {
            return attributes.name
        }

    val phone: String
        get() {
            return attributes.phone
        }

    val posActive: Boolean
        get() {
            return attributes.posActive
        }

    val paymentsActivated: Boolean
        get() {
            return attributes.paymentsActivated
        }

    val relationshipType: String
        get() {
            return attributes.relationshipType
        }

    val signUpState: String
        get() {
            return attributes.signUpState
        }

    val timeZoneName: String
        get() {
            return attributes.timeZoneName
        }

    val uiPreferences: List<Any>
        get() {
            return attributes.uiPreferences
        }

    val widgetUrl: Any
        get() {
            return attributes.widgetUrl
        }

    val reviewsActive: Boolean
        get() {
            return attributes.reviewsActive
        }

    val ordersActive: Boolean
        get() {
            return attributes.ordersActive
        }

    val productMessages: List<Message>
        get() {
            return attributes.productMessages
        }

    val accountState: AccountStateType
        get() {
            return attributes.accountState.block?.let { AccountStateType.valueOf(it.uppercase()) }
                ?: AccountStateType.ACTIVE
        }

    val address: String
        get() {
            val addressArray: MutableList<String> = mutableListOf()
            attributes.address1?.let { if (it.isNotBlank()) addressArray.add(it) }
            attributes.neighborhood?.let { if (it.isNotBlank()) addressArray.add(it) }
            attributes.region?.let { if (it.isNotBlank()) addressArray.add(it) }
            return addressArray.joinToString(", ")
        }

    val isGroupRestaurant: Boolean
        get() {
            return !groupRestaurants.isNullOrEmpty()
        }

    val marketingOptInVisibility: Boolean
        get() {
            return attributes.marketingOptIn?.visibilty ?: false
        }

    val marketingOptInDefaultValue: Boolean
        get() {
            return attributes.marketingOptIn?.defaultValue ?: false
        }

    val statusCategories: List<ReservationStatusCategory>
        get() {
            return attributes.statusesCategories ?: listOf()
        }

    val loyaltyEnabled: Boolean
        get() = attributes.loyaltyEnabled

    val vouchersEnabled: Boolean
        get() = attributes.vouchersEnabled

    val smsStatus: MessagingStatus
        get() = MessagingStatus.valueOf((attributes.flags?.smsStatus ?: "disabled").uppercase())

    val whatsappStatus: MessagingStatus
        get() = MessagingStatus.valueOf((attributes.flags?.whatsappStatus ?: "disabled").uppercase())
}

enum class AccountStateType(val type: String?) {
    EVERYTHING("everything"),
    IN_HOUSE("in_house"),
    ACTIVE(null)
}

enum class MessagingStatus(val value: String) {
    DISABLED("disabled"),
    QUOTA_ENABLED("quota_enabled"), 
    QUOTA_APPROACHING("quota_approaching"),
    QUOTA_EXCEEDED("quota_exceeded"),
    ALWAYS_ENABLED("always_enabled"),
    BLOCKED("blocked")
}
