package com.eatapp.clementine.data.network.response.restaurant


import com.eatapp.clementine.data.network.response.product.Message
import com.google.gson.annotations.SerializedName

data class RestaurantAttributes(
    @SerializedName("contact_email")
    val contactEmail: String,
    @SerializedName("country_code")
    val countryCode: String,
    @SerializedName("currency")
    val currency: String?,
    @SerializedName("custom_tags")
    val customTags: List<String>,
    @SerializedName("image_url")
    val imageUrl: String?,
    @SerializedName("name")
    val name: String,
    @SerializedName("phone")
    val phone: String,
    @SerializedName("pos_active")
    val posActive: Boolean,
    @SerializedName("relationship_type")
    val relationshipType: String,
    @SerializedName("sign_up_state")
    val signUpState: String,
    @SerializedName("time_zone_name")
    val timeZoneName: String,
    @SerializedName("ui_preferences")
    val uiPreferences: List<Any>,
    @SerializedName("widget_url")
    val widgetUrl: String,
    @SerializedName("reviews_active")
    val reviewsActive: Boolean,
    @SerializedName("orders_activated")
    val ordersActive: Boolean,
    @SerializedName("product_messages")
    val productMessages: List<Message>,
    @SerializedName("account_state")
    val accountState: AccountState,
    @SerializedName("country")
    val country: String,
    @SerializedName("payments_activated")
    val paymentsActivated: Boolean,
    @SerializedName("payment_gateway")
    val paymentGateway: String?,
    @SerializedName("booking_fee_rate")
    val bookingFeeRate: Double?,
    @SerializedName("address_line_1")
    val address1: String?,
    @SerializedName("address_line_2")
    val address2: String?,
    @SerializedName("neighborhood")
    val neighborhood: String?,
    @SerializedName("region")
    val region: String?,
    @SerializedName("flags")
    val flags: Flags?,
    @SerializedName("marketing_opt_in")
    val marketingOptIn: MarketingOptIn?,
    @SerializedName("statuses")
    val statusesCategories: List<ReservationStatusCategory>?,
    @SerializedName("loyalty_enabled")
    val loyaltyEnabled: Boolean,
    @SerializedName("vouchers_enabled")
    val vouchersEnabled: Boolean
)

data class Flags(
    @SerializedName("sms_status")
    val smsStatus: String?,
    @SerializedName("whatsapp_status")
    val whatsappStatus: String?
)