package com.eatapp.clementine.data.network.response.restaurant

import com.eatapp.clementine.data.network.response.DataMultiple
import com.eatapp.clementine.data.network.response.DataSingle
import com.google.gson.annotations.SerializedName

data class RestaurantRelationships(
    val region: DataSingle,
    val cuisine: DataSingle,
    val neighborhood: DataSingle,
    @SerializedName("group_restaurants")
    val groupRestaurants: DataMultiple
)
