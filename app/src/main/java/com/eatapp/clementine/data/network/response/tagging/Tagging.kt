package com.eatapp.clementine.data.network.response.tagging

import android.os.Parcelable
import com.eatapp.clementine.data.database.entities.TaggingEntity
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class Tagging(
    @SerializedName("attributes")
    val attributes: TaggingAttributes,
    @SerializedName("id")
    val id: String?,
    @SerializedName("type")
    val type: String,

    var color: String?

) : Parcelable {

    constructor() : this(TaggingAttributes("", "", "", ""), "", "", "")

    val category: String?
        get() {
            return attributes.category
        }

    val icon: String?
        get() {
            return attributes.icon
        }

    val name: String
        get() {
            return attributes.name
        }

    fun toEntity(): TaggingEntity {
        return TaggingEntity(
            category = this.attributes.category ?: "",
            icon = this.attributes.icon ?: "",
            id = this.id,
            name = this.attributes.name,
            tagger = this.attributes.tagger,
            color = this.color
        )
    }
}