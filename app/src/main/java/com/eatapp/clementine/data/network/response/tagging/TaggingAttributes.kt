package com.eatapp.clementine.data.network.response.tagging

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class TaggingAttributes(
    @SerializedName("category")
    val category: String?,
    @SerializedName("icon")
    val icon: String?,
    @SerializedName("name")
    val name: String,
    @SerializedName("tagger")
    val tagger: String?
) : Parcelable