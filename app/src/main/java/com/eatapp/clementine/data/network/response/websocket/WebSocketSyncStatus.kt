package com.eatapp.clementine.data.network.response.websocket

import com.eatapp.clementine.data.network.body.SyncStatus
import com.eatapp.clementine.internal.managers.StreamIdentifier
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName

data class WebSocketSyncStatus(
    @SerializedName("identifier")
    val streamIdentifierRaw: String?,
    val message: Message?
) {
    fun parseStreamIdentifier(): StreamIdentifier {
        return Gson().fromJson(streamIdentifierRaw, StreamIdentifier::class.java)
    }
}

data class Message(
    @SerializedName("sync_id")
    val syncId: String?,
    val status: SyncStatus?
)
