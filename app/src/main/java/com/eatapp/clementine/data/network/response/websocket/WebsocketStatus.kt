package com.eatapp.clementine.data.network.response.websocket

import com.google.gson.annotations.SerializedName

data class WebsocketStatus(
    @SerializedName("Action")
    val action: Int,
    @SerializedName("Guest")
    val guest: Int,
    @SerializedName("MessageTemplate")
    val messageTemplate: Int,
    @SerializedName("WhatsAppTemplate")
    val whatsappTemplate: Int,
    @SerializedName("OnlineSeatingShift")
    val onlineSeatingShift: Int,
    @SerializedName("Reservation")
    val reservation: Int,
    @SerializedName("RestaurantUser")
    val restaurantUser: Int,
    @SerializedName("RestaurantServer")
    val restaurantServer: Int,
    @SerializedName("Room")
    val room: Int,
    @SerializedName("Table")
    val table: Int,
    @SerializedName("Tag")
    val tag: Int,
    @SerializedName("DayNote")
    val dayNote: Int,
    @SerializedName("UpdatedAt")
    val updatedAt: Int,
    @SerializedName("Payment")
    val payment: Int,
    @SerializedName("Conversation")
    val conversation: Int,
    @SerializedName("Voucher")
    val voucher: Int
)