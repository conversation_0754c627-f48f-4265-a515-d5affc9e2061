package com.eatapp.clementine.data.repository

import com.eatapp.clementine.data.network.body.ClosingBody
import com.eatapp.clementine.data.network.response.closing.Closing
import kotlinx.coroutines.flow.Flow
import java.util.Date

interface ClosingsRepository {
    suspend fun closings(date: Date): Flow<List<Closing>>
    suspend fun insertClosing(closingBody: ClosingBody)
    suspend fun deleteClosing(closingId: String)
}