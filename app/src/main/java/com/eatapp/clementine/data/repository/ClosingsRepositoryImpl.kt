package com.eatapp.clementine.data.repository

import com.eatapp.clementine.data.datasource.closing.ClosingDataSource
import com.eatapp.clementine.data.datasource.closing.RemoteClosingDataSource
import com.eatapp.clementine.data.datasource.closing.RoomClosingDataSource
import com.eatapp.clementine.data.network.body.ClosingBody
import com.eatapp.clementine.data.network.response.closing.Closing
import com.eatapp.clementine.internal.managers.EatManager
import kotlinx.coroutines.flow.Flow
import java.util.Date
import javax.inject.Inject

class ClosingsRepositoryImpl @Inject constructor(
    private val remoteDataSource: RemoteClosingDataSource,
    private val roomDataSource: RoomClosingDataSource,
    private val eatManager: EatManager
) : ClosingsRepository {

    private val activeDataSource: ClosingDataSource
        get() = if (eatManager.offlineMode) roomDataSource else remoteDataSource

    override suspend fun closings(date: Date): Flow<List<Closing>> {
        return activeDataSource.closings(date)
    }

    override suspend fun insertClosing(closingBody: ClosingBody) {
        activeDataSource.insertClosing(closingBody)
    }

    override suspend fun deleteClosing(closingId: String) {
        activeDataSource.deleteClosing(closingId)
    }
}