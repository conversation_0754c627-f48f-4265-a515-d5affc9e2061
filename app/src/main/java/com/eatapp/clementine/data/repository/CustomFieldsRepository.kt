package com.eatapp.clementine.data.repository

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.eatapp.clementine.data.network.response.custom_fields.CustomField

interface CustomFieldsRepository: CacheableRepository {
    val customFields: LiveData<List<CustomField>>
    fun reservationCustomFields(): List<CustomField>
    fun guestCustomFields(): List<CustomField>
}