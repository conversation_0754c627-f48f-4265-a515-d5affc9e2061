package com.eatapp.clementine.data.repository

import androidx.lifecycle.MutableLiveData
import com.eatapp.clementine.data.database.dao.CustomFieldsDao
import com.eatapp.clementine.data.network.response.custom_fields.CustomField
import com.eatapp.clementine.data.network.response.custom_fields.CustomFieldComponent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import javax.inject.Inject

class CustomFieldsRepositoryImpl @Inject constructor(
    private val customFieldsDao: CustomFieldsDao
) : CustomFieldsRepository {

    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var collectingJob: Job? = null

    override val customFields = MutableLiveData<List<CustomField>>()

    override fun startCollecting(restaurantId: String) {
        collectingJob?.cancel()

        collectingJob = customFieldsDao.customFieldsFlow(restaurantId)
            .distinctUntilChanged()
            .onEach {
                customFields.postValue(it.map { it.toCustomField() }.toMutableList())
            }
            .launchIn(scope)
    }

    override fun reservationCustomFields(): List<CustomField> {
        return customFields.value?.filter { it.attributes.component == CustomFieldComponent.RESERVATION }
            ?: emptyList()
    }

    override fun guestCustomFields(): List<CustomField> {
        return customFields.value?.filter { it.attributes.component == CustomFieldComponent.GUEST }
            ?: emptyList()
    }
}