package com.eatapp.clementine.data.repository

import com.eatapp.clementine.data.network.response.daynote.DayNoteData
import kotlinx.coroutines.flow.Flow
import java.util.Date

interface DayNoteRepository {
    fun dayNote(date: Date): Flow<List<DayNoteData>>
    suspend fun insertDayNote(date: Date, content: String): DayNoteData
    suspend fun updateDayNote(noteId: String, content: String): DayNoteData
}