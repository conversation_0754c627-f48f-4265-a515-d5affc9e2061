package com.eatapp.clementine.data.repository

import com.eatapp.clementine.data.datasource.daynote.DayNoteDataSource
import com.eatapp.clementine.data.datasource.daynote.RemoteDayNoteDataSource
import com.eatapp.clementine.data.datasource.daynote.RoomDayNoteDataSource
import com.eatapp.clementine.data.network.response.daynote.DayNoteData
import com.eatapp.clementine.internal.managers.EatManager
import kotlinx.coroutines.flow.Flow
import java.util.Date
import javax.inject.Inject

class DayNoteRepositoryImpl @Inject constructor(
    private val remoteDataSource: RemoteDayNoteDataSource,
    private val roomDataSource: RoomDayNoteDataSource,
    private val eatManager: EatManager
) : DayNoteRepository {

    private val activeDataSource: DayNoteDataSource
        get() = if (eatManager.offlineMode) roomDataSource else remoteDataSource

//    override suspend fun dayNote(date: Date): DayNotesResponse
//            = eatApiService.dayNoteAsync(simpleDate(date))
//
//    override suspend fun insertDayNote(date: Date, content: String): DayNoteResponse
//            = eatApiService.insertDayNoteAsync(simpleDate(date), content)
//
//    override suspend fun updateDayNote(noteId: String, content: String): DayNoteResponse
//            = eatApiService.updateDayNoteAsync(noteId, content)

    //    override fun dayNotes(restaurantId: String, date: Date): Flow<List<DayNote>> {
//        return dayNoteDao.getDayNotes(restaurantId, date)
//    }
//
//    override suspend fun updateDayNote(dayNote: DayNote) {
//        dayNoteDao.updateDayNote(dayNote)
//    }
//
//    override suspend fun deleteDayNote(dayNote: DayNote) {
//        dayNoteDao.deleteDayNote(dayNote)
//    }
//
//    override suspend fun saveDayNotes(dayNotes: List<DayNote>) {
//        dayNoteDao.saveDayNotes(dayNotes)
//    }
    override fun dayNote(date: Date): Flow<List<DayNoteData>> {
        return activeDataSource.dayNote(date)
    }

    override suspend fun insertDayNote(date: Date, content: String): DayNoteData {
        return activeDataSource.insertDayNote(date, content)
    }

    override suspend fun updateDayNote(noteId: String, content: String): DayNoteData {
        return activeDataSource.updateDayNote(noteId, content)
    }
}