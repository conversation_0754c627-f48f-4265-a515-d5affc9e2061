package com.eatapp.clementine.data.repository

import com.eatapp.clementine.data.network.body.RegisterBody
import com.eatapp.clementine.data.network.response.auth.AuthResponse
import com.eatapp.clementine.data.network.response.custom_fields.CustomFieldResponse
import com.eatapp.clementine.data.network.response.guest.GuestTagsResponse
import com.eatapp.clementine.data.network.response.pizzaslicer.PizzaSlicerResponse
import com.eatapp.clementine.data.network.response.restaurant.RestaurantResponse
import com.eatapp.clementine.data.network.response.restaurant.RestaurantsResponse
import com.eatapp.clementine.data.network.response.room.RoomsResponse
import com.eatapp.clementine.data.network.response.room.TablesResponse
import com.eatapp.clementine.data.network.response.shift.ShiftsResponse
import com.eatapp.clementine.data.network.response.tag.TagsResponse
import com.eatapp.clementine.data.network.response.templates.WhatsappTemplatesResponse
import com.eatapp.clementine.data.network.response.user.UserResponse
import com.eatapp.clementine.data.network.response.user.UsersResponse

interface FabricateRepository {

    suspend fun login(email: String, password: String, captchaToken: String): AuthResponse

    suspend fun twoFactor(token: String, otp: String): AuthResponse

    suspend fun register(registerBody: RegisterBody): AuthResponse

    suspend fun restaurants(): RestaurantsResponse

    suspend fun restaurant(restaurantId: String): RestaurantResponse

    suspend fun rooms(restaurantId: String)

    suspend fun tables(restaurantId: String)

    suspend fun user(restaurantId: String, userId: String): UserResponse

    suspend fun users(restaurantId: String)

    suspend fun tags(restaurantId: String)

    suspend fun shifts(restaurantId: String)

    suspend fun pizzaSlicer(): PizzaSlicerResponse

    suspend fun customFields(restaurantId: String)

    suspend fun messageTemplates(restaurantId: String)

    suspend fun whatsappTemplates(restaurantId: String)

    suspend fun vouchers(restaurantId: String)
}