package com.eatapp.clementine.data.repository

import com.eatapp.clementine.data.database.dao.CustomFieldsDao
import com.eatapp.clementine.data.database.dao.OnlineSeatingShiftDao
import com.eatapp.clementine.data.database.dao.RestaurantUserDao
import com.eatapp.clementine.data.database.dao.RoomDao
import com.eatapp.clementine.data.database.dao.TableDao
import com.eatapp.clementine.data.database.dao.TagDao
import com.eatapp.clementine.data.database.dao.TemplatesDao
import com.eatapp.clementine.data.database.dao.VoucherDao
import com.eatapp.clementine.data.network.EatApiRestaurant
import com.eatapp.clementine.data.network.body.RegisterBody
import com.eatapp.clementine.data.network.response.auth.AuthResponse
import com.eatapp.clementine.data.network.response.pizzaslicer.PizzaSlicerResponse
import com.eatapp.clementine.data.network.response.restaurant.RestaurantResponse
import com.eatapp.clementine.data.network.response.restaurant.RestaurantsResponse
import com.eatapp.clementine.data.network.response.user.UserResponse
import javax.inject.Inject

class FabricateRepositoryImpl @Inject constructor(
    private val eatApiRestaurant: EatApiRestaurant,
    private val tableDao: TableDao,
    private val shiftDao: OnlineSeatingShiftDao,
    private val userDao: RestaurantUserDao,
    private val roomDao: RoomDao,
    private val tagDao: TagDao,
    private val templatesDao: TemplatesDao,
    private val voucherDao: VoucherDao,
    private val customFieldsDao: CustomFieldsDao
) : FabricateRepository {

    override suspend fun login(
        email: String,
        password: String,
        captchaToken: String
    ): AuthResponse = eatApiRestaurant.loginAsync(email, password, captchaToken)

    override suspend fun register(registerBody: RegisterBody): AuthResponse =
        eatApiRestaurant.registerAsync(registerBody)

    override suspend fun restaurants(): RestaurantsResponse =
        eatApiRestaurant.restaurantsAsync(true, 9999)

    override suspend fun restaurant(restaurantId: String): RestaurantResponse =
        eatApiRestaurant.restaurantAsync(restaurantId)

    override suspend fun rooms(restaurantId: String) {
        val response = eatApiRestaurant.roomsAsync(restaurantId)
        roomDao.deleteRooms(restaurantId)
        roomDao.saveRooms(response.rooms.map { it.toEntity(restaurantId) })
    }

    override suspend fun tables(restaurantId: String) {
        val response = eatApiRestaurant.tablesAsync(restaurantId, 9999)
        tableDao.deleteTables(restaurantId)
        tableDao.saveTables(response.tables.map { it.toEntity(restaurantId) })
    }

    override suspend fun user(restaurantId: String, userId: String): UserResponse =
        eatApiRestaurant.userAsync(restaurantId, userId)

    override suspend fun users(restaurantId: String) {
        val response = eatApiRestaurant.users(restaurantId)
        userDao.deleteRestaurantUsers(restaurantId)
        userDao.saveRestaurantUsers(response.users.map { it.toEntity(restaurantId) })
    }

    override suspend fun tags(restaurantId: String) {
        val response = eatApiRestaurant.tagsAsync(9999)
        tagDao.deleteTags(restaurantId)
        tagDao.saveTags(response.tags.map { it.toEntity(restaurantId) })
    }

    override suspend fun shifts(restaurantId: String) {
        val response = eatApiRestaurant.shiftsAsync(restaurantId, "active", 999)
        shiftDao.deleteOnlineSeatingShifts(restaurantId)
        shiftDao.saveOnlineSeatingShifts(response.shifts.map { it.toOnlineSeatingShift(restaurantId) })
    }

    override suspend fun pizzaSlicer(): PizzaSlicerResponse = eatApiRestaurant.pizzaSliceAsync()

    override suspend fun customFields(restaurantId: String) {
        val response = eatApiRestaurant.customFields(restaurantId)
        customFieldsDao.deleteCustomFields(restaurantId)
        customFieldsDao.saveCustomFields(response.customFields.map { it.toEntity(restaurantId) })
    }

    override suspend fun twoFactor(token: String, otp: String): AuthResponse =
        eatApiRestaurant.loginTwoFactor("Bearer $token", otp)

    override suspend fun messageTemplates(restaurantId: String) {
        val response = eatApiRestaurant.messageTemplatesAsync(restaurantId)
        templatesDao.deleteMessageTemplates(restaurantId)
        templatesDao.saveMessageTemplates(response.data.map { it.toEntity(restaurantId) })
    }

    override suspend fun whatsappTemplates(restaurantId: String) {
        val response = eatApiRestaurant.whatsappTemplatesAsync(restaurantId)
        templatesDao.deleteWhatsappTemplates(restaurantId)
        templatesDao.saveWhatsappTemplates(response.templates.map { it.toEntity(restaurantId) })
    }

    override suspend fun vouchers(restaurantId: String) {
        val response = eatApiRestaurant.vouchers(restaurantId)
        voucherDao.deleteVouchers(restaurantId)
        voucherDao.saveVouchers(response.vouchers.map { it.toEntity(restaurantId) })
    }
}