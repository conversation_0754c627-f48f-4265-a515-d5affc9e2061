package com.eatapp.clementine.data.repository

import androidx.paging.PagingData
import com.eatapp.clementine.data.network.body.AssignVouchersRequest
import com.eatapp.clementine.data.network.body.GuestBody
import com.eatapp.clementine.data.network.body.RedeemVoucherAssignmentRequest
import com.eatapp.clementine.data.network.response.guest.Guest
import com.eatapp.clementine.data.network.response.guest.GuestResponse
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.network.response.tagging.Tagging
import kotlinx.coroutines.flow.Flow

interface GuestsRepository {

    suspend fun guests(query: String?, page: Int, limit: Int): List<Guest>

    suspend fun guest(guestId: String): Guest

    suspend fun updateGuest(
        guestId: String,
        guestBody: GuestBody,
        taggings: List<Tagging>?
    ): Guest

    suspend fun createGuest(guestBody: GuestBody): Guest

    suspend fun deleteGuest(guestId: String)

    suspend fun reservations(guestId: String): List<Reservation>

    suspend fun updateVouchersForGuest(
        guestId: String,
        body: AssignVouchersRequest
    ): GuestResponse

    suspend fun redeemVoucherForGuest(
        guestId: String,
        body: RedeemVoucherAssignmentRequest
    ): GuestResponse

    fun getPagingGuests(restaurantId: String, query: String?): Flow<PagingData<Guest>>

    // Used to trigger guests flow when db data changes, since the return type of paging source is not flow, and doesn't observe db
    // Custom paging source was implemented to support keyed pagination to avoid room-s COUNT query, which takes a lot of time
    fun invalidatePagingSource()
}