package com.eatapp.clementine.data.repository

import androidx.paging.PagingData
import com.eatapp.clementine.data.datasource.guest.GuestDataSource
import com.eatapp.clementine.data.datasource.guest.RemoteGuestDataSource
import com.eatapp.clementine.data.datasource.guest.RoomGuestDataSource
import com.eatapp.clementine.data.network.body.AssignVouchersRequest
import com.eatapp.clementine.data.network.body.GuestBody
import com.eatapp.clementine.data.network.body.RedeemVoucherAssignmentRequest
import com.eatapp.clementine.data.network.response.guest.Guest
import com.eatapp.clementine.data.network.response.guest.GuestResponse
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.network.response.tagging.Tagging
import com.eatapp.clementine.internal.managers.EatManager
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

class GuestsRepositoryImpl @Inject constructor(
    private val remoteDataSource: RemoteGuestDataSource,
    private val roomDataSource: RoomGuestDataSource,
    private val eatManager: EatManager
) : GuestsRepository {

    private val activeDataSource: GuestDataSource
        get() = if (eatManager.offlineMode) roomDataSource else remoteDataSource

    override suspend fun guests(query: String?, page: Int, limit: Int): List<Guest> {
        return activeDataSource.guests(query, page, limit)
    }

    override suspend fun guest(guestId: String): Guest {
        return remoteDataSource.guest(guestId)
    }

    override suspend fun updateGuest(
        guestId: String,
        guestBody: GuestBody,
        taggings: List<Tagging>?
    ): Guest {
        return activeDataSource.updateGuest(guestId, guestBody, taggings)
    }

    override suspend fun createGuest(guestBody: GuestBody): Guest {
        return activeDataSource.createGuest(guestBody)
    }

    override suspend fun deleteGuest(guestId: String) {
        activeDataSource.deleteGuest(guestId)
    }

    override suspend fun reservations(guestId: String): List<Reservation> =
        activeDataSource.reservations(guestId)

    override suspend fun updateVouchersForGuest(
        guestId: String,
        body: AssignVouchersRequest
    ): GuestResponse {
        return remoteDataSource.updateVouchersForGuest(
            guestId,
            body
        )
    }

    override suspend fun redeemVoucherForGuest(
        guestId: String,
        body: RedeemVoucherAssignmentRequest
    ): GuestResponse {
        return remoteDataSource.redeemVoucherForGuest(
            guestId,
            body
        )
    }


    override fun getPagingGuests(
        restaurantId: String,
        query: String?
    ): Flow<PagingData<Guest>> {
        return activeDataSource.paginatedGuests(restaurantId, query)
    }

    override fun invalidatePagingSource() {
        roomDataSource.invalidatePagingSource()
    }
}

