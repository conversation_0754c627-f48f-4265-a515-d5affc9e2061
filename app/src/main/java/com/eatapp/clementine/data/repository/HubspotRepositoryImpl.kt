package com.eatapp.clementine.data.repository

import com.eatapp.clementine.data.network.EatApiHubspotService
import com.eatapp.clementine.data.network.response.hubspot.HubspotResponse
import javax.inject.Inject

class HubspotRepositoryImpl @Inject constructor(
    private val eatApiHubspotService: EatApiHubspotService
) : HubspotRepository {

    override suspend fun token(): HubspotResponse
            = eatApiHubspotService.tokenAsync()
}

