package com.eatapp.clementine.data.repository

import androidx.lifecycle.LiveData
import com.eatapp.clementine.data.network.response.conversation.Conversation
import com.eatapp.clementine.data.network.response.message.MessagesResponse
import com.eatapp.clementine.data.network.response.templates.MessageTemplate
import com.eatapp.clementine.data.network.response.templates.WhatsappTemplate

interface MessagingRepository: CacheableRepository {

    val conversations: LiveData<List<Conversation>>
    val whatsappTemplates: LiveData<List<WhatsappTemplate>>
    val messageTemplates: LiveData<List<MessageTemplate>>

    suspend fun messages(
        restaurantId: String,
        guestId: String?,
        reservationId: String?
    ): MessagesResponse

    suspend fun sendSms(
        restaurantId: String,
        reservationId: String?,
        guestId: String?,
        templateId: String?,
        text: String?
    ): Any

    suspend fun sendEmail(
        restaurantId: String,
        reservationId: String?,
        guestId: String?,
        templateId: String?,
        text: String?
    ): Any  

    suspend fun sendWhatsapp(
        restaurantId: String,
        reservationId: String?,
        guestId: String?,
        templateId: String?,
        text: String?
    ): Any

    suspend fun markAsRead(
        restaurantId: String,
        guestId: String
    ): Any

    suspend fun loadConversations()
}