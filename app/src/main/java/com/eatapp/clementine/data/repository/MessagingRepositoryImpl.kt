package com.eatapp.clementine.data.repository

import androidx.lifecycle.MutableLiveData
import com.eatapp.clementine.data.database.dao.ConversationsDao
import com.eatapp.clementine.data.database.dao.TemplatesDao
import com.eatapp.clementine.data.network.EatApiMessaging
import com.eatapp.clementine.data.network.response.conversation.Conversation
import com.eatapp.clementine.data.network.response.message.MessagesResponse
import com.eatapp.clementine.data.network.response.templates.MessageTemplate
import com.eatapp.clementine.data.network.response.templates.WhatsappTemplate
import com.eatapp.clementine.internal.managers.EatManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import javax.inject.Inject

class MessagingRepositoryImpl @Inject constructor(
    private val eatApiMessaging: EatApiMessaging,
    private val conversationsDao: ConversationsDao,
    private val eatManager: EatManager,
    private val templatesDao: TemplatesDao
) : MessagingRepository {

    override val conversations = MutableLiveData<List<Conversation>>()
    override val whatsappTemplates = MutableLiveData<List<WhatsappTemplate>>()
    override val messageTemplates = MutableLiveData<List<MessageTemplate>>()

    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val collectingJobs = mutableMapOf<String, Job>()

    override fun startCollecting(restaurantId: String) {
        // Clear old jobs
        collectingJobs.values.forEach { it.cancel() }
        collectingJobs.clear()

        // WhatsApp templates
        collectAndPost(
            key = "whatsapp",
            flow = templatesDao.whatsappTemplatesFlow(restaurantId),
            post = { whatsappTemplates.postValue(it.map { t -> t.toWhatsappTemplate() }) }
        )

        // Message templates
        collectAndPost(
            key = "message",
            flow = templatesDao.messageTemplatesFlow(restaurantId),
            post = { messageTemplates.postValue(it.map { t -> t.toMessageTemplate() }) }
        )

        // Conversation templates
        collectAndPost(
            key = "conversation",
            flow = conversationsDao.conversationsFlow(restaurantId),
            post = { conversations.postValue(it.map { t -> t.toConversation() }) }
        )
    }

    private fun <T> collectAndPost(
        key: String,
        flow: Flow<List<T>>,
        post: (List<T>) -> Unit
    ) {
        val job = flow
            .distinctUntilChanged()
            .onEach { post(it) }
            .launchIn(scope)

        collectingJobs[key] = job
    }


    override suspend fun messages(
        restaurantId: String,
        guestId: String?,
        reservationId: String?
    ): MessagesResponse =
        eatApiMessaging.messages(restaurantId, guestId, reservationId)

    override suspend fun sendSms(
        restaurantId: String,
        reservationId: String?,
        guestId: String?,
        templateId: String?,
        text: String?
    ): Any = eatApiMessaging.sendSms(restaurantId, reservationId, guestId, templateId, text)

    override suspend fun sendEmail(
        restaurantId: String,
        reservationId: String?,
        guestId: String?,
        templateId: String?,
        text: String?
    ): Any = eatApiMessaging.sendEmail(restaurantId, reservationId, guestId, templateId, text)

    override suspend fun sendWhatsapp(
        restaurantId: String,
        reservationId: String?,
        guestId: String?,
        templateId: String?,
        text: String?
    ): Any = eatApiMessaging.sendWhatsapp(restaurantId, reservationId, guestId, templateId, text)

    override suspend fun markAsRead(restaurantId: String, guestId: String): Any =
        eatApiMessaging.markAsRead(restaurantId, guestId)

    override suspend fun loadConversations() {
        val conversations = eatApiMessaging.conversations().conversations
        conversationsDao.deleteConversations(eatManager.restaurantId())
        conversationsDao.saveConversations(conversations.map { it.toEntity(eatManager.restaurantId()) })
    }
}