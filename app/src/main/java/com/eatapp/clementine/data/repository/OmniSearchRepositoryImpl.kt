package com.eatapp.clementine.data.repository

import com.eatapp.clementine.data.network.EatApiRestaurant
import com.eatapp.clementine.data.network.response.omnisearch.OmniSearchResponse
import com.eatapp.clementine.data.network.response.tagging.Tagging
import com.eatapp.clementine.internal.managers.TagType
import javax.inject.Inject

class OmniSearchRepositoryImpl @Inject constructor(
    private val eatApiRestaurant: EatApiRestaurant,
    private val tagsRepository: TagRepository
) : OmniSearchRepository {

    override suspend fun search(query: String): OmniSearchResponse {
        val response = eatApiRestaurant.search(query)
        val reservations = response.reservations?.reservations
        val guests = response.guests?.guests

        reservations?.forEach {
            addTagColors(it.taggings, TagType.Reservation)
        }
        guests?.forEach {
            addTagColors(it.taggings, TagType.Guest)
        }
        return response
    }

    private fun addTagColors(
        taggings: List<Tagging>?,
        type: TagType
    ) {
        if (type == TagType.Reservation) {
            taggings?.forEach { tagging ->
                tagging.color = tagsRepository.reservationTags()
                    .firstOrNull { it.attributes.name == tagging.name }
                    ?.category?.attributes?.color
            }
        } else {
            taggings?.forEach { tagging ->
                tagging.color = tagsRepository.guestTags()
                    .firstOrNull { it.attributes.name == tagging.name }
                    ?.category?.attributes?.color
            }
        }
    }
}