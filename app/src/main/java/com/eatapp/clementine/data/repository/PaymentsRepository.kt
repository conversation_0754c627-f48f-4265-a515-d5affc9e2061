package com.eatapp.clementine.data.repository

import com.eatapp.clementine.data.network.body.CreatePaymentBody
import com.eatapp.clementine.data.network.body.EditPaymentBody
import com.eatapp.clementine.data.network.response.message.MessagesResponse
import com.eatapp.clementine.data.network.response.payment.Payment
import com.eatapp.clementine.data.network.response.payment.PaymentRulesResponse
import kotlinx.coroutines.flow.Flow

interface PaymentsRepository {
    fun loadPayment(paymentId: String): Flow<Payment>
    fun loadPayments(reservationId: String): Flow<List<Payment>>
    suspend fun updatePayment(paymentId: String, actionType: PaymentActionType)
    suspend fun refundPayment(
        paymentId: String,
        refundAmount: Double,
        userId: String,
        pin: String?
    ): Payment
    suspend fun sendReminder(paymentId: String): MessagesResponse
    suspend fun loadPaymentRules(): PaymentRulesResponse
    suspend fun createPayment(createPaymentBody: CreatePaymentBody): Payment
    suspend fun editPayment(paymentId: String, editPaymentBody: EditPaymentBody): Payment
}