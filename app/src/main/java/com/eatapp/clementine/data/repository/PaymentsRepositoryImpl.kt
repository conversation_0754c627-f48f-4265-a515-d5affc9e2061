package com.eatapp.clementine.data.repository

import com.eatapp.clementine.data.datasource.payments.PaymentDataSource
import com.eatapp.clementine.data.datasource.payments.RemotePaymentDataSource
import com.eatapp.clementine.data.datasource.payments.RoomPaymentDataSource
import com.eatapp.clementine.data.network.body.CreatePaymentBody
import com.eatapp.clementine.data.network.body.EditPaymentBody
import com.eatapp.clementine.data.network.response.message.MessagesResponse
import com.eatapp.clementine.data.network.response.payment.Payment
import com.eatapp.clementine.data.network.response.payment.PaymentRulesResponse
import com.eatapp.clementine.internal.managers.EatManager
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

enum class PaymentActionType(val path: String) {
    SEND_REMINDER("messages"),
    VOID("void"),
    CAPTURE("capture"),
    REFUND("refund"),
    CANCEL("cancel")
}

class PaymentsRepositoryImpl @Inject constructor(
    private val remoteDataSource: RemotePaymentDataSource,
    private val roomDataSource: RoomPaymentDataSource,
    private val eatManager: EatManager
) : PaymentsRepository {

    private val activeDataSource: PaymentDataSource
        get() = if (eatManager.offlineMode) roomDataSource else remoteDataSource

    override fun loadPayments(reservationId: String): Flow<List<Payment>> {
        return activeDataSource.payments(reservationId)
    }

    override fun loadPayment(paymentId: String): Flow<Payment> {
        return activeDataSource.payment(paymentId)
    }

    override suspend fun updatePayment(paymentId: String, actionType: PaymentActionType) {
        remoteDataSource.updatePayment(paymentId, actionType)
    }

    override suspend fun refundPayment(
        paymentId: String,
        refundAmount: Double,
        userId: String,
        pin: String?
    ): Payment {
        return remoteDataSource.refundPayment(
            paymentId,
            refundAmount,
            userId,
            pin
        ).payment
    }

    override suspend fun sendReminder(paymentId: String): MessagesResponse {
        return remoteDataSource.sendReminder(paymentId)
    }

    override suspend fun loadPaymentRules(): PaymentRulesResponse {
        return remoteDataSource.paymentRules()
    }

    override suspend fun createPayment(createPaymentBody: CreatePaymentBody): Payment {
        return remoteDataSource.createPayment(createPaymentBody).payment
    }

    override suspend fun editPayment(paymentId: String, editPaymentBody: EditPaymentBody): Payment {
        return remoteDataSource.editPayment(paymentId, editPaymentBody)
    }
}