package com.eatapp.clementine.data.repository

import com.eatapp.clementine.data.datasource.posrecord.PosRecordDataSource
import com.eatapp.clementine.data.datasource.posrecord.RemotePosRecordDataSource
import com.eatapp.clementine.data.datasource.posrecord.RoomPosRecordDataSource
import com.eatapp.clementine.data.network.response.pos.PosRecord
import com.eatapp.clementine.internal.managers.EatManager
import java.util.Date
import javax.inject.Inject

class PosRepositoryImpl @Inject constructor(
    private val remoteDataSource: RemotePosRecordDataSource,
    private val roomDataSource: RoomPosRecordDataSource,
    private val eatManager: EatManager
) : PosRepository {

    private val activeDataSource: PosRecordDataSource
        get() = if (eatManager.offlineMode) roomDataSource else remoteDataSource

    override suspend fun posRecord(posRecordId: String): PosRecord?
            = activeDataSource.posRecord(posRecordId)

    override suspend fun posRecords(date: Date): List<PosRecord>
            = activeDataSource.posRecords(date)
}