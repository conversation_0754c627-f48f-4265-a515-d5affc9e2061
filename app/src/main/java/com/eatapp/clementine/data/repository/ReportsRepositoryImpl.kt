package com.eatapp.clementine.data.repository

import com.eatapp.clementine.data.network.EatApiRestaurant
import com.eatapp.clementine.data.network.response.reports.ReportsResponse
import com.eatapp.clementine.internal.simpleDate
import java.util.Date
import javax.inject.Inject

class ReportsRepositoryImpl @Inject constructor(
    private val eatApiRestaurant: EatApiRestaurant
) : ReportsRepository {

    override suspend fun reports(from: Date, to: Date): ReportsResponse =
        eatApiRestaurant.reportsAsync(simpleDate(from), simpleDate(to))

}