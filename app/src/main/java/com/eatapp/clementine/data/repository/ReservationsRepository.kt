package com.eatapp.clementine.data.repository

import com.eatapp.clementine.data.network.body.AssignVouchersRequest
import com.eatapp.clementine.data.network.body.RedeemVoucherAssignmentRequest
import com.eatapp.clementine.data.network.body.ReservationBody
import com.eatapp.clementine.data.network.body.StatusBody
import com.eatapp.clementine.data.network.response.notification.NotificationsResponse
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.network.response.reservation.ReservationResponse
import com.eatapp.clementine.data.network.response.survey.SurveyData
import com.eatapp.clementine.data.network.response.tagging.Tagging
import kotlinx.coroutines.flow.Flow
import java.util.Date

interface ReservationsRepository {

    fun reservations(date: Date): Flow<List<Reservation>>

    suspend fun reservation(restaurantId: String, eatId: String): Reservation

    suspend fun updateReservation(
        restaurantId: String,
        reservationId: String,
        reservationBody: ReservationBody,
        taggings: List<Tagging>?
    ): Reservation

    suspend fun updateReservationStatus(
        restaurantId: String,
        reservationId: String,
        reservationBody: StatusBody
    )

    suspend fun updateVouchersForReservation(
        restaurantId: String,
        reservationId: String,
        body: AssignVouchersRequest
    ): ReservationResponse

    suspend fun redeemVoucherForReservation(
        restaurantId: String,
        reservationId: String,
        body: RedeemVoucherAssignmentRequest
    ): ReservationResponse

    suspend fun createReservation(
        reservationBody: ReservationBody,
        taggings: List<Tagging>?
    ): Reservation

    suspend fun deleteReservation(reservationId: String)

    suspend fun surveys(): SurveyData

    suspend fun tableReady(reservationId: String): ReservationResponse

    suspend fun notifications(page: Int, limit: Int): NotificationsResponse
}