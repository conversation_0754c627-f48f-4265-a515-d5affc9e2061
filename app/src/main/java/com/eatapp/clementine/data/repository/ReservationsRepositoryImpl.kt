package com.eatapp.clementine.data.repository

import com.eatapp.clementine.data.datasource.reservation.RemoteReservationDataSource
import com.eatapp.clementine.data.datasource.reservation.ReservationDataSource
import com.eatapp.clementine.data.datasource.reservation.RoomReservationDataSource
import com.eatapp.clementine.data.network.body.AssignVouchersRequest
import com.eatapp.clementine.data.network.body.RedeemVoucherAssignmentRequest
import com.eatapp.clementine.data.network.body.ReservationBody
import com.eatapp.clementine.data.network.body.StatusBody
import com.eatapp.clementine.data.network.response.notification.NotificationsResponse
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.network.response.reservation.ReservationResponse
import com.eatapp.clementine.data.network.response.survey.SurveyData
import com.eatapp.clementine.data.network.response.tagging.Tagging
import com.eatapp.clementine.internal.endOfTheEatDay
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.internal.startOfTheEatDay
import kotlinx.coroutines.flow.Flow
import java.util.Date
import javax.inject.Inject

class ReservationsRepositoryImpl @Inject constructor(
    private val remoteDataSource: RemoteReservationDataSource,
    private val roomDataSource: RoomReservationDataSource,
    private val eatManager: EatManager
) : ReservationsRepository {

    private val activeDataSource: ReservationDataSource
        get() = if (eatManager.offlineMode) roomDataSource else remoteDataSource

    override fun reservations(date: Date): Flow<List<Reservation>> {
        return activeDataSource.reservations(
            eatManager.restaurantId(),
            startOfTheEatDay(date),
            endOfTheEatDay(date)
        )
    }

    override suspend fun reservation(restaurantId: String, eatId: String): Reservation {
        return activeDataSource.reservation(restaurantId, eatId)
    }

    override suspend fun updateReservation(
        restaurantId: String,
        reservationId: String,
        reservationBody: ReservationBody,
        taggings: List<Tagging>?
    ): Reservation {
        return activeDataSource.updateReservation(
            restaurantId,
            reservationId,
            reservationBody,
            taggings
        )
    }

    override suspend fun updateReservationStatus(
        restaurantId: String,
        reservationId: String,
        reservationBody: StatusBody
    ) {
        activeDataSource.updateReservationStatus(restaurantId, reservationId, reservationBody)
    }

    override suspend fun updateVouchersForReservation(
        restaurantId: String,
        reservationId: String,
        body: AssignVouchersRequest
    ): ReservationResponse {
        return remoteDataSource.updateVouchersForReservation(
            restaurantId,
            reservationId,
            body
        )
    }

    override suspend fun redeemVoucherForReservation(
        restaurantId: String,
        reservationId: String,
        body: RedeemVoucherAssignmentRequest
    ): ReservationResponse {
        return remoteDataSource.redeemVoucherForReservation(
            restaurantId,
            reservationId,
            body
        )
    }

    override suspend fun deleteReservation(reservationId: String) {
        activeDataSource.deleteReservation(reservationId)
    }

    override suspend fun tableReady(reservationId: String): ReservationResponse {
        return remoteDataSource.tableReady(reservationId)
    }

    override suspend fun notifications(
        page: Int,
        limit: Int
    ): NotificationsResponse {
        return remoteDataSource.notifications(page, limit)
    }

    override suspend fun createReservation(
        reservationBody: ReservationBody,
        taggings: List<Tagging>?
    ): Reservation {
        return activeDataSource.createReservation(reservationBody, taggings)
    }

    override suspend fun surveys(): SurveyData =
        remoteDataSource.surveys()
}