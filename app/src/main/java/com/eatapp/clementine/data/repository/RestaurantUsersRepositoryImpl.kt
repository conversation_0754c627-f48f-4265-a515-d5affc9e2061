package com.eatapp.clementine.data.repository

import androidx.lifecycle.MutableLiveData
import com.eatapp.clementine.data.database.dao.RestaurantUserDao
import com.eatapp.clementine.data.network.response.user.User
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import javax.inject.Inject

class RestaurantUsersRepositoryImpl @Inject constructor(
    private val restaurantUserDao: RestaurantUserDao,
) : RestaurantUsersRepository {

    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var collectingJob: Job? = null

    override val takers = MutableLiveData<List<User>>()
    override val users = MutableLiveData<List<User>>()

    override fun startCollecting(restaurantId: String) {
        collectingJob?.cancel()

        collectingJob = restaurantUserDao.restaurantUsersFlow(restaurantId)
            .distinctUntilChanged()
            .onEach {
                takers.postValue(it.map { it.toUserModel() }.filter { it.taker }.toMutableList())
                users.postValue(it.map { it.toUserModel() }.toMutableList())
            }
            .launchIn(scope)
    }
}