package com.eatapp.clementine.data.repository

import androidx.lifecycle.MutableLiveData
import com.eatapp.clementine.data.database.dao.RoomDao
import com.eatapp.clementine.data.network.response.room.Room
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import javax.inject.Inject

class RoomRepositoryImpl @Inject constructor(
    private val roomDao: RoomDao
) : RoomRepository {

    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var collectingJob: Job? = null

    override val rooms = MutableLiveData<List<Room>>()

    override fun startCollecting(restaurantId: String) {
        collectingJob?.cancel()

        collectingJob = roomDao.roomsWithTablesFlow(restaurantId)
            .distinctUntilChanged()
            .onEach {
                rooms.postValue(it.map { it.toRoomModel() }.toMutableList())
            }
            .launchIn(scope)
    }
}