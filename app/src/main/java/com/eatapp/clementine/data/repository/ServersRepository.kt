package com.eatapp.clementine.data.repository

import androidx.lifecycle.LiveData
import com.eatapp.clementine.data.network.body.ServerBody
import com.eatapp.clementine.data.network.response.server.Server
import kotlinx.coroutines.flow.Flow

interface ServersRepository: CacheableRepository {
    val servers: LiveData<List<Server>>
    suspend fun updateServer(serverId: String, body: ServerBody)
    suspend fun loadServers()
}