package com.eatapp.clementine.data.repository

import androidx.lifecycle.MutableLiveData
import com.eatapp.clementine.data.database.dao.RestaurantServerDao
import com.eatapp.clementine.data.datasource.server.RemoteServerDataSource
import com.eatapp.clementine.data.datasource.server.RoomServerDataSource
import com.eatapp.clementine.data.datasource.server.ServerDataSource
import com.eatapp.clementine.data.network.body.ServerBody
import com.eatapp.clementine.data.network.response.server.Server
import com.eatapp.clementine.internal.managers.EatManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import javax.inject.Inject

class ServersRepositoryImpl @Inject constructor(
    private val remoteDataSource: RemoteServerDataSource,
    private val roomDataSource: RoomServerDataSource,
    private val eatManager: EatManager,
    private val serverDao: RestaurantServerDao
) : ServersRepository {

    override val servers = MutableLiveData<List<Server>>()

    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var collectingJob: Job? = null

    private val activeDataSource: ServerDataSource
        get() = if (eatManager.offlineMode) roomDataSource else remoteDataSource

    override suspend fun updateServer(serverId: String, body: ServerBody) {
        activeDataSource.updateServer(serverId, body)
    }

    override suspend fun loadServers() {
        activeDataSource.servers()
    }

    override fun startCollecting(restaurantId: String) {
        collectingJob?.cancel()
        collectingJob = serverDao.restaurantServersWithTablesFlow(eatManager.restaurantId())
            .distinctUntilChanged()
            .onEach {
                servers.postValue(it.map { it.toServerModel() })
            }
            .launchIn(scope)
    }
}