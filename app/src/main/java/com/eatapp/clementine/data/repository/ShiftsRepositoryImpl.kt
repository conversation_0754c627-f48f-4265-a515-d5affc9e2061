package com.eatapp.clementine.data.repository

import androidx.lifecycle.MutableLiveData
import com.eatapp.clementine.data.database.dao.OnlineSeatingShiftDao
import com.eatapp.clementine.data.network.response.shift.Shift
import com.eatapp.clementine.internal.managers.EatManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import javax.inject.Inject

class ShiftsRepositoryImpl @Inject constructor(
    private val shiftDao: OnlineSeatingShiftDao,
    private val eatManager: EatManager
) : ShiftsRepository {

    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var collectingJob: Job? = null

    override val shifts = MutableLiveData<List<Shift>>()

    override fun startCollecting(restaurantId: String) {
        collectingJob?.cancel()

        collectingJob = shiftDao.onlineSeatingShiftsFlow(
            restaurantId
        ).distinctUntilChanged()
            .flowOn(Dispatchers.IO)
            .onEach {
                shifts.postValue(it.map { it.toShift() }.toMutableList())
            }
            .launchIn(scope)
    }
}