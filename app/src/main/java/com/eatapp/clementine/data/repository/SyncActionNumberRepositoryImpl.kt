package com.eatapp.clementine.data.repository

import com.eatapp.clementine.data.database.dao.SyncActionNumberDao
import com.eatapp.clementine.data.database.entities.SyncActionNumberEntity
import javax.inject.Inject

class SyncActionNumberRepositoryImpl @Inject constructor(
    private val syncActionDao: SyncActionNumberDao
) : SyncActionNumberRepository {
    override suspend fun getSyncActionNumber(restaurantId: String): Int {
        return syncActionDao.syncActionNumber(restaurantId)
    }

    override suspend fun updateActionNumber(actionNumber: SyncActionNumberEntity) {
        syncActionDao.updateActionNumber(actionNumber)
    }
}