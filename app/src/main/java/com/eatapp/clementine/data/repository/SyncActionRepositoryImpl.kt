package com.eatapp.clementine.data.repository

import com.eatapp.clementine.data.database.dao.SyncActionDao
import com.eatapp.clementine.data.network.body.SyncAction
import javax.inject.Inject

class SyncActionRepositoryImpl @Inject constructor(
    private val syncActionDao: SyncActionDao
) : SyncActionRepository {
    override suspend fun saveSyncAction(syncAction: SyncAction) {
        syncActionDao.saveSyncAction(syncAction)
    }
}