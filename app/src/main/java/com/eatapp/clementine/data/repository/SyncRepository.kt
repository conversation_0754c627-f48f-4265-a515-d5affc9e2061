package com.eatapp.clementine.data.repository

import com.eatapp.clementine.data.network.body.AsyncChangesRequest
import com.eatapp.clementine.data.network.body.Payload
import com.eatapp.clementine.data.network.body.SyncResponse
import okhttp3.ResponseBody
import retrofit2.Response

interface SyncRepository {
    suspend fun postSyncChanges(asyncChangesRequest: AsyncChangesRequest): Response<SyncResponse>
    suspend fun getSyncChanges(syncId: String): SyncResponse
    suspend fun downloadFile(fileUrl: String): Response<ResponseBody>
}