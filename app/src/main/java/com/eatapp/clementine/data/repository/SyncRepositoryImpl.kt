package com.eatapp.clementine.data.repository

import com.eatapp.clementine.data.network.EatApiRestaurant
import com.eatapp.clementine.data.network.RemoteSyncService
import com.eatapp.clementine.data.network.body.AsyncChangesRequest
import com.eatapp.clementine.data.network.body.SyncResponse
import okhttp3.ResponseBody
import retrofit2.Response
import javax.inject.Inject

class SyncRepositoryImpl @Inject constructor(
    private val eatApiService: EatApiRestaurant,
    private val remoteSyncService: RemoteSyncService
) : SyncRepository {
    override suspend fun postSyncChanges(asyncChangesRequest: AsyncChangesRequest): Response<SyncResponse> {
        return eatApiService.postSyncChanges(asyncChangesRequest)
    }

    override suspend fun getSyncChanges(syncId: String): SyncResponse {
        return eatApiService.getSyncChanges(syncId)
    }

    override suspend fun downloadFile(fileUrl: String): Response<ResponseBody> {
        return remoteSyncService.downloadFile(fileUrl)
    }
}