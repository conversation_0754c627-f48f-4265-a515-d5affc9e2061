package com.eatapp.clementine.data.repository

import androidx.lifecycle.MutableLiveData
import com.eatapp.clementine.data.database.dao.TableDao
import com.eatapp.clementine.data.network.response.room.Table
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import javax.inject.Inject

class TablesRepositoryImpl @Inject constructor(
    private val tableDao: TableDao,
) : TablesRepository {

    override val tables = MutableLiveData<List<Table>>()

    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var collectingJob: Job? = null

    override fun startCollecting(restaurantId: String) {
        collectingJob?.cancel()

        collectingJob = tableDao.tablesFlow(restaurantId)
            .distinctUntilChanged()
            .flowOn(Dispatchers.IO)
            .onEach {
                tables.postValue(it.map { it.toTableModel() }.toMutableList())
            }
            .launchIn(scope)
    }
}