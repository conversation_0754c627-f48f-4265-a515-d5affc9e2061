package com.eatapp.clementine.data.repository

import androidx.lifecycle.LiveData
import com.eatapp.clementine.data.database.entities.GuestEntity
import com.eatapp.clementine.data.database.entities.ReservationEntity
import com.eatapp.clementine.data.network.response.guest.Guest
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.network.response.tag.Tag

interface TagRepository: CacheableRepository {
    val tags: LiveData<List<Tag>>
    fun reservationTags() : List<Tag>
    fun guestTags() : List<Tag>
    fun addTagColors(guestEntity: GuestEntity)
    fun addTagColors(reservationEntity: ReservationEntity)
    fun addTagColors(guest: Guest): Guest
    fun addTagColors(reservation: Reservation)
}