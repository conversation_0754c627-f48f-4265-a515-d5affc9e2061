package com.eatapp.clementine.data.repository

import androidx.lifecycle.MutableLiveData
import com.eatapp.clementine.data.database.dao.TagDao
import com.eatapp.clementine.data.database.entities.GuestEntity
import com.eatapp.clementine.data.database.entities.ReservationEntity
import com.eatapp.clementine.data.network.response.guest.Guest
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.network.response.tag.Tag
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class TagRepositoryImpl @Inject constructor(
    private val tagDao: TagDao
) : TagRepository {

    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var collectingJob: Job? = null

    override var tags = MutableLiveData<List<Tag>>()

    override fun startCollecting(restaurantId: String) {
        collectingJob?.cancel()

        collectingJob = tagDao.tagsFlow(restaurantId)
            .distinctUntilChanged()
            .onEach { tagList ->
                tags.postValue(tagList.map { it.toTagModel() }.toMutableList())
            }
            .launchIn(scope)
    }

    override fun reservationTags(): List<Tag> {
        return tags.value?.filter { it.attributes.context.contains("reservation") } ?: emptyList()
    }

    override fun guestTags(): List<Tag> {
        return tags.value?.filter { it.attributes.context.contains("reservation") } ?: emptyList()
    }

    override fun addTagColors(guestEntity: GuestEntity) {
        guestEntity?.taggingEntities?.forEach { tagging ->
            tagging.color =
                guestTags()
                    .firstOrNull { it.attributes.name == tagging.name }?.category?.attributes?.color
        }
    }

    override fun addTagColors(reservationEntity: ReservationEntity) {
        reservationEntity.taggingEntities?.forEach { tagging ->
            tagging.color =
                reservationTags()
                    .firstOrNull { it.attributes.name == tagging.name }?.category?.attributes?.color
        }
    }

    override fun addTagColors(guest: Guest): Guest {
        guest.taggings?.forEach { tagging ->
            tagging.color =
                guestTags()
                    .firstOrNull { it.attributes.name == tagging.name }?.category?.attributes?.color
        }
        return guest
    }

    override fun addTagColors(reservation: Reservation) {
        reservation.taggings?.forEach { tagging ->
            tagging.color =
                reservationTags()
                    .firstOrNull { it.attributes.name == tagging.name }?.category?.attributes?.color
        }
    }
}