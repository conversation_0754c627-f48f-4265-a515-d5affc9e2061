package com.eatapp.clementine.data.repository

import androidx.lifecycle.MutableLiveData
import com.eatapp.clementine.data.database.dao.VoucherDao
import com.eatapp.clementine.data.network.response.vouchers.Voucher
import com.eatapp.clementine.internal.managers.EatManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import javax.inject.Inject

class VoucherRepositoryImpl @Inject constructor(
    private val voucherDao: VoucherDao
) : VoucherRepository {

    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var collectingJob: Job? = null

    override val vouchers = MutableLiveData<List<Voucher>>()

    override fun startCollecting(restaurantId: String) {
        collectingJob?.cancel()

        collectingJob = voucherDao.vouchersFlow(restaurantId)
            .distinctUntilChanged()
            .flowOn(Dispatchers.IO)
            .onEach {
                vouchers.postValue(it.map { it.toVoucher() }.toMutableList())
            }
            .launchIn(scope)
    }
}