package com.eatapp.clementine.data.repository

import com.eatapp.clementine.data.network.EatApiCanaryWsService
import com.eatapp.clementine.data.network.EatApiHydraWsService
import com.eatapp.clementine.data.network.EatApiRestaurant
import com.eatapp.clementine.data.network.response.websocket.WebsocketMessage
import javax.inject.Inject

class WebsocketsRepositoryImpl @Inject constructor(
    private val eatApiWsHydraService: EatApiHydraWsService,
    private val eatApiWsCanaryService: EatApiCanaryWsService,
    private val eatApiRestaurant: EatApiRestaurant
) : WebsocketsRepository {

    override suspend fun hydra(): WebsocketMessage
            = eatApiWsHydraService.hydraAsync()

    override suspend fun hydraBackup(): WebsocketMessage
            = eatApiRestaurant.hydraAsync()

    override suspend fun canary(key: String)
            = eatApiWsCanaryService.canaryAsync(key)
}