package com.eatapp.clementine.enums

enum class Configuration(val title: String, val value: String) {

    SHIFT("Shift", "shift"),
    STATUS("Status", "status"),
    BOTH("Both", "both"),
    LIFECYCLE("Life-cycle", "life_cycle"),
    NONE("None", "none"),

    RESERVATION_TIME("Reservation time", "reservation_time"),
    CREATED_TIME("Created time", "created_time"),
    NAME("Name", "name"),
    COVERS("Covers", "covers"),

    RESERVATION_COMPACT_MODE("Compact view", "reservation_compact_view"),
    RESERVATION_DETAIL_MODE("Detail view", "reservation_detail_view"),

    ALL_SHIFTS("All shifts", "all_shifts");

    companion object {

        val groupBy = arrayListOf(
            SHIFT,
            STATUS,
            BOTH,
            NONE
        )

        val sortBy = arrayListOf(
            RESERVATION_TIME,
            CREATED_TIME,
            NAME,
            COVERS
        )

        val reservationViewMode = arrayListOf(
            RESERVATION_COMPACT_MODE,
            RESERVATION_DETAIL_MODE
        )
    }
}