package com.eatapp.clementine.enums

import com.eatapp.clementine.R

enum class CustomTag(val icon: Int) {

    RED( R.drawable.ic_tag_icon),
    GREEN(R.drawable.ic_tag_icon),
    BLUE(R.drawable.ic_tag_icon),
    YELLOW(R.drawable.ic_tag_icon);

    companion object {

        private val icons = arrayListOf(
            RED,
            GRE<PERSON>,
            BLUE,
            Y<PERSON>LOW
        )

        fun getIcon(position: Int): Int {
            return icons[position%4].icon
        }
    }
}
