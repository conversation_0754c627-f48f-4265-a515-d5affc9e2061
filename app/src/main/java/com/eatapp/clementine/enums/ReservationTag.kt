package com.eatapp.clementine.enums

    import com.eatapp.clementine.R

enum class ReservationTag(val title: String, val icon: Int) {

    ALLERGY("allergy_icon", R.drawable.allergy_icon),
    ALERT_MANAGEMENT("alert_management_icon", R.drawable.alert_management_icon),
    ANNIVERSARY("anniversary_icon", R.drawable.anniversary_icon),
    BIRTHDAY("birthday_icon", R.drawable.birthday_icon),
    BLOGGER("blogger_icon", R.drawable.blogger_icon),
    BOOTH("booth_icon", R.drawable.booth_icon),
    COMPLIMENTRY("complimentary_icon", R.drawable.complimentary_icon),
    EMPLOYEE("employee_icon", R.drawable.employee_icon),
    FRIEND("friend_icon", R.drawable.friend_icon),
    OWNER("friend_icon", R.drawable.friend_icon),
    GLUTEN("gluten_icon", R.drawable.gluten_icon),
    GRADUATION("graduation_icon", R.drawable.graduation_icon),
    INVESTOR("investor_icon", R.drawable.investor_icon),
    PATIO("patio_icon", R.drawable.patio_icon),
    PESCATARIAN("fish_icon", R.drawable.fish_icon),
    QUIET("quiet_icon", R.drawable.quiet_icon),
    REDMEAT("meat_icon", R.drawable.meat_icon),
    REGULAR("regular_icon", R.drawable.regular_icon),
    SMOKING("smoking_icon", R.drawable.smoking_icon),
    VEG("vegan_icon", R.drawable.vegan_icon),
    VEGAN("vegan_icon", R.drawable.vegan_icon),
    VIEW("view_icon", R.drawable.view_icon),
    VIP("vip_icon", R.drawable.vip_icon),
    WHEELCHAIR("wheel_chair_icon", R.drawable.wheel_chair_icon),
    WINDOW("window_icon", R.drawable.window_icon);

    companion object {

        val list = arrayListOf(
            ALLERGY,
            ALERT_MANAGEMENT,
            ANNIVERSARY,
            BIRTHDAY,
            BLOGGER,
            BOOTH,
            COMPLIMENTRY,
            EMPLOYEE,
            FRIEND,
            OWNER,
            GLUTEN,
            GRADUATION,
            INVESTOR,
            PATIO,
            PESCATARIAN,
            QUIET,
            REDMEAT,
            REGULAR,
            SMOKING,
            VEG,
            VEGAN,
            VIEW,
            VIP,
            WHEELCHAIR,
            WINDOW
        )

        fun getIcon(tag: String?): Any {
            entries.forEach { c ->
                if (c.title == tag) return c.icon
            }

            tag?.let {
                if (it.length <= 3 && tag.isNotEmpty()) {
                    return tag
                }
            }

            return R.drawable.ic_tag_icon
        }
    }
}
