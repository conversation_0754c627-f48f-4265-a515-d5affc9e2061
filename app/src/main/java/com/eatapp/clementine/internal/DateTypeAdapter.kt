package com.eatapp.clementine.internal

import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.google.gson.JsonPrimitive
import com.google.gson.JsonSerializationContext
import com.google.gson.JsonSerializer
import java.lang.reflect.Type
import java.text.SimpleDateFormat
import java.time.LocalDateTime
import java.time.ZoneId
import java.util.Date
import javax.inject.Inject

class DateTypeAdapter @Inject constructor() : JsonSerializer<Date>, JsonDeserializer<Date> {


    companion object {
        private val DEFAULT_ZONE_ID: ZoneId = ZoneId.systemDefault()
    }

    private var dateFormat: SimpleDateFormat = formatNonLocal()

    @Synchronized
    override fun serialize(
        src: Date?,
        typeOfSrc: Type?,
        context: JsonSerializationContext?
    ): JsonElement {
        val dateFormatAsString = dateFormat.format(src)
        return JsonPrimitive(dateFormatAsString)
    }

    @Synchronized
    override fun deserialize(
        json: JsonElement?,
        typeOfT: Type?,
        context: JsonDeserializationContext?
    ): Date? {
        return try {
            Date.from(
                LocalDateTime.parse(json!!.asString.take(19)).atZone(DEFAULT_ZONE_ID)
                    .toInstant()
            )
        } catch (e: Exception) {
            return try {
                dateFromString(json!!.asString)
            } catch (e: Exception) {
                null
            }
        }
    }
}