package com.eatapp.clementine.internal

import com.eatapp.clementine.data.network.response.error.ErrorResponse
import com.google.gson.Gson
import kotlinx.coroutines.CancellationException
import retrofit2.HttpException

class EatException(exception: Exception, poll: Boolean) : Exception() {

    var code: Int = 0
    var alert: Boolean = false
    var title: String = ""
    var description: String = ""
    var key: String? = ""

    constructor(title: String, description: String) : this(ValidationException(), false) {
        this.title = title
        this.description = description
    }

    init {

        when (exception) {
            is HttpException -> {
                val errorResponse = parseErrorResponse(exception)
                code = exception.code()
                title = "Server error"
                description = description(errorResponse)
                key = key(errorResponse)
                alert = true
            }

            is NoConnectivityException -> {
                title = "No connection!"
                description = "Check your connection and try again."
                alert = !poll
            }

            is CancellationException -> {
                alert = false
            }

            is ValidationException -> {
                alert = true
            }

            else -> {
                title = "Error!"
                description = "Something went wrong."
                alert = !poll
            }
        }
    }

    private fun key(response: ErrorResponse?): String? {
        return response?.errors?.firstOrNull()?.key
    }

    private fun description(response: ErrorResponse?): String {
        return if (response?.errors.isNullOrEmpty()) {
            "Something went wrong."
        } else {
            val error = response?.errors?.get(0)
            if ((error?.key != error?.detail) && (error?.key?.underscoreToCapitalized != error?.detail)) {
                String.format("%s :: %s", error?.key?.underscoreToCapitalized ?: "Unknown", error?.detail)
            } else {
                error?.key?.underscoreToCapitalized ?: "Something went wrong."
            }
        }
    }

    private fun parseErrorResponse(exception: HttpException): ErrorResponse? {
        return try {
            val gson = Gson()
            gson.fromJson(
                exception.response()?.errorBody()?.string(),
                ErrorResponse::class.java
            )
        } catch (e: Exception) { null }
    }
}