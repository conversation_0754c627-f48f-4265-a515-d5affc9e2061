package com.eatapp.clementine.internal

import com.eatapp.clementine.BuildConfig

class Endpoints {

    companion object {
        var restaurantEndpoint: String = BuildConfig.BASE_URL
        var messagingEndpoint: String = BuildConfig.BASE_URL_MESSAGING
        var hubspotEndpoint: String = BuildConfig.BASE_URL_HUBSPOT
        var posEndPoint: String = BuildConfig.BASE_URL_POS
        var paymentsEndPoint: String = BuildConfig.BASE_URL_PAYMENTS
        var websocketConnectionEndpoint: String = BuildConfig.WS_URL
        var hydraEndpoint: String? = BuildConfig.WS_SERVICE_URL
        var canaryEndpoint: String = BuildConfig.WS_SERVICE_URL
        var adminEndpoint: String? = BuildConfig.BASE_URL_ADMIN
    }
}