package com.eatapp.clementine.internal

import android.util.Base64
import java.io.UnsupportedEncodingException
import java.util.*

object JWTUtils {

    @Throws(Exception::class)
    fun expiryDate(JWTEncoded: String): Date {
        return try {
            val split = JWTEncoded.split("\\.".toRegex()).toTypedArray()
            Date((getJson(split[1]).split("exp\":")[1].split(",\"")[0]).toLong()*1000)
        } catch (e: UnsupportedEncodingException) {
            Date()
        }
    }

    @Throws(UnsupportedEncodingException::class)
    private fun getJson(strEncoded: String): String {
        val decodedBytes: ByteArray = Base64.decode(strEncoded, Base64.URL_SAFE)
        return String(decodedBytes, Charsets.UTF_8)
    }
}
