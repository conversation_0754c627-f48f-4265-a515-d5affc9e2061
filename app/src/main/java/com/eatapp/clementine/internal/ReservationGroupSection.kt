package com.eatapp.clementine.internal

import com.eatapp.clementine.data.network.response.restaurant.ReservationStatus
import com.eatapp.clementine.ui.home.overview.ReservationsViewModel

data class ReservationGroupSection(
    val title: String,
    val statuses: List<ReservationStatus>,
    var expanded: <PERSON><PERSON><PERSON>,
    var items: MutableList<ReservationSection>,
    val fakeId: String
) {

    fun coversCount(filterType: ReservationsViewModel.FilterType): Int {
        return when (filterType) {
            ReservationsViewModel.FilterType.ALL -> {
                items.flatMap { it.items }.sumOf { it.covers }
            }
            ReservationsViewModel.FilterType.SEATED -> {
                items.flatMap { it.items }.filter { Status.isSeated(it.status) }.sumOf { it.covers }
            }
            ReservationsViewModel.FilterType.UPCOMING -> {
                items.flatMap { it.items }.filter { Status.isUpcomingUi(it.status) }.sumOf { it.covers }
            }
            ReservationsViewModel.FilterType.SEATED_UPCOMING -> {
                items.flatMap { it.items }
                    .filter { Status.isSeated(it.status) || Status.isUpcomingUi(it.status) }.sumOf { it.covers }
            }
        }
    }

    fun reservationsCount(filterType: ReservationsViewModel.FilterType): Int {
        return when (filterType) {
            ReservationsViewModel.FilterType.ALL -> {
                items.sumOf { it.items.size }
            }

            ReservationsViewModel.FilterType.SEATED -> {
                items.flatMap { it.items }.filter { Status.isSeated(it.status) }.size
            }

            ReservationsViewModel.FilterType.UPCOMING -> {
                items.flatMap { it.items }.filter { Status.isUpcomingUi(it.status) }.size
            }

            ReservationsViewModel.FilterType.SEATED_UPCOMING -> {
                items.flatMap { it.items }
                    .filter { Status.isSeated(it.status) || Status.isUpcomingUi(it.status) }.size
            }
        }
    }
}
