package com.eatapp.clementine.internal

import androidx.databinding.BaseObservable
import androidx.databinding.Bindable
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.ui.home.overview.ReservationsViewModel

data class ReservationSection(
    val title: String?,
    val color: Any,
    var items: List<Reservation>,
    var expanded: Boolean = true,
    val groupName: String,
    val groupId: String,
    val fakeId: String
 ) : BaseObservable() {

    var filterType: ReservationsViewModel.FilterType = ReservationsViewModel.FilterType.ALL

    val resCount: Int
        @Bindable get() {
            return when (filterType) {
                ReservationsViewModel.FilterType.ALL -> items.filter { res ->
                    Status.isSeated(res.status)
                            || Status.isUpcomingUi(res.status)
                            || Status.isFinished(res.status)
                    }.count()
                ReservationsViewModel.FilterType.SEATED -> items.filter { res -> Status.isSeated(res.status) }
                    .count()
                ReservationsViewModel.FilterType.UPCOMING -> items.filter { res -> Status.isUpcomingUi(res.status) }
                    .count()
                ReservationsViewModel.FilterType.SEATED_UPCOMING -> items.filter { res ->
                    Status.isSeated(res.status)
                            || Status.isUpcomingUi(res.status)
                    }.count()
            }
        }

    val coversCount: Int
        @Bindable get() {
            return when (filterType) {
                ReservationsViewModel.FilterType.ALL -> items.filter { res ->
                    Status.isSeated(res.status)
                            || Status.isUpcomingUi(res.status)
                            || Status.isFinished(res.status)
                }.sumOf { res -> res.covers }

                ReservationsViewModel.FilterType.SEATED -> items.filter { res -> Status.isSeated(res.status) }
                    .sumOf { res -> res.covers }

                ReservationsViewModel.FilterType.UPCOMING -> items.filter { res ->
                    Status.isUpcomingUi(
                        res.status
                    )
                }.sumOf { res -> res.covers }

                ReservationsViewModel.FilterType.SEATED_UPCOMING -> items.filter { res ->
                    Status.isSeated(res.status)
                            || Status.isUpcomingUi(res.status)
                }.sumOf { res -> res.covers }
            }
        }

}
