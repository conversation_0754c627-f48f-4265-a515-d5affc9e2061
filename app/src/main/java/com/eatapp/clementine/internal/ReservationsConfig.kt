package com.eatapp.clementine.internal

import android.content.SharedPreferences
import com.eatapp.clementine.enums.Configuration
import com.eatapp.clementine.internal.managers.GROUP_BY
import com.eatapp.clementine.internal.managers.GROUP_BY_ACTIVATED
import com.eatapp.clementine.internal.managers.RESTAURANT_ID
import com.eatapp.clementine.internal.managers.SORT_BY
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ReservationsConfig @Inject constructor(
    private val sharedPreferences: SharedPreferences,
) {
    val restaurantId: String?
        get() {
            return sharedPreferences.getString(RESTAURANT_ID, "")
        }

    var shiftFilter: String = ""

    var groupBy: Configuration
        get() {
            val grBy = sharedPreferences.getString(GROUP_BY + restaurantId, "shift")
            return Configuration.groupBy.firstOrNull { it.value == grBy } ?: Configuration.SHIFT
        }
        set(groupby) {
            sharedPreferences
                .edit()
                .putString(GROUP_BY + restaurantId, groupby.value)
                .apply()
        }

    var groupByActivated: Boolean
        get() {
            return sharedPreferences.getBoolean(GROUP_BY_ACTIVATED + restaurantId, true)
        }
        set(activated) {
            sharedPreferences
                .edit()
                .putBoolean(GROUP_BY_ACTIVATED + restaurantId, activated)
                .apply()
    }

    var sortBy: Configuration
        get() {
            val srBy = sharedPreferences.getString(SORT_BY + restaurantId, "reservation_time")
            return Configuration.sortBy.firstOrNull { it.value == srBy } ?: Configuration.RESERVATION_TIME
        }
        set(sortby) {
            sharedPreferences
                .edit()
                .putString(SORT_BY + restaurantId, sortby.value)
                .apply()
     }
}