package com.eatapp.clementine.internal

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import kotlinx.parcelize.RawValue

@Parcelize
data class SelectorItem(
    val id: String,
    var name: String,
    var value: @RawValue Any?,
    var icon: @RawValue Any?,
    var color: Int,
    var isAddItem: Boolean,
    var isSelected: Boolean,
    var isHeader: Boolean,
    var isDisabled: Boolean,
    val imageUrl: String? = null,
    ) : Parcelable {

    constructor(
        id: String,
        name: String,
        value: @RawValue Any?,
        isSelected: Boolean,
        isHeader: Boolean,
        isDisabled: Boolean,
        imageUrl: String? = null,
    ) : this(id, name, value, null,0, isAddItem = false, isSelected = isSelected, isHeader = isHeader, isDisabled = isDisabled, imageUrl = imageUrl)

    constructor(
        id: String,
        name: String,
        value: @RawValue Any?,
        icon: @RawValue Any?,
        color: Int,
        isSelected: <PERSON><PERSON><PERSON>,
        isHeader: <PERSON>olean,
        isDisabled: Boolean
    ) : this(id, name, value, icon, color, isAddItem = false, isSelected = isSelected, isHeader = isHeader, isDisabled = isDisabled)

    constructor(
        name: String,
        isAddItem: Boolean
    ) : this("", name, null,null, 0, isAddItem = isAddItem, isSelected = false, isHeader = false, isDisabled = false)
}