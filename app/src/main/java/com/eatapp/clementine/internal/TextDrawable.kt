package com.eatapp.clementine.internal

import android.content.Context
import android.content.res.Resources
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.ColorFilter
import android.graphics.Paint
import android.graphics.Paint.Align
import android.graphics.drawable.Drawable
import androidx.core.content.res.ResourcesCompat
import com.eatapp.clementine.R
import kotlin.math.roundToInt

class TextDrawable(private val resources: Resources,
                   private val context: Context,
                   private val text: CharSequence,
                   private val textSize: Float,
                   private val position: Float) : Drawable() {

    private val paint: Paint = Paint(Paint.ANTI_ALIAS_FLAG)

    override fun draw(canvas: Canvas) {
        val bounds = bounds
        canvas.drawText(text, 0, text.length,
            bounds.centerX().toFloat(), position, paint)
    }

    override fun getOpacity(): Int {
        return paint.alpha
    }

    override fun getIntrinsicWidth(): Int {
        return resources.getDimension(R.dimen.global_icon_size_20).roundToInt()
    }

    override fun getIntrinsicHeight(): Int {
        return resources.getDimension(R.dimen.global_icon_size_20).roundToInt()
    }

    override fun setAlpha(alpha: Int) {
        paint.alpha = alpha
    }

    override fun setColorFilter(filter: ColorFilter?) {
        paint.colorFilter = filter
    }

    companion object {
        private const val DEFAULT_COLOR = Color.BLACK
    }

    init {
        paint.color = DEFAULT_COLOR
        paint.textAlign = Align.CENTER
        paint.textSize = textSize
        paint.typeface = ResourcesCompat.getFont(context, R.font.inter_bold)
    }
}