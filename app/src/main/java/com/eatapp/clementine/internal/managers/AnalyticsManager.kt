package com.eatapp.clementine.internal.managers

import android.content.Context
import com.eatapp.clementine.BuildConfig
import com.mixpanel.android.mpmetrics.MixpanelAPI
import dagger.hilt.android.qualifiers.ApplicationContext
import org.json.JSONObject
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AnalyticsManager @Inject constructor(
    @ApplicationContext val context: Context,
    val eatManager: EatManager
) {

    var instance: MixpanelAPI = MixpanelAPI.getInstance(context, BuildConfig.MIXPANEL_TOKEN, true)

    init {

        val obj = JSONObject()
        obj.put("First Time Open", eatManager.firstStart())
        eatManager.firstStart(false)
        obj.put("Product", "clementine")
        instance.registerSuperProperties(obj)

        trackAppOpen()
    }

    fun registerUser(id: String) {
        instance.identify(id)
    }

    fun unregisterUser() {
        instance.reset()

        val obj = JSONObject()
        obj.put("First Time Open", eatManager.firstStart())
        obj.put("Product", "clementine")
        instance.registerSuperProperties(obj)
    }

    fun updateUser(name: String?, email: String?, restaurantId: String?) {
        val props = JSONObject()
        props.put("Name", name)
        props.put("Email", email)
        props.put("Restaurant Id", restaurantId)
        instance.people.identify(instance.distinctId)
        instance.people.set(props)
        instance.registerSuperProperties(props)
    }

    private fun trackAppOpen() {
        instance.track("app_open")
    }

    fun trackSignup() {
        instance.track("sign-up_(complete)")
    }

    fun trackLogin() {
        instance.track("log_in_(complete)")
    }

    fun trackAddReservationFromTabBar() {
        val obj = JSONObject()
        obj.put("Reservation location", "Orange Button")
        instance.track("add_reservation", obj)
    }

    fun trackAddReservationFromGuest() {
        val obj = JSONObject()
        obj.put("Reservation location", "Guest")
        instance.track("add_reservation", obj)
    }

    fun trackFinishReservation() {
        instance.track("finish_reservation")
    }

    fun trackViewReservationView() {
        instance.track("click_list_view")
    }

    fun trackViewGuestView() {
        instance.track("click_guests_view")
    }

    fun trackAddGuest(properties: JSONObject) {
        instance.track("add_guest", properties)
    }

    fun trackViewGuestProfile() {
        instance.track("view_guest_profile")
    }

    fun trackSearchGuests() {
        instance.track("search_guests")
    }

    fun trackEditGuestProfile(properties: JSONObject) {
        instance.track("edit_guest_profile", properties)
    }

    fun trackDeleteGuestProfile() {
        instance.track("delete_guest_profile")
    }

    fun trackViewReportsView() {
        instance.track("click_reports")
    }

    fun trackViewSettingsView() {
        instance.track("click_settings_view")
    }

    fun trackOpenHubspot() {
        instance.track("open_hubspot")
    }

    fun trackSurveyShown() {
        instance.track("survey_shown")
    }

    fun trackSurveyClosed() {
        instance.track("survey_closed")
    }

    fun reservationListViewCompactMode() {
        instance.track("reservation_list_view.compact")
    }

    fun reservationListViewDetailMode() {
        instance.track("reservation_list_view.default")
    }

    fun qrScannerOpened() {
        instance.track( "qr_scanner_opened")
    }

    fun qrScannerSuccess() {
        instance.track( "qr_scanner.success")
    }

    fun pushNotificationReservation() {
        instance.track( "push_notification.reservation.opened")
    }

    fun trackReservationStatus() {
        instance.track( "reservation_list_status.edited")
    }
}