package com.eatapp.clementine.internal.managers

import com.eatapp.clementine.BuildConfig
import com.eatapp.clementine.EatApplication
import com.google.android.recaptcha.Recaptcha
import com.google.android.recaptcha.RecaptchaClient
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Singleton

@Singleton
class CaptchaManager(
    @ApplicationContext val context: EatApplication
) {
    var recaptchaClient: RecaptchaClient? = null

    suspend fun fetchRecaptchaClient(): Result<RecaptchaClient> {
        recaptchaClient?.let { client ->
            return Result.success(client)
        } ?: return Recaptcha.getClient(context, BuildConfig.RECAPTCHA_KEY)
    }
}