package com.eatapp.clementine.internal.managers

import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.eatapp.clementine.data.network.response.websocket.WebSocketSyncStatus
import com.eatapp.clementine.data.network.response.websocket.WebsocketData
import com.eatapp.clementine.data.network.response.websocket.WebsocketMessage
import com.eatapp.clementine.data.network.response.websocket.WebsocketStatus
import com.eatapp.clementine.data.repository.WebsocketsRepository
import com.eatapp.clementine.internal.Endpoints
import com.eatapp.clementine.internal.asLiveData
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.neovisionaries.ws.client.WebSocket
import com.neovisionaries.ws.client.WebSocketAdapter
import com.neovisionaries.ws.client.WebSocketException
import com.neovisionaries.ws.client.WebSocketFactory
import com.neovisionaries.ws.client.WebSocketFrame
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.util.Date
import java.util.Timer
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.concurrent.fixedRateTimer
import kotlin.concurrent.schedule

@Singleton
class DataManager @Inject constructor(
    private val eatManager: EatManager,
    private val websocketsRepository: WebsocketsRepository,
    private val featureFlagsManager: FeatureFlagsManager,
    private val logger: LoggerManager
) {
    companion object {
        const val connectionTimeout = 10000       //10 seconds
        const val canaryInterval = 60000L * 5       //5 minutes
        const val wsRetryInterval = 60000L * 5      //5 minutes
        const val healthTimeout = 2000L           //2 seconds
        const val hydraInterval = 5000L           //5 seconds
        const val chChannelTimeout = 5000L        //5 seconds
    }

    private var ws: WebSocket? = null
    private var hydraPayload: WebsocketMessage? = null
    private var isConnected: Boolean = false
    private var isSubscribed: Boolean = false
    private var isHydraDown: Boolean = false
    private var hydraTimestamp: Date? = null
    private var hydraBackupTimestamp: Date? = null

    private var hydraTimer: Timer? = null
    private var canaryTimer: Timer? = null
    private var healthTimer: Timer? = null
    private var wsRetryTimer: Timer? = null
    private var changesChannelTimer: Timer? = null

    private var canaryKey: String = ""

    private val _update = MutableLiveData<List<String>>()
    val update: LiveData<List<String>> by lazy {
        _update.asLiveData()
    }

    private val _actionNumber = MutableLiveData<Int>()
    val actionNumber: LiveData<Int> = _actionNumber

    private val _syncStatusUpdate = MutableLiveData<WebSocketSyncStatus>()
    val syncStatusUpdate: LiveData<WebSocketSyncStatus> = _syncStatusUpdate

    private val _polling = MutableLiveData<Boolean>()
    val polling: LiveData<Boolean> by lazy {
        _polling.asLiveData()
    }

    private val _syncStatusPolling = MutableLiveData<Boolean>()
    val syncStatusPolling: LiveData<Boolean> = _syncStatusPolling

    /**
     * Websockets related methods
     */

    fun connect(token: String, resubscribe: Boolean) {

        val factory = WebSocketFactory()
        ws = factory.createSocket(
            String.format(Endpoints.websocketConnectionEndpoint + "?token=%s", token),
            connectionTimeout
        )

        logger.log(loggerName = "WS", activity = "connection", outcome = "initiated")

        /*
         * Start boot retry timer in case something goes wrong
         * and cancel it once subscription is confirmed
        */
        startWsRetryTimer()

        ws?.addListener(object : WebSocketAdapter() {
            override fun onConnected(ws: WebSocket, headers: Map<String, List<String>>) {
                logger.log(loggerName = "WS", activity = "connection", outcome = "connected")

                isConnected = true

                if (resubscribe) {
                    updateSubscription(Commands.Subscribe, eatManager.restaurantId())
                }
            }

            override fun onTextMessage(ws: WebSocket, text: String) {
                checkStatus(text)
            }

            override fun onDisconnected(
                websocket: WebSocket?, serverCloseFrame: WebSocketFrame?,
                clientCloseFrame: WebSocketFrame?, closedByServer: Boolean
            ) {
                super.onDisconnected(websocket, serverCloseFrame, clientCloseFrame, closedByServer)
                logger.log(
                    loggerName = "WS",
                    activity = "connection",
                    outcome = "disconnected",
                    logLevel = LogLevel.ERROR
                )

                pollHydra()
                pollSyncStatus(true)
            }

            override fun onError(websocket: WebSocket?, cause: WebSocketException?) {
                super.onError(websocket, cause)
                logger.log(
                    loggerName = "WS",
                    activity = "connection",
                    outcome = "error",
                    logLevel = LogLevel.ERROR
                )
                pollHydra()
                pollSyncStatus(true)
            }
        })

        ws?.connectAsynchronously()
    }

    fun disconnect() {
        healthTimer?.cancel()
        healthTimer = null
        canaryTimer?.cancel()
        canaryTimer = null
        hydraTimer?.cancel()
        hydraTimer = null

        ws?.disconnect()
    }

    fun isConnected(): Boolean {
        return isConnected
    }

    fun isSubscribed(): Boolean {
        return isSubscribed
    }

    fun isHydraEnabled(): Boolean {
        return featureFlagsManager.hydraEnabled && !isHydraDown
    }

    fun updateSubscription(command: Commands, restaurantId: String) {

        if (command == Commands.Unsubscribe) {

            logger.log(
                loggerName = "WS",
                activity = "subscription",
                action = "ChangesChannel",
                outcome = "unsubscribed"
            )
            hydraPayload = null

        } else if (command == Commands.Subscribe) {
            /*
             * Schedule changes channel timer in 5 seconds
            */
            changesChannelTimer = Timer()
            changesChannelTimer?.schedule(chChannelTimeout) {
                logger.log(
                    loggerName = "WS",
                    activity = "subscription",
                    action = "ChangesChannel",
                    outcome = "timeout",
                    logLevel = LogLevel.ERROR
                )
                disconnect()
            }
        }

        val model =
            SubscribeModel(
                command.type,
                Gson().toJson(
                    Identifier(
                        Channel.CHANGES.channelName,
                        restaurantId
                    )
                )
            )

        val modelSyncStatus =
            SubscribeModel(
                command.type,
                Gson().toJson(
                    Identifier(
                        Channel.SYNC_STATUS.channelName,
                        restaurantId
                    )
                )
            )
        ws?.sendText(Gson().toJson(model))
        ws?.sendText(Gson().toJson(modelSyncStatus))
    }

    private fun checkStatus(str: String) {

        val message = Gson().fromJson(str, SubscriptionMessage::class.java)

        Log.d("ws_str", str)

        when {
            str.contains("reject_subscription") -> {
                logger.log(
                    loggerName = "WS",
                    activity = "subscription",
                    action = "ChangesChannel",
                    outcome = "rejected",
                    logLevel = LogLevel.WARN
                )
                pollHydra()

                if (message.parseStreamIdentifier().channel == Channel.SYNC_STATUS) {
                    pollSyncStatus(true)
                }
                return
            }

            str.contains("confirm_subscription") -> {
                Log.d("---", " subscription $str at ${Date()}")
                logger.log(
                    loggerName = "WS",
                    activity = "subscription",
                    action = "ChangesChannel",
                    outcome = "subscribed"
                )

                /*
                 * Cancelling hydra timer in case it's already running
                */
                hydraTimer?.cancel()

                /*
                 * Cancelling boot retry timer (subscription successful)
                */
                wsRetryTimer?.cancel()

                /*
                 * Cancelling changes channel timer (subscription successful)
                */
                changesChannelTimer?.cancel()

                if (message.parseStreamIdentifier().channel == Channel.CHANGES) {
                    if (!eatManager.offlineMode) {
                        isSubscribed = true
                    } else {
                        subscribeToSyncStatusUpdates()
                    }
                } else if (message.parseStreamIdentifier().channel == Channel.SYNC_STATUS) {
                    isSubscribed = true
                    pollSyncStatus(false)
                }

                /*
                 * Starting canary health check timer which will check
                 * health of websockets every 5 minutes
                */
                startCanaryTimer()
                return
            }

            str.contains("canary") -> {
                val wsData: WebsocketData = Gson().fromJson(str, WebsocketData::class.java)
                if (wsData.message.type == "canary" && wsData.message.key == canaryKey) {
                    logger.log(loggerName = "WS", activity = "canary", outcome = "health good")

                    /*
                     * Cancel health timer (websockets health is good)
                    */
                    healthTimer?.cancel()

                    /*
                     * Rescheduling canary health check timer after getting
                     * canary payload and confirming websockets are healthy
                    */
                    startCanaryTimer()
                }
            }

            str.contains(Channel.CHANGES.channelName) -> {
                logger.log(
                    loggerName = "WS",
                    activity = "payload_received",
                    action = "ChangesChannel",
                    outcome = "successful"
                )
                Log.d("--- Changes channel", "Web socket data $str at ${Date()}")
                val wsData: WebsocketData = Gson().fromJson(str, WebsocketData::class.java)
                processPayload(wsData.message.status, "WS")
                hydraPayload = wsData.message
            }

            str.contains(Channel.SYNC_STATUS.channelName) -> {
                val wsData: WebSocketSyncStatus =
                    Gson().fromJson(str, WebSocketSyncStatus::class.java)
                Log.d("--- Sync updates channel", "Web socket data $str at ${Date()}")
                _syncStatusUpdate.postValue(wsData)
            }
        }
    }

    private fun subscribeToSyncStatusUpdates() {
        val model =
            SubscribeModel(
                Commands.Subscribe.type,
                Gson().toJson(
                    Identifier(
                        Channel.SYNC_STATUS.channelName,
                        eatManager.restaurantId()
                    )
                )
            )
        ws?.sendText(Gson().toJson(model))
    }

    @OptIn(DelicateCoroutinesApi::class)
    private fun startCanaryTimer() {

        if (!featureFlagsManager.canaryEnabled) return

        canaryTimer?.cancel()
        canaryTimer = Timer()

        /*
         * Schedule canary timer in 5 minutes
        */
        canaryTimer?.schedule(canaryInterval) {

            healthTimer = Timer()
            healthTimer?.schedule(healthTimeout) {
                logger.log(
                    loggerName = "WS",
                    activity = "canary",
                    outcome = "expired",
                    logLevel = LogLevel.ERROR
                )

                /*
                 * Canary payload didn't come in 2 seconds therefore
                 * disconnect from websockets and start polling
                */
                disconnect()
            }

            GlobalScope.launch(Dispatchers.IO) {
                logger.log(loggerName = "WS", activity = "canary", outcome = "api called")

                canaryKey = UUID.randomUUID().toString()
                try {
                    websocketsRepository.canary(canaryKey)
                } catch (e: Exception) {
                }
            }
        }
    }

    private fun startWsRetryTimer() {
        wsRetryTimer?.cancel()
        wsRetryTimer = Timer()

        /*
         * Schedule reboot timer in 5 minutes
        */
        wsRetryTimer?.schedule(wsRetryInterval) {
            logger.log(loggerName = "WS", activity = "reboot", outcome = "attempt initiated")
            connect(eatManager.token(), true)
        }
    }

    private fun pollSyncStatus(poll: Boolean) {

        if (!eatManager.offlineMode) {
            return
        }

        if (eatManager.token().isBlank()) {
            _syncStatusPolling.postValue(false)
            return
        }
        Log.d(SyncManager.LOG_TAG, " started polling for sync status value -> $poll")
        _syncStatusPolling.postValue(poll)
    }

    /**
     * Hydra related methods
     */

    @OptIn(DelicateCoroutinesApi::class)
    private fun pollHydra() {

        isSubscribed = false
        isConnected = false

        if (!featureFlagsManager.hydraEnabled) {
            _polling.value = true
            return
        }

        if (eatManager.token().isBlank()) {
            return
        }

        hydraTimer?.cancel()
        hydraTimer =
            fixedRateTimer("default", false, initialDelay = hydraInterval, period = hydraInterval) {
                GlobalScope.launch(Dispatchers.IO) {

                    if (Date().time - (hydraTimestamp?.time ?: 0) < 4500) {
                        return@launch
                    }

                    if (!featureFlagsManager.hydraEnabled) {
                        _polling.postValue(true)
                        hydraTimer?.cancel()
                        return@launch
                    }
                    hydraTimestamp = Date()
                    logger.log(
                        loggerName = "Hydra",
                        activity = "primary_polling",
                        outcome = "api called"
                    )
                    getHydraPayload()
                }
            }
    }

    private suspend fun getHydraPayload() {
        try {
            val response = websocketsRepository.hydra()
            isHydraDown = false
            processPayload(response.status, "Hydra")
            hydraPayload = response
        } catch (exception: Exception) {
            logger.log(
                loggerName = "Hydra",
                activity = "primary_polling",
                outcome = "api down",
                logLevel = LogLevel.ERROR
            )
            pollBackupHydra()
        }
    }

    @OptIn(DelicateCoroutinesApi::class)
    private fun pollBackupHydra() {

        isSubscribed = false
        isConnected = false

        if (!featureFlagsManager.hydraEnabled) {
            _polling.value = true
            return
        }

        if (eatManager.token().isBlank()) {
            return
        }

        hydraTimer?.cancel()
        hydraTimer =
            fixedRateTimer("default", false, initialDelay = hydraInterval, period = hydraInterval) {
                GlobalScope.launch(Dispatchers.IO) {

                    if (Date().time - (hydraBackupTimestamp?.time ?: 0) < 4500) {
                        return@launch
                    }

                    if (!featureFlagsManager.hydraEnabled) {
                        _polling.postValue(true)
                        hydraTimer?.cancel()
                        return@launch
                    }

                    hydraBackupTimestamp = Date()
                    logger.log(
                        loggerName = "Hydra",
                        activity = "backup_polling",
                        outcome = "api called"
                    )
                    getHydraBackupPayload()
                }
            }
    }

    private suspend fun getHydraBackupPayload() {
        try {
            val response = websocketsRepository.hydraBackup()
            isHydraDown = false
            processPayload(response.status, "Hydra")
            hydraPayload = response
        } catch (exception: Exception) {
            logger.log(
                loggerName = "Hydra",
                activity = "backup_polling",
                outcome = "api down",
                logLevel = LogLevel.ERROR
            )
            isHydraDown = true
            _polling.postValue(true)
            hydraTimer?.cancel()
        }
    }

    /**
     * Common methods
     */

    private fun processPayload(status: WebsocketStatus, log: String) {

        // For offline mode, only trigger sync actions updates
        if (eatManager.offlineMode) {
            Log.d(
                SyncManager.LOG_TAG,
                "Posting sync action number to actionNumber live data"
            )
            _actionNumber.postValue(status.action)
            return
        }

        hydraPayload?.let {

            val updateEvents: MutableList<String> = mutableListOf()

            if (status.action > it.status.action) {
                logger.log(
                    loggerName = log, activity = "action_update",
                    outcome = "remote_revision: ${status.action} > local_revision: ${it.status.action}"
                )
                updateEvents.add(ActionType.ActionNumber.type)
            }
            if (status.reservation > it.status.reservation) {
                logger.log(
                    loggerName = log, activity = "reservation_update",
                    outcome = "remote_revision: ${status.reservation} > local_revision: ${it.status.reservation}"
                )
                updateEvents.add(ActionType.Reservation.type)
            }
            if (status.dayNote > it.status.dayNote) {
                logger.log(
                    loggerName = log, activity = "day_note_update",
                    outcome = "remote_revision: ${status.dayNote} > local_revision: ${it.status.dayNote}"
                )
                updateEvents.add(ActionType.DayNote.type)
            }
            if (status.guest > it.status.guest) {
                logger.log(
                    loggerName = log, activity = "guest_update",
                    outcome = "remote_revision: ${status.guest} > local_revision: ${it.status.guest}"
                )
                updateEvents.add(ActionType.Guest.type)
            }
            if (status.restaurantServer > it.status.restaurantServer) {
                logger.log(
                    loggerName = log, activity = "restaurant_server_update",
                    outcome = "remote_revision: ${status.restaurantServer} > local_revision: ${it.status.restaurantServer}"
                )
                updateEvents.add(ActionType.Server.type)
            }
            if (status.room > it.status.room) {
                logger.log(
                    loggerName = log, activity = "room_update",
                    outcome = "remote_revision: ${status.room} > local_revision: ${it.status.room}"
                )
                updateEvents.add(ActionType.Room.type)
            }
            if (status.table > it.status.table) {
                logger.log(
                    loggerName = log, activity = "table_update",
                    outcome = "remote_revision: ${status.table} > local_revision: ${it.status.table}"
                )
                updateEvents.add(ActionType.Table.type)
            }
            if (status.tag > it.status.tag) {
                logger.log(
                    loggerName = log, activity = "tag_update",
                    outcome = "remote_revision: ${status.tag} > local_revision: ${it.status.tag}"
                )
                updateEvents.add(ActionType.Tag.type)
            }
            if (status.payment > it.status.payment) {
                logger.log(
                    loggerName = log, activity = "payment_update",
                    outcome = "remote_revision: ${status.payment} > local_revision: ${it.status.payment}"
                )
                updateEvents.add(ActionType.Payment.type)
            }
            if (status.messageTemplate > it.status.messageTemplate) {
                logger.log(
                    loggerName = log, activity = "message_template_update",
                    outcome = "remote_revision: ${status.messageTemplate} > local_revision: ${it.status.messageTemplate}"
                )
                updateEvents.add(ActionType.MessageTemplate.type)
            }
            if (status.whatsappTemplate > it.status.whatsappTemplate) {
                logger.log(
                    loggerName = log, activity = "whatsapp_template_update",
                    outcome = "remote_revision: ${status.whatsappTemplate} > local_revision: ${it.status.whatsappTemplate}"
                )
                updateEvents.add(ActionType.WhatsAppTemplate.type)
            }
            if (status.conversation > it.status.conversation) {
                logger.log(
                    loggerName = log, activity = "conversation_update",
                    outcome = "remote_revision: ${status.conversation} > local_revision: ${it.status.conversation}"
                )
                updateEvents.add(ActionType.Conversation.type)
            }
            if (status.voucher > it.status.voucher) {
                logger.log(
                    loggerName = log, activity = "voucher_update",
                    outcome = "remote_revision: ${status.voucher} > local_revision: ${it.status.voucher}"
                )
                updateEvents.add(ActionType.Voucher.type)
            }

            if (updateEvents.isNotEmpty()) {
                _update.postValue(updateEvents)

                logger.log(
                    loggerName = log, activity = "data_update",
                    outcome = "up to date"
                )
            }

        } ?: logger.log(
            loggerName = log, activity = "initial_payload",
            outcome = "saved"
        )
    }
}

enum class Commands(val type: String) {
    Subscribe("subscribe"),
    Unsubscribe("unsubscribe"),
}

enum class Channel(val channelName: String) {
    @SerializedName("ChangesChannel")
    CHANGES("ChangesChannel"),

    @SerializedName("SyncUpdatesChannel")
    SYNC_STATUS("SyncUpdatesChannel")
}

enum class ActionType(val type: String) {
    Reservation("reservation"),
    DayNote("day_note"),
    Guest("guest"),
    Server("server"),
    Room("room"),
    MessageTemplate("message_template"),
    WhatsAppTemplate("whatsapp_template"),
    Table("table"),
    Tag("tag"),
    Payment("payment"),
    ActionNumber("action_number"),
    Conversation("conversation"),
    Voucher("voucher")
}

data class SubscribeModel(
    @SerializedName("command")
    var command: String?,
    @SerializedName("identifier")
    var identifier: String?
)

data class Identifier(
    @SerializedName("channel")
    var channel: String?,
    @SerializedName("id")
    var id: String?
)

data class SubscriptionMessage(
    @SerializedName("type")
    val type: String,

    @SerializedName("stream_identifier")
    val streamIdentifierRaw: String?
) {
    fun parseStreamIdentifier(): StreamIdentifier {
        return Gson().fromJson(streamIdentifierRaw, StreamIdentifier::class.java)
    }
}

data class StreamIdentifier(
    @SerializedName("channel")
    val channel: Channel?,

    @SerializedName("id")
    val id: String?
)
