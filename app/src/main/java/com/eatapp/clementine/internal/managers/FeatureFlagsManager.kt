package com.eatapp.clementine.internal.managers

import android.app.Application
import com.eatapp.clementine.BuildConfig
import com.launchdarkly.sdk.LDContext
import com.launchdarkly.sdk.android.LDClient
import com.launchdarkly.sdk.android.LDConfig

class FeatureFlagsManager(
    private val context: Application,
) {
    var client: LDClient? = null

    fun configureDarkly(userId: String) {
        val ldConfig: LDConfig = LDConfig.Builder(LDConfig.Builder.AutoEnvAttributes.Enabled)
            .mobileKey(BuildConfig.LAUNCH_DARKLY_KEY)
            .build()

        val ldContext = LDContext.create(userId);

        client = LDClient.init(context, ldConfig, ldContext, 0)
    }

    val hydraEnabled: Boolean
        get() {
            return client?.boolVariation("high-availability.enable-hydra-polling", false) ?: true
        }

    val canaryEnabled: Boolean
        get() {
            return client?.boolVariation("high-availability.enable-canary-polling", false) ?: true
        }

    val npsEnabled: Boolean
        get() {
            return client?.boolVariation("surveys.enable-nps", false) ?: true
        }

    val badgerEnabled: Boolean
        get() {
            return client?.boolVariation("badger.enable-popups-and-banners", false) ?: true
        }

    val onboardingQuickSetupEnabled: Boolean
        get() {
            return client?.boolVariation("onboarding.enable-quick-setup", false) ?: true
        }

    val latestVersion: Int
        get() {
            return client?.intVariation("app.latest-version", 0) ?: 0
        }

    val variationWhatsappChatFlow: String
        get() {
            return client?.stringVariation("messaging.enable-whatsapp-chat-flow", "on") ?: "on"
        }

    val showNewGuestFlow: Boolean
        get() {
            return client?.boolVariation("guest-creation.enable-simplified-flow", false) ?: false
        }
}