package com.eatapp.clementine.internal.managers

import android.util.Log
import com.datadog.android.log.Logger
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class LoggerManager @Inject constructor(
    val eatManager: EatManager
) {
    private var logger: Logger? = null

    init {
        logger = Logger.Builder()
            .setNetworkInfoEnabled(true)
            //.setLogcatLogsEnabled(true)
            .setDatadogLogsEnabled(true)
            .setBundleWithTraceEnabled(true)
            .setLoggerName("clementine")
            .build()
    }

    fun log(loggerName: String,
            activity: String,
            action: String? = null,
            outcome: String? = null,
            logLevel: LogLevel = LogLevel.INFO,
            additionalAttributes: Pair<String, String>? = null
    ) {
        val attributes = mapOf(
            "restaurant_id" to eatManager.restaurantId(),
            "restaurant_name" to eatManager.restaurant()?.name,
            "severity" to logLevel.level,
            "logger.name" to loggerName,
            "activity" to activity.replace(" ", "_").lowercase(),
            "action" to action,
            "outcome" to outcome,
            (additionalAttributes?.first ?: "") to additionalAttributes?.second
        )

        val message = " :: " + loggerName + " -> " + activity.replace(" ", "_").lowercase() +
        " -> " + ( if (action != null) { ("$action -> ") } else "") + (outcome ?: "")

        when (logLevel) {
            LogLevel.INFO -> logger?.i(message, attributes = attributes)
            LogLevel.WARN -> logger?.w(message, attributes = attributes)
            LogLevel.ERROR -> logger?.e(message, attributes = attributes)
        }

        Log.d("clementine", message)
    }
}

enum class LogLevel(val level: String) {
    INFO("info"),
    WARN("warn"),
    ERROR("error")
}