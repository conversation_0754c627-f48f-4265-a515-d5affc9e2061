package com.eatapp.clementine.internal.managers

import android.content.ComponentName
import android.content.ServiceConnection
import android.graphics.Bitmap
import android.graphics.Canvas
import android.os.IBinder
import android.view.View
import android.view.View.MeasureSpec
import com.eatapp.clementine.data.network.response.guest.Guest
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.network.response.room.Table
import com.eatapp.clementine.data.network.response.server.Server
import net.posprinter.posprinterface.IMyBinder
import net.posprinter.posprinterface.ProcessData
import net.posprinter.posprinterface.TaskCallback
import net.posprinter.utils.BitmapProcess
import net.posprinter.utils.BitmapToByteData
import net.posprinter.utils.DataForSendToPrinterPos80
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import javax.inject.Inject
import javax.inject.Singleton


@Singleton
class PrintManager @Inject constructor() {

    companion object {
        const val chitWidth = 580
    }

    var serConnection: ServiceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName, service: IBinder) {
            binder = service as IMyBinder
        }

        override fun onServiceDisconnected(name: ComponentName) {
            binder = null
        }
    }

    var binder: IMyBinder? = null
    var isConnected = false

    fun printText(
        reservation: Reservation?,
        servers: List<Server>?,
        logo: Bitmap?,
        eatManager: EatManager?,
        testPrint: Boolean = false
    ) {

        binder?.WriteSendData(object : TaskCallback {

            override fun OnSucceed() {}
            override fun OnFailed() {}

        }, ProcessData {
            val list: MutableList<ByteArray> = ArrayList()

            if (testPrint) {
                reservation?.guest = Guest()
                reservation?.guest?.firstName = "John"
                reservation?.guest?.lastName = "Doe"
                reservation?.covers = 2
                reservation?.startTime = Date()
                reservation?.notes = "Lorem ipsum"
                reservation?.guest?.notes = "Regular guest"
            }

            /*
             Eat logo
            */
            list.add(DataForSendToPrinterPos80.initializePrinter())
            list.add(DataForSendToPrinterPos80.printRasterBmp(0, logo, BitmapToByteData.BmpType.Dithering, BitmapToByteData.AlignType.Left, 70))
            list.add(DataForSendToPrinterPos80.printAndFeedLine())

            /*
             Guest name
            */
            reservation?.guestName?.let { guestName ->
                appendToChit(list, "Guest: ", guestName)
            }

            /*
             Covers
            */
            val covers = "%d %s".format(reservation?.covers ?: 0, if (reservation?.covers == 1) "cover" else "covers")
            appendToChit(list, "Covers: ", covers)

            /*
             Reservation time
            */
            val time = SimpleDateFormat("dd MMM (hh:mm a)", Locale.US).format(reservation?.startTime ?: Date())
            appendToChit(list, "Time: ", time)

            /*
             Table
            */
            reservation?.tables?.also {
                val tablesList = ChitPrintUtils.getTables(it)
                if (tablesList != "") {
                    appendToChit(list, "Table: ", tablesList)
                }
            }

            /*
             Server
            */
            reservation?.tables?.also {
                val serversList = ChitPrintUtils.getServers(it, servers)
                if (serversList != "") {
                    appendToChit(list, "Server: ", serversList)
                }
            }

            /*
             Guest notes
            */
            if (reservation?.guest?.notes?.isBlank() == false) {
                appendToChit(list, "Guest notes: ", reservation.guest?.notes ?: "")
            }

            /*
             Guest tags
             */
            if (reservation?.guest?.taggings?.isEmpty() == false && eatManager?.chitPrintConfig(
                    chitPrintConfigGuestTags, true) == true) {

                val tagsArray = mutableListOf<String>()

                reservation.guest?.taggings?.forEach { tagging ->
                    tagsArray.add(tagging.name)
                }

                val tags = tagsArray.joinToString( ", " )

                appendToChit(list, "Guest tags: ", tags)
            }

            /*
             Reservation notes
             */
            if (reservation?.notes?.isBlank() == false && eatManager?.chitPrintConfig(
                    chitPrintConfigReservationNotes, true) == true) {
                appendToChit(list, "Reservation notes: ", reservation.notes ?: "")
            }

            /*
             Reservation tags
             */
            if (reservation?.taggings?.isEmpty() == false && eatManager?.chitPrintConfig(
                    chitPrintConfigReservationTags, true) == true) {

                val tagsArray = mutableListOf<String>()

                reservation.taggings?.forEach { tagging ->
                    tagsArray.add(tagging.name)
                }

                val tags = tagsArray.joinToString( ", " )

                appendToChit(list, "Reservation tags: ", tags)
            }

            /*
             Time created
             */
            val text = SimpleDateFormat("dd.MM.yyyy", Locale.US).format(Date())
            appendToChit(list, "Created on: ", text)

            /*
             Private comments
            */
            if (reservation?.comments?.isEmpty() == false && eatManager?.chitPrintConfig(
                    chitPrintConfigPrivateComments, true) == true) {
                list.add(DataForSendToPrinterPos80.selectOrCancelBoldModel(1))
                list.add(ChitPrintUtils.stringToByteArray(ChitPrintUtils.addNewLine("Comments: ")))
                list.add(DataForSendToPrinterPos80.selectOrCancelBoldModel(0))
                reservation.comments?.forEach {
                    list.add(ChitPrintUtils.stringToByteArray(ChitPrintUtils.addNewLine(it.name + ": " + it.comment)))
                }
                list.add(DataForSendToPrinterPos80.printAndFeedLine())
            }

            /*
             No. of visits
            */
            if (reservation?.guest != null && eatManager?.chitPrintConfig(
                    chitPrintConfigNoOfVisits, false) == true) {
                appendToChit(list, "No. of visits: ", reservation.guest?.visitCount.toString())
            }

            /*
             Average spend per guest
            */
            if (reservation?.guest != null && eatManager?.chitPrintConfig(
                    chitPrintConfigAverageSpendPerGuest, false) == true && eatManager.restaurant()?.posActive == true) {
                appendToChit(list, "Average spend per guest: ",
                    reservation.guest?.averageSpendPerCover.toString() + " " + (eatManager.restaurant()?.currency ?: "AED")
                )
            }

            /*
             Average review rating
            */
            if (reservation?.guest != null && eatManager?.chitPrintConfig(
                    chitPrintConfigAverageReviewRating, false) == true && eatManager.restaurant()?.reviewsActive == true) {
                appendToChit(list, "Average review rating: ", reservation.guest?.reviewRating.toString())
            }

            /*
             Last review rating
            */
            if (reservation?.review != null && reservation.review?.rating != null && eatManager?.chitPrintConfig(
                    chitPrintConfigLastReviewRating, false) == true && eatManager.restaurant()?.reviewsActive == true) {
                appendToChit(list, "Last review rating: ", reservation.review?.rating.toString() + " - " + (reservation.review?.comment ?: "<empty>"))
            }

            list.add(DataForSendToPrinterPos80.printAndFeedLine())

            list.add(DataForSendToPrinterPos80.selectCutPagerModerAndCutPager(66, 1))

            return@ProcessData list
        })
    }

    private fun appendToChit(list: MutableList<ByteArray>, title: String, value: String) {
        list.add(DataForSendToPrinterPos80.selectOrCancelBoldModel(1))
        list.add(ChitPrintUtils.stringToByteArray(ChitPrintUtils.addNewLine(title)))
        list.add(DataForSendToPrinterPos80.selectOrCancelBoldModel(0))
        list.add(ChitPrintUtils.stringToByteArray(ChitPrintUtils.addNewLine(value)))
        list.add(DataForSendToPrinterPos80.printAndFeedLine())
    }
    
    fun printImage(view: View) {

        val bitmap = ChitPrintUtils.getBitmapFromView(view)

        binder?.WriteSendData(object : TaskCallback {

            override fun OnSucceed() {}
            override fun OnFailed() {}

        }, ProcessData {

            val list: MutableList<ByteArray> = ArrayList()
            list.add(DataForSendToPrinterPos80.initializePrinter())
            var blist: List<Bitmap?> = ArrayList()
            blist = BitmapProcess.cutBitmap(150, bitmap)
            for (i in blist.indices) {
                list.add(
                    DataForSendToPrinterPos80.printRasterBmp(
                        0,
                        blist[i],
                        BitmapToByteData.BmpType.Dithering,
                        BitmapToByteData.AlignType.Center,
                        chitWidth
                    )
                )
            }
            list.add(DataForSendToPrinterPos80.printAndFeedLine())
            list.add(DataForSendToPrinterPos80.selectCutPagerModerAndCutPager(0))
            list
        })
    }

    object ChitPrintUtils {

        fun addNewLine(string: String?): String {
            return "${string ?: ""}\n"
        }

        fun stringToByteArray(input: String): ByteArray {
            return input.toByteArray(Charsets.UTF_8)
        }

        fun getBitmapFromView(view: View): Bitmap? {
            view.measure(MeasureSpec.UNSPECIFIED, MeasureSpec.UNSPECIFIED)
            val bitmap = Bitmap.createBitmap(
                chitWidth, view.measuredHeight,
                Bitmap.Config.ARGB_8888
            )
            val canvas = Canvas(bitmap)
            view.layout(0, 0, chitWidth, view.measuredHeight)
            view.draw(canvas)

            return bitmap
        }

        fun getServers(tables: List<Table>?, servers: List<Server>?): String {
            val serversList = mutableListOf<String>()
            tables?.let {
                for (reservationTable in it) {

                    reservationTable.restaurantServerId?.let { id ->
                        serversList.add(servers?.find { server -> server.id == id }?.name ?: "")
                    }
                }
            }
            return serversList.joinToString( ", " )
        }

        fun getTables(tables: List<Table>): String {
            val tablesList = mutableListOf<String>()

            for (reservationTable in tables) {
                tablesList.add(reservationTable.number)
            }
            return tablesList.joinToString( ", " )
        }
    }
}