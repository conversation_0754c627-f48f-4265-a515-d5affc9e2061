package com.eatapp.clementine.internal.managers

import android.util.Log
import androidx.lifecycle.Observer
import com.eatapp.clementine.data.database.dao.SyncActionDao
import com.eatapp.clementine.data.network.body.AsyncChangesRequest
import com.eatapp.clementine.data.network.body.SyncActionBody
import com.eatapp.clementine.data.network.body.SyncResponse
import com.eatapp.clementine.data.network.body.SyncStatus
import com.eatapp.clementine.data.network.response.websocket.WebSocketSyncStatus
import com.eatapp.clementine.data.repository.SyncRepository
import com.eatapp.clementine.internal.InternetConnectionSingleLiveEvent
import com.eatapp.clementine.internal.cancelChildCoroutines
import com.eatapp.clementine.internal.notModified
import com.eatapp.clementine.internal.sync.FileDownloadHelper
import com.eatapp.clementine.internal.sync.SyncSaveHelper
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import retrofit2.Response
import java.util.Date
import java.util.UUID
import java.util.concurrent.atomic.AtomicBoolean
import javax.inject.Inject
import javax.inject.Singleton

@OptIn(FlowPreview::class)
@Singleton
class SyncManager @Inject constructor(
    private val dataManager: DataManager,
    private val syncSaveHelper: SyncSaveHelper,
    private val eatManager: EatManager,
    private val syncRepository: SyncRepository,
    private val syncActionDao: SyncActionDao,
    private val fileDownloadHelper: FileDownloadHelper,
    internetConnectionLiveData: InternetConnectionSingleLiveEvent
) {

    companion object Companion {
        const val LOG_TAG = "Sync-engine"
    }

    val progressFlow: MutableStateFlow<DownloadState> =
        MutableStateFlow(DownloadState.Downloading(0f, 0, null))

    @Volatile
    private var syncId: String? = null
    private var syncInProgress = AtomicBoolean(false)

    private val scope: CoroutineScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

    fun retrySync() {
        scope.launch {
            fileDownloadHelper.clearData()
            progressFlow.value = DownloadState.Downloading(0f, 0, null)
            progressFlow.value = DownloadState.Processing(0, 0, null)
            Log.d(LOG_TAG, "INITIATE SYNC CALLED FROM RETRY")
            initiateSync()
        }
    }

    private val actionNumberObserver = Observer<Int> {
        scope.launch {
            Log.d(LOG_TAG, "Action number observer triggered")
            val actionNumber = syncSaveHelper.getActionNumber()
            if (actionNumber < it) {
                Log.d(
                    LOG_TAG,
                    "Data manager - update received - local revision $actionNumber - remote revision $it"
                )
                if (!syncInProgress.get()) {
                    Log.d(LOG_TAG, "Data manager - update received - Initiating sync")
                    Log.d(LOG_TAG, "INITIATE SYNC CALLED FROM ACTION NUMBER OBSERVER")
                    initiateSync()
                }
            }
        }
    }

    private val syncStatusObserver = Observer<WebSocketSyncStatus> {
        scope.launch {
            Log.d(
                LOG_TAG,
                "Data manager - sync update status received ${it.message?.status?.toString()}"
            )
            processSyncUpdate(it)
        }
    }

    private val syncStatusPollingObserver = Observer<Boolean> {
        Log.d(LOG_TAG, "Sync status polling observer triggered value -> $it")
        if (it) {
            startPollingSyncStatus()
        } else {
            stopPollingSyncStatus()
        }
    }

    private val internetConnectionObserver = Observer<Boolean> { isConnected ->
        if (!eatManager.offlineMode || eatManager.token()
                .isBlank() || !dataManager.isSubscribed()
        ) {
            return@Observer
        }

        if (isConnected) {
            scope.launch {
                Log.d(LOG_TAG, "INITIATE SYNC CALLED FROM CONNECTION OBSERVER")
                if (!syncInProgress.get()) {
                    initiateSync(
                        syncActionDao.notSyncedActions(
                            eatManager.restaurantId()
                        ).firstOrNull()
                            ?.map { it.toBody() } ?: emptyList()
                    )
                }
            }
        }
    }

    private var collectJob: Job? = null

    private var pollingJob: Job? = null

    private var observingRestaurantId: String? = null

    init {
        internetConnectionLiveData.observeForever(internetConnectionObserver)
    }

    fun startObserving(restaurantId: String) {
        if (!eatManager.offlineMode) {
            stopObserving()
            return
        }

        // Clean up before starting new
        if (restaurantId != observingRestaurantId) {
            stopObserving()
            observingRestaurantId = restaurantId
        }

        dataManager.actionNumber.observeForever(actionNumberObserver)
        dataManager.syncStatusUpdate.observeForever(syncStatusObserver)
        dataManager.syncStatusPolling.observeForever(syncStatusPollingObserver)

        collectJob = scope.launch {
            syncActionDao.notSyncedActions(
                restaurantId
            ).debounce(300).collect {
                if (it.isNotEmpty()) {
                    launch(Dispatchers.IO) {
                        initiateSync(it.map { it.toBody() })
                    }
                }
            }
        }

        fileDownloadHelper.progressFlow = progressFlow
    }

    private fun stopObserving() {
        dataManager.syncStatusPolling.removeObserver(syncStatusPollingObserver)
        dataManager.actionNumber.removeObserver(actionNumberObserver)
        dataManager.syncStatusUpdate.removeObserver(syncStatusObserver)
        collectJob?.cancel()
        collectJob = null
        observingRestaurantId = null
    }

    private fun processSyncUpdate(webSocketSyncStatus: WebSocketSyncStatus) {
        if (webSocketSyncStatus.message == null) {
            return
        }

        Log.d(
            LOG_TAG,
            "Sync update status for sync id ${webSocketSyncStatus.message.syncId} ---- ${webSocketSyncStatus.message.status}"
        )
        if (webSocketSyncStatus.message.syncId != this.syncId) {
            Log.d(LOG_TAG, "Different sync id - returning from function")
            return
        }

        Log.d(LOG_TAG, "Sync update status, sync in progress = $syncInProgress")
        if (webSocketSyncStatus.message.status == SyncStatus.COMPLETE
            || webSocketSyncStatus.message.status == SyncStatus.FAILED
            || webSocketSyncStatus.message.status == SyncStatus.ERROR
            || webSocketSyncStatus.message.status == SyncStatus.READING && syncInProgress.get()
            || webSocketSyncStatus.message.status == SyncStatus.BATCH_COMPLETE && syncInProgress.get()
        ) {
            Log.d(
                LOG_TAG,
                "Calling get sync data for sync id ${webSocketSyncStatus.message.syncId}"
            )
            getSyncStatusAndData(webSocketSyncStatus.message.syncId!!)
        }
    }

    private suspend fun initiateSync(syncActions: List<SyncActionBody> = emptyList()) {
        syncInProgress.set(true)

        val actionNumber = syncSaveHelper.getActionNumber()

        Log.d(LOG_TAG, "Initiating sync with action number $actionNumber at ${Date()}")
        Log.d(LOG_TAG, "Initiating sync with actions: $syncActions")
        val request = AsyncChangesRequest(
            actionNumber,
            eatManager.sessionId ?: UUID.randomUUID().toString(),
            syncActions
        )

        try {
            val response = syncRepository.postSyncChanges(request)

            Log.d(LOG_TAG, "Initiating sync response status ${response.body()?.syncStatus}")
            Log.d(LOG_TAG, "Setting sync id ---- ${response.body()?.syncId}")

            response.body()?.syncId?.let {
                this.syncId = it
            }
            syncActionDao.updateAllNotSyncedActionsToSynced()
            processPostSyncResponse(response)
        } catch (e: Exception) {
            progressFlow.value = DownloadState.Failed()
            scope.cancelChildCoroutines()
            fileDownloadHelper.clearData()
            syncInProgress.set(false)
        }
    }

    private suspend fun processPostSyncResponse(response: Response<SyncResponse>) {
        if (response.notModified()) {
            Log.d(LOG_TAG, "Not modified")
            // Nothing to process
            syncInProgress.set(false)
            syncId = null
            stopPollingSyncStatus()
            return
        }

        response.body()?.let {
            if (it.isInstant()) {
                Log.d(LOG_TAG, "Instant")
                fileDownloadHelper.processInstantResponse(it)
                Log.d(LOG_TAG, "Saving sync action number ${it.serverActionNumber}")
                markSyncCompleted(it.serverActionNumber!!)
                return
            }

            getSyncStatusAndData(it.syncId)
        }
    }

    private fun getSyncStatusAndData(syncId: String) {
        scope.launch {
            try {
                Log.d(LOG_TAG, "Get sync data triggered ---- $syncId")
                val response = syncRepository.getSyncChanges(syncId)
                Log.d(
                    LOG_TAG,
                    "Get sync data response received with status ---- ${response.syncStatus}"
                )

                fileDownloadHelper.downloadFiles(response) {
                    markSyncCompleted(response.serverActionNumber!!)
                }
            } catch (e: Exception) {
                scope.cancelChildCoroutines()
                fileDownloadHelper.clearData()
                delay(500)
                progressFlow.value = DownloadState.Failed()
                syncInProgress.set(false)
            }
        }
    }

    private fun markSyncCompleted(actionNumber: Int) {
        syncInProgress.set(false)
        syncId = null
        Log.d(LOG_TAG, "Marking sync as completed")
        progressFlow.value = DownloadState.Finished

        scope.launch {
            syncSaveHelper.saveActionNumber(actionNumber)
            Log.d(LOG_TAG, "INITIATE SYNC CALLED FROM SYNC COMPLETED")
            initiateSync()
        }
    }

    private fun startPollingSyncStatus() {
        Log.d(LOG_TAG, "Sync status polling already running = ${pollingJob?.isActive}")
        if (pollingJob?.isActive == true) return // already running

        pollingJob = scope.launch {
            while (isActive) {
                if (syncId != null) {
                    Log.d(
                        LOG_TAG,
                        "Sync status polling sync id fetching data for sync id - $syncId"
                    )
                    getSyncStatusAndData(syncId!!)
                }
                delay(5000)
            }
        }
    }

    private fun stopPollingSyncStatus() {
        pollingJob?.cancel()
        pollingJob = null
    }

    sealed class DownloadState {
        data class Downloading(
            val downloadedSize: Float,
            val downloadedFiles: Int,
            val totalFiles: Int?
        ) : DownloadState()

        data class Processing(
            val processedFiles: Int,
            val savedFiles: Int,
            val totalFiles: Int? = null
        ) : DownloadState()

        data object Finished : DownloadState()
        data class Failed(val error: Throwable? = null) : DownloadState()
    }
}
