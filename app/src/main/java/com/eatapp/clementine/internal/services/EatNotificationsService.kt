package com.eatapp.clementine.internal.services

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Intent
import android.util.Log
import androidx.core.app.NotificationCompat
import com.eatapp.clementine.R
import com.eatapp.clementine.data.network.response.notification.NotificationAction
import com.eatapp.clementine.internal.Constants
import com.eatapp.clementine.internal.Constants.RESERVATION_KEY_EXTRA
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.ui.home.HomeActivity
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import dagger.hilt.android.AndroidEntryPoint
import org.json.JSONObject
import javax.inject.Inject
import kotlin.math.abs

@AndroidEntryPoint
class EatNotificationsService: FirebaseMessagingService() {

    @Inject
    lateinit var gson: Gson

    @Inject
    lateinit var eatManager: EatManager

    override fun onNewToken(token: String) {
        super.onNewToken(token)
        Log.d("FCM", "New token: $token")
    }

    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        super.onMessageReceived(remoteMessage)

        val jsonObject = JSONObject(gson.toJson(remoteMessage.data))
        val actionsJson = jsonObject.getString("actions")
        val actions = gson.fromJson(actionsJson, Array<NotificationAction>::class.java).toList()

        val reservationId = jsonObject.getString("reservation_id")

        // Suppress notification if reservationId is in the suppressed list
        if (eatManager.getSuppressedNotificationIds().contains(reservationId)) {
            return
        }

        val notification = ReservationNotification(
            reservationId = reservationId,
            title = jsonObject.getString("title"),
            body = jsonObject.getString("body"),
            actions = actions,
        )

        showNotification(notification)
    }

    private fun showNotification(notification: ReservationNotification) {
        val notificationManager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
        val channelId = "FCM_CHANNEL"

        val channel = NotificationChannel(
            channelId,
            "Reservations",
            NotificationManager.IMPORTANCE_HIGH
        )
        notificationManager.createNotificationChannel(channel)

        // Generate a unique notification ID based on reservationId
        val notificationId = abs(notification.reservationId.hashCode())

        // Create intent to open ReservationActivity
        val contentIntent = Intent(this, HomeActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_SINGLE_TOP
            putExtra(RESERVATION_KEY_EXTRA, notification.reservationId)
        }
        val pendingContentIntent = PendingIntent.getActivity(
            this,
            notificationId,
            contentIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val builder = NotificationCompat.Builder(this, channelId)
            .setContentTitle(notification.title)
            .setContentText(notification.body)
            .setSmallIcon(R.drawable.ic_eat)
            .setAutoCancel(true)
            .setContentIntent(pendingContentIntent)

        notification.actions.forEach {
            val intent = Intent(this, NotificationActionReceiver::class.java).apply {
                action = it.type
                putExtra(Constants.STATUS, it.url)
                putExtra(Constants.RESERVATION_ID_EXTRA, notification.reservationId)
                putExtra(Constants.NOTIFICATION_ID_EXTRA, notificationId)
            }
            val pendingIntent = PendingIntent.getBroadcast(
                this,
                notificationId,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            
            builder.addAction(
                NotificationCompat.Action.Builder(
                    0,
                    it.type.replaceFirstChar { c -> c.uppercaseChar() },
                    pendingIntent
                ).build()
            )
        }

        eatManager.notificationsCount += 1

        notificationManager.notify(notificationId, builder.build())
    }
}

data class ReservationNotification(
    val title: String,
    val body: String,
    val actions: List<NotificationAction>,
    @SerializedName("reservation_id")
    val reservationId: String,
)