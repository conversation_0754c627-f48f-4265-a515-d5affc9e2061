package com.eatapp.clementine.internal.services

import android.app.NotificationManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import com.eatapp.clementine.data.network.body.StatusBody
import com.eatapp.clementine.data.repository.ReservationsRepository
import com.eatapp.clementine.internal.Constants
import com.eatapp.clementine.internal.Status
import com.eatapp.clementine.internal.managers.EatManager
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class NotificationActionReceiver : BroadcastReceiver() {

    @Inject
    lateinit var eatManager: EatManager

    @Inject
    lateinit var reservationsRepository: ReservationsRepository

    override fun onReceive(context: Context, intent: Intent) {
        val status = intent.getStringExtra(Constants.STATUS)
        val reservationId = intent.getStringExtra(Constants.RESERVATION_ID_EXTRA) ?: ""
        val notificationId = intent.getIntExtra(Constants.NOTIFICATION_ID_EXTRA, 0)

        // Dismiss the specific notification
        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        status?.let {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val reservationBody = StatusBody(status = when (intent.action) {
                        "confirm" -> Status.Value.CONFIRMED.code
                        "decline" ->  Status.Value.DENIED.code
                        "waitlist" ->  Status.Value.WAITLIST.code
                        else -> return@launch
                    })

                    reservationsRepository.updateReservationStatus(
                        eatManager.restaurantId(),
                        reservationId,
                        reservationBody
                    )

                    notificationManager.cancel(notificationId)

                } catch (e: Exception) {
                    Log.e("NotificationAction", "Error handling action: ${e.message}")
                }
            }
        }
    }
} 