package com.eatapp.clementine.internal.sync

import com.eatapp.clementine.internal.formatNonLocal
import com.google.gson.annotations.SerializedName
import java.lang.reflect.Modifier
import java.util.Calendar
import java.util.Date
import kotlin.reflect.KClass
import kotlin.reflect.full.memberProperties
import kotlin.reflect.jvm.isAccessible
import kotlin.reflect.jvm.javaField

object EntitySyncActionDataMapper {

    inline fun <reified T : Any> dataClassToSerializedMap(instance: T): Map<String, Any?> =
        dataClassToSerializedMap(instance, T::class)

    fun <T : Any> dataClassToSerializedMap(instance: T, kClass: KClass<T>): Map<String, Any?> {
        fun serializeValue(value: Any?): Any? {
            return when (value) {
                null -> null
                is Date -> formatNonLocal().format(value.time)
                is Calendar -> formatNonLocal().format(value.time)
                is List<*> -> value.map { serializeValue(it) }
                is Set<*> -> value.map { serializeValue(it) }.toSet()
                is Map<*, *> -> value.mapValues { serializeValue(it.value) }
                else -> {
                    val valueKClass = value::class
                    if (valueKClass.isData) {
                        @Suppress("UNCHECKED_CAST")
                        return dataClassToSerializedMap(value, valueKClass as KClass<Any>)
                    } else {
                        return value
                    }
                }
            }
        }

        return kClass.memberProperties
            .filter { it.javaField != null && Modifier.isTransient(it.javaField?.modifiers ?: 0).not() }
            .associate { prop ->
                prop.isAccessible = true
                // Try to get serialized name if it exits, otherwise use property name
                val serializedName =
                    prop.javaField?.getAnnotation(SerializedName::class.java)?.value
                val key = serializedName ?: prop.name
                val value = prop.get(instance)
                key to serializeValue(value)
            }
    }
}