package com.eatapp.clementine.internal.sync

import android.content.Context
import android.util.Log
import com.eatapp.clementine.data.network.body.Change
import com.eatapp.clementine.data.network.body.ChangeType
import com.eatapp.clementine.data.network.body.SyncResponse
import com.eatapp.clementine.data.network.body.SyncStatus
import com.eatapp.clementine.data.repository.SyncRepository
import com.eatapp.clementine.internal.cancelChildCoroutines
import com.eatapp.clementine.internal.managers.SyncManager
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.joinAll
import kotlinx.coroutines.launch
import java.io.File
import java.io.IOException
import java.util.Collections
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class FileDownloadHelper @Inject constructor(
    @ApplicationContext private val context: Context,
    private val syncRepository: SyncRepository,
    private val fileProcessingHelper: FileProcessingHelper
) {

    var progressFlow: MutableStateFlow<SyncManager.DownloadState>? = null
        set(value) {
            fileProcessingHelper.progressFlow = value
            field = value
        }

    private val downloadingFiles: MutableSet<String> =
        Collections.newSetFromMap(ConcurrentHashMap())
    private val downloadedFiles: MutableSet<String> =
        Collections.newSetFromMap(ConcurrentHashMap())
    private val processingFiles: MutableSet<String> =
        Collections.newSetFromMap(ConcurrentHashMap())

    private var totalPayloads = 0
    private var totalPayloadsProcessed = 0

    private var totalDownloadedBytes = 0L
    private var totalBytesToDownload = 0L
    private var totalFiles: Int? = null

    private val coroutineScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    private val TAG = "Sync-engine"

    /**
     * Downloads multiple files in parallel. Ignores files already downloaded or in progress.
     * @param response Sync response that contains changes
     * @param onComplete Called when all files are processed
     */
    fun downloadFiles(response: SyncResponse, onComplete: () -> Unit) {

        if (response.changes.isNullOrEmpty()) {
            return
        }

        if (response.changes.isNullOrEmpty() && response.syncStatus == SyncStatus.COMPLETE) {
            onComplete()
            return
        }

        val changes = response.changes
        val remoteChanges = filterRemoteChanges(changes)
        val payloadChanges = filterPayloadChanges(changes)

        if (response.batches != null) {
            totalFiles = response.batches
        }

        coroutineScope.launch {

            if (!payloadChanges?.payloads.isNullOrEmpty()) {
                if (!processingFiles.contains(response.syncId)) {
                    processingFiles.add(response.syncId)
                    fileProcessingHelper.processPayloads(payloadChanges?.payloads!!)
                }
            }

            val processingJobs = mutableListOf<Job>()
            val jobs = remoteChanges?.mapNotNull { change ->

                val fileId = change.remote?.normalizedUrl
                val url = change.remote?.url

                if (fileId == null || url == null) {
                    return@mapNotNull null
                }

                if (downloadedFiles.contains(fileId)) {
                    return@mapNotNull null
                }

                if (downloadingFiles.contains(fileId)) {
                    return@mapNotNull null
                }

                totalPayloads += change.volume

                val file = File(context.cacheDir, fileId)
                val job = async {
                    try {
                        downloadFileWithRetry(url, file)
                        downloadedFiles.add(fileId)

                        Log.d(
                            SyncManager.LOG_TAG,
                            "Updating progress after file downloaded - size = ${downloadedFiles.size}"
                        )
                        launch {
                            delay(1000)
                            updateDownloadProgress()
                        }
                        Log.d(
                            SyncManager.LOG_TAG,
                            "Enqueuing file for processing named:  ${file.name}"
                        )
                        val processingJob = fileProcessingHelper.enqueueFileForProcessing(
                            file,
                            onFileSaved = {
                                updateProcessingProgress()
                            },
                            onFileProcessed = {
                                updateProcessingProgress()
                            }
                        )
                        processingJobs.add(processingJob)
                    } catch (e: Exception) {
                        file.delete()
                        cancelSync()
                        delay(500)
                        progressFlow?.value = SyncManager.DownloadState.Failed()
                    } finally {
                        downloadingFiles.remove(fileId)
                    }
                }

                downloadingFiles.add(fileId)
                job
            }

            jobs?.awaitAll()
            processingJobs.joinAll()

            if ((response.syncStatus == SyncStatus.COMPLETE) && fileProcessingHelper.getSavedFilesCount() == (remoteChanges?.size
                    ?: 0)
            ) {
                onComplete()
                delay(500)
                clearData()
            }
        }
    }

    suspend fun processInstantResponse(response: SyncResponse) {
        fileProcessingHelper.processPayloads(
            response.changes?.firstOrNull()?.payloads ?: emptyList()
        )
    }

    @Throws
    private suspend fun downloadFileWithRetry(url: String, file: File) {
        retryWithLimit(3) {
            downloadFile(
                url,
                file,
                totalFileSizeListener = { size ->
                    Log.d(TAG, "File size for $url: $size bytes")
                    totalBytesToDownload += size
                },
                totalDownloadedBytesListener = { bytesRead ->
                    totalDownloadedBytes += bytesRead
                    updateDownloadProgress()
                }
            )
        }
    }

    private fun updateDownloadProgress() {
        progressFlow?.value = SyncManager.DownloadState.Downloading(
            (totalDownloadedBytes.toFloat() / (1024 * 1024)),
            downloadedFiles.size,
            totalFiles
        )
    }

    private fun updateProcessingProgress() {
        progressFlow?.value =
            SyncManager.DownloadState.Processing(
                fileProcessingHelper.getProcessedFileCount(),
                fileProcessingHelper.getSavedFilesCount(),
                totalFiles
            )
    }

    private suspend fun <T> retryWithLimit(
        maxRetries: Int,
        action: suspend () -> T
    ): T {
        var currentAttempt = 0
        var lastException: Exception? = null

        while (currentAttempt < maxRetries) {
            try {
                return action()
            } catch (e: Exception) {
                currentAttempt++
                lastException = e
                Log.w(TAG, "Retry $currentAttempt/$maxRetries failed: ${e.message}")
                if (currentAttempt >= maxRetries) break
            }
        }

        throw lastException ?: IllegalStateException("Retry failed without an exception.")
    }

    @Throws
    private suspend fun downloadFile(
        url: String,
        file: File,
        totalFileSizeListener: (Long) -> Unit,
        totalDownloadedBytesListener: (Int) -> Unit
    ) {
        val fileResponse = syncRepository.downloadFile(url)
        if (!fileResponse.isSuccessful) {
            throw Exception("${fileResponse.code()} ${fileResponse.message()}")
        }

        val body = fileResponse.body() ?: throw IOException("Empty body for $url")
        val stream = body.byteStream()

        val fileSize = body.contentLength()
        totalFileSizeListener.invoke(fileSize)

        try {
            file.outputStream().use { outputStream ->
                val buffer = ByteArray(DEFAULT_BUFFER_SIZE)
                var bytesRead: Int

                while (stream.read(buffer).also { bytesRead = it } != -1) {
                    outputStream.write(buffer, 0, bytesRead)
                    totalDownloadedBytesListener.invoke(bytesRead)
                }
            }
        } catch (e: Exception) {
            file.delete()
            throw e
        }
    }

    private fun filterPayloadChanges(changes: List<Change>?) =
        changes?.firstOrNull { it.type == ChangeType.PAYLOAD }

    private fun filterRemoteChanges(changes: List<Change>?) =
        changes?.filter { it.type == ChangeType.REMOTE }

    private fun cancelSync() {
        coroutineScope.cancelChildCoroutines()
        clearData()
    }

    fun clearData() {
        progressFlow?.value =
            SyncManager.DownloadState.Downloading(0f, 0, null)
        totalBytesToDownload = 0L
        totalDownloadedBytes = 0L
        totalFiles = null
        downloadedFiles.clear()
        downloadingFiles.clear()
        processingFiles.clear()
        fileProcessingHelper.resetProcessedFileCount()
        fileProcessingHelper.resetSavedFilesCount()
        totalPayloads = 0
        totalPayloadsProcessed = 0
    }
}