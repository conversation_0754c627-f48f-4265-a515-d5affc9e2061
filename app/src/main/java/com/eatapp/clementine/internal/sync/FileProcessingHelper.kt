package com.eatapp.clementine.internal.sync

import android.util.Log
import com.eatapp.clementine.data.database.entities.ClosingPeriodEntity
import com.eatapp.clementine.data.database.entities.ConciergeEntity
import com.eatapp.clementine.data.database.entities.DayNoteEntity
import com.eatapp.clementine.data.database.entities.GuestEntity
import com.eatapp.clementine.data.database.entities.OnlineSeatingShiftEntity
import com.eatapp.clementine.data.database.entities.OrderEntity
import com.eatapp.clementine.data.database.entities.PaymentEntity
import com.eatapp.clementine.data.database.entities.PosRecordEntity
import com.eatapp.clementine.data.database.entities.ReservationEntity
import com.eatapp.clementine.data.database.entities.RestaurantServerEntity
import com.eatapp.clementine.data.database.entities.RestaurantUserEntity
import com.eatapp.clementine.data.database.entities.RoomEntity
import com.eatapp.clementine.data.database.entities.TableEntity
import com.eatapp.clementine.data.database.entities.TagEntity
import com.eatapp.clementine.data.network.body.Payload
import com.eatapp.clementine.data.network.body.SyncChangeType
import com.eatapp.clementine.data.network.body.SyncObjectType
import com.eatapp.clementine.data.network.body.SyncObjectType.CLOSING_PERIOD
import com.eatapp.clementine.data.network.body.SyncObjectType.CONCIERGE
import com.eatapp.clementine.data.network.body.SyncObjectType.DAY_NOTE
import com.eatapp.clementine.data.network.body.SyncObjectType.GUEST
import com.eatapp.clementine.data.network.body.SyncObjectType.ORDER
import com.eatapp.clementine.data.network.body.SyncObjectType.PAYMENT
import com.eatapp.clementine.data.network.body.SyncObjectType.POS_RECORD
import com.eatapp.clementine.data.network.body.SyncObjectType.RESERVATION
import com.eatapp.clementine.data.network.body.SyncObjectType.RESTAURANT_SERVER
import com.eatapp.clementine.data.network.body.SyncObjectType.ROOM
import com.eatapp.clementine.data.network.body.SyncObjectType.SHIFT
import com.eatapp.clementine.data.network.body.SyncObjectType.TABLE
import com.eatapp.clementine.data.network.body.SyncObjectType.TAG
import com.eatapp.clementine.data.network.body.SyncObjectType.USER
import com.eatapp.clementine.internal.cancelChildCoroutines
import com.eatapp.clementine.internal.isGzip
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.internal.managers.SyncManager
import com.eatapp.clementine.internal.managers.SyncManager.Companion.LOG_TAG
import com.google.gson.Gson
import com.google.gson.stream.JsonReader
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Semaphore
import kotlinx.coroutines.sync.withPermit
import kotlinx.coroutines.withContext
import java.io.File
import java.io.InputStreamReader
import java.util.Date
import java.util.concurrent.atomic.AtomicInteger
import java.util.zip.GZIPInputStream
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class FileProcessingHelper @Inject constructor(
    private val syncSaveHelper: SyncSaveHelper,
    private val gson: Gson,
    private val eatManager: EatManager
) {
    var progressFlow: MutableStateFlow<SyncManager.DownloadState>? = null

    private val processedFileCount = AtomicInteger(0)
    private val savedFileCount = AtomicInteger(0)
    private val processingSemaphore = Semaphore(4) // max 4 concurrent processors
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.IO) // IO for file access

    /**
     * Enqueues a file for processing. Parsing is limited to 4 files at a time.
     * @param file The file to process
     * @param onPayloadProcessed Callback to report payloads processed (pass 1 for each payload)
     */
    fun enqueueFileForProcessing(
        file: File,
        onFileProcessed: (() -> Unit)? = null,
        onFileSaved: (() -> Unit)? = null
    ): Job {
        return scope.launch {
            processingSemaphore.withPermit {
                try {
                    Log.i("Sync-engine", "Started processing: ${file.name}")
                    processDownloadedFile(file, onFileProcessed, onFileSaved)
                } catch (e: Exception) {
                    Log.e("Sync-engine", "Error processing file ${file.name}: ${e.message}", e)
                    scope.cancelChildCoroutines()
                    progressFlow?.value = SyncManager.DownloadState.Failed()
                } finally {
                    file.delete()
                }
            }
        }
    }

    fun getProcessedFileCount(): Int = processedFileCount.get()

    fun resetProcessedFileCount() {
        processedFileCount.set(0)
    }

    fun getSavedFilesCount(): Int = savedFileCount.get()

    fun resetSavedFilesCount() {
        savedFileCount.set(0)
    }

    private suspend fun processDownloadedFile(
        file: File,
        onFileProcessed: (() -> Unit)?,
        onFileSaved: (() -> Unit)?
    ) {
        val payloadHolder = RemoteBatchDataHolder(eatManager)
        withContext(Dispatchers.IO) {
            val inputStream = if (file.isGzip()) {
                GZIPInputStream(file.inputStream())
            } else {
                file.inputStream()
            }
            inputStream.use { gzInputStream ->
                JsonReader(InputStreamReader(gzInputStream)).use { jsonReader ->
                    jsonReader.beginArray()
                    while (jsonReader.hasNext()) {
                        gson.fromJson<Payload?>(
                            jsonReader,
                            Payload::class.java
                        )?.let {
                            payloadHolder.appendPayload(it)
                        }
                    }
                    jsonReader.endArray()
                }
            }
            processedFileCount.incrementAndGet()
            onFileProcessed?.invoke()
            Log.i(LOG_TAG, "Finished processing: ${file.name}")
            Log.i(LOG_TAG, "Started saving: ${file.name}")
            saveChanges(payloadHolder)
            savedFileCount.incrementAndGet()
            onFileSaved?.invoke()
            Log.i(LOG_TAG, "Finished saving: ${file.name}")
        }
    }

    private suspend fun saveChanges(
        remoteBatchDataHolder: RemoteBatchDataHolder
    ) {
        withContext(Dispatchers.IO) {
            Log.d(LOG_TAG, "Started sorting and saving file: ${Date()}")

            if (remoteBatchDataHolder.size() == 0) {
                Log.d(LOG_TAG, "Sync doesn't contain remote batch, results list is empty")
                return@withContext
            }

            val jobs = listOf(
                async {
                    saveData(
                        TAG,
                        remoteBatchDataHolder.get(TAG) as MutableList<TagEntity>,
                        syncSaveHelper::saveTags
                    )
                },
                async {
                    saveData(
                        RESTAURANT_SERVER,
                        remoteBatchDataHolder.get(RESTAURANT_SERVER) as MutableList<RestaurantServerEntity>,
                        syncSaveHelper::saveServers
                    )
                },
                async {
                    saveData(
                        USER,
                        remoteBatchDataHolder.get(USER) as MutableList<RestaurantUserEntity>,
                        syncSaveHelper::saveUsers
                    )
                },
                async {
                    saveData(
                        ROOM,
                        remoteBatchDataHolder.get(ROOM) as MutableList<RoomEntity>,
                        syncSaveHelper::saveRooms
                    )
                },
                async {
                    saveData(
                        TABLE,
                        remoteBatchDataHolder.get(TABLE) as MutableList<TableEntity>,
                        syncSaveHelper::saveTables
                    )
                },
                async {
                    saveData(
                        GUEST,
                        remoteBatchDataHolder.get(GUEST) as MutableList<GuestEntity>,
                        syncSaveHelper::saveGuests
                    )
                },
                async {
                    saveData(
                        CONCIERGE,
                        remoteBatchDataHolder.get(CONCIERGE) as MutableList<ConciergeEntity>,
                        syncSaveHelper::saveConcierges
                    )
                },
                async {
                    saveData(
                        SHIFT,
                        remoteBatchDataHolder.get(SHIFT) as MutableList<OnlineSeatingShiftEntity>,
                        syncSaveHelper::saveShifts
                    )
                },
                async {
                    saveData(
                        RESERVATION,
                        remoteBatchDataHolder.get(RESERVATION) as MutableList<ReservationEntity>,
                        syncSaveHelper::saveReservations
                    )
                },
                async {
                    Log.d(LOG_TAG, "Started saving data for saveReservationTableRelationships")
                    syncSaveHelper.saveReservationTableRelationships(remoteBatchDataHolder.getReservationTableRelationships())
                    Log.d(LOG_TAG, "Finished saving data for saveReservationTableRelationships")
                },
//                async {
//                    Log.d(LOG_TAG, "Started saving data for saveReservationPreferenceRelationships")
//                    syncSaveHelper.saveReservationPreferenceRelationships(initialSyncDataHolder.getReservationPreferenceRelationShips())
//                    Log.d(
//                        LOG_TAG,
//                        "Finished saving data for saveReservationPreferenceRelationships"
//                    )
//                },
                async {
                    saveData(
                        POS_RECORD,
                        remoteBatchDataHolder.get(POS_RECORD) as MutableList<PosRecordEntity>,
                        syncSaveHelper::savePosRecords
                    )
                },
                async {
                    saveData(
                        DAY_NOTE,
                        remoteBatchDataHolder.get(DAY_NOTE) as MutableList<DayNoteEntity>,
                        syncSaveHelper::saveDayNotes
                    )
                },
                async {
                    saveData(
                        PAYMENT,
                        remoteBatchDataHolder.get(PAYMENT) as MutableList<PaymentEntity>,
                        syncSaveHelper::savePayments
                    )
                },
                async {
                    saveData(
                        CLOSING_PERIOD,
                        remoteBatchDataHolder.get(CLOSING_PERIOD) as MutableList<ClosingPeriodEntity>,
                        syncSaveHelper::saveClosingPeriods
                    )
                },
                async {
                    saveData(
                        ORDER,
                        remoteBatchDataHolder.get(ORDER) as MutableList<OrderEntity>,
                        syncSaveHelper::saveOrders
                    )
                }
            )

            // Wait for all jobs to complete
            jobs.awaitAll()

            Log.d(LOG_TAG, "Finished sorting and saving file: ${Date()}")
            remoteBatchDataHolder.wipeData()
        }
    }

    suspend fun processPayloads(payloads: List<Payload>) = coroutineScope {
        Log.d(LOG_TAG, "Started processing payloads - ${Date()}")

        payloads.map { payload ->
            async {
                processingSemaphore.withPermit {
                    try {
                        when (payload.objectType) {
                            SyncObjectType.UNKNOWN -> Unit
                            RESERVATION -> processPayload<ReservationEntity>(
                                payload,
                                syncSaveHelper::updateReservation,
                                syncSaveHelper::deleteReservation
                            )

                            GUEST -> processPayload<GuestEntity>(
                                payload,
                                syncSaveHelper::updateGuest,
                                syncSaveHelper::deleteGuest
                            )

                            ROOM -> processPayload<RoomEntity>(
                                payload,
                                syncSaveHelper::updateRoom,
                                syncSaveHelper::deleteRoom
                            )

                            TABLE -> processPayload<TableEntity>(
                                payload,
                                syncSaveHelper::updateTable,
                                syncSaveHelper::deleteTable
                            )

                            USER -> processPayload<RestaurantUserEntity>(
                                payload,
                                syncSaveHelper::updateUser,
                                syncSaveHelper::deleteUser
                            )

                            RESTAURANT_SERVER -> processPayload<RestaurantServerEntity>(
                                payload,
                                syncSaveHelper::updateServer,
                                syncSaveHelper::deleteServer
                            )

                            POS_RECORD -> processPayload<PosRecordEntity>(
                                payload,
                                syncSaveHelper::updatePosRecord,
                                syncSaveHelper::deletePosRecord
                            )

                            DAY_NOTE -> processPayload<DayNoteEntity>(
                                payload,
                                syncSaveHelper::updateDayNote,
                                syncSaveHelper::deleteDayNote
                            )

                            PAYMENT -> processPayload<PaymentEntity>(
                                payload,
                                syncSaveHelper::updatePayment,
                                syncSaveHelper::deletePayment
                            )

                            SHIFT -> processPayload<OnlineSeatingShiftEntity>(
                                payload,
                                syncSaveHelper::updateShift,
                                syncSaveHelper::deleteShift
                            )

                            CLOSING_PERIOD -> processPayload<ClosingPeriodEntity>(
                                payload,
                                syncSaveHelper::updateClosingPeriod,
                                syncSaveHelper::deleteClosingPeriod
                            )

                            ORDER -> processPayload<OrderEntity>(
                                payload,
                                syncSaveHelper::updateOrder,
                                syncSaveHelper::deleteOrder
                            )

                            CONCIERGE -> processPayload<ConciergeEntity>(
                                payload,
                                syncSaveHelper::updateConcierge,
                                syncSaveHelper::deleteConcierge
                            )

                            TAG -> processPayload<TagEntity>(
                                payload,
                                syncSaveHelper::updateTag,
                                syncSaveHelper::deleteTag
                            )
                        }
                    } catch (e: Exception) {
                        Log.e(
                            LOG_TAG,
                            "Error processing payload (${payload.objectType}): ${e.message}",
                            e
                        )
                    }
                }
            }
        }.awaitAll()

        Log.d(LOG_TAG, "Finished processing payloads - ${Date()}")
    }

    private suspend fun <T> saveData(
        key: SyncObjectType,
        data: MutableList<T>,
        saveFunction: suspend (MutableList<T>) -> Unit
    ) {
        Log.d(LOG_TAG, "Started saving data for $key")
        saveFunction(data)
        Log.d(LOG_TAG, "Finished saving data for $key")
    }

    private suspend inline fun <reified T> processPayload(
        payload: Payload,
        updateMethod: suspend (T) -> Unit,
        deleteMethod: suspend (T) -> Unit
    ) {
        when (payload.changeType) {
            SyncChangeType.UPSERT -> updateMethod(payload.data as T)
            SyncChangeType.DELETE -> deleteMethod(payload.data as T)
            else -> {
                // Handle other cases if needed
            }
        }
    }
}