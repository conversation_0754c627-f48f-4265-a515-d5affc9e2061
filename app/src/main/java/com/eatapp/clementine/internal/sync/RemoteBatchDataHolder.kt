package com.eatapp.clementine.internal.sync

import com.eatapp.clementine.data.database.entities.ClosingPeriodEntity
import com.eatapp.clementine.data.database.entities.ConciergeEntity
import com.eatapp.clementine.data.database.entities.DayNoteEntity
import com.eatapp.clementine.data.database.entities.GuestEntity
import com.eatapp.clementine.data.database.entities.OnlineSeatingShiftEntity
import com.eatapp.clementine.data.database.entities.OrderEntity
import com.eatapp.clementine.data.database.entities.PaymentEntity
import com.eatapp.clementine.data.database.entities.PosRecordEntity
import com.eatapp.clementine.data.database.entities.ReservationEntity
import com.eatapp.clementine.data.database.entities.ReservationTableCrossRef
import com.eatapp.clementine.data.database.entities.RestaurantEntity
import com.eatapp.clementine.data.database.entities.RestaurantServerEntity
import com.eatapp.clementine.data.database.entities.RestaurantUserEntity
import com.eatapp.clementine.data.database.entities.RoomEntity
import com.eatapp.clementine.data.database.entities.TableEntity
import com.eatapp.clementine.data.database.entities.TagEntity
import com.eatapp.clementine.data.network.body.Payload
import com.eatapp.clementine.data.network.body.SyncObjectType
import com.eatapp.clementine.internal.managers.EatManager
import java.util.concurrent.ConcurrentHashMap

class RemoteBatchDataHolder(
    private val eatManager: EatManager
) {

    private val results = ConcurrentHashMap<SyncObjectType, MutableList<RestaurantEntity>>()
    private val reservationTableCrossRef = mutableListOf<ReservationTableCrossRef>()
//    private val reservationPreferenceCrossRef = mutableListOf<ReservationShiftCrossRef>()

    fun appendPayload(payload: Payload) {
        var list = results[payload.objectType]
        if (list == null) {
            list = mutableListOf()
        }
        when (payload.objectType) {
            SyncObjectType.UNKNOWN -> {

            }

            SyncObjectType.RESTAURANT_SERVER -> {
                val server = payload.data as RestaurantServerEntity
                if (server.color != null) {
                    storeEntity(server, list)
                }
            }

            SyncObjectType.USER -> {
                val user = payload.data as RestaurantUserEntity
                if (user.name != null) {
                    storeEntity(user, list)
                }
            }

            SyncObjectType.ROOM -> {
                storeEntity(payload.data as RoomEntity, list)
            }

            SyncObjectType.TABLE -> {
                val tableEntity = payload.data as TableEntity
                if (tableEntity.number != null && tableEntity.roomId != null) {
                    storeEntity(tableEntity, list)
                }
            }

            SyncObjectType.GUEST -> {
                val guestEntity = payload.data as GuestEntity
                if (!guestEntity.firstName.isNullOrEmpty()) {
                    storeEntity(payload.data, list)
                }
            }

            SyncObjectType.CONCIERGE -> {
                storeEntity(payload.data as ConciergeEntity, list)
            }

            SyncObjectType.SHIFT -> {
                storeEntity(payload.data as OnlineSeatingShiftEntity, list)
            }

            SyncObjectType.RESERVATION -> {
                val reservationEntity = payload.data as ReservationEntity

//                reservationPreferenceCrossRef.addAll(reservation.preferenceIds?.map {
//                    ReservationShiftCrossRef(reservation.reservationId, it)
//                } ?: emptyList())

                if (reservationEntity.startTime != null && reservationEntity.key != null) {
                    storeEntity(reservationEntity, list)
                    reservationTableCrossRef.addAll(reservationEntity.tableIds?.map {
                        ReservationTableCrossRef(reservationEntity.reservationId, it)
                    } ?: emptyList())
                }
            }

            SyncObjectType.POS_RECORD -> {
                storeEntity(payload.data as PosRecordEntity, list)
            }

            SyncObjectType.DAY_NOTE -> {
                storeEntity(payload.data as DayNoteEntity, list)
            }

            SyncObjectType.PAYMENT -> {
                storeEntity(payload.data as PaymentEntity, list)
            }

            SyncObjectType.CLOSING_PERIOD -> {
                storeEntity(payload.data as ClosingPeriodEntity, list)
            }

            SyncObjectType.ORDER -> {
                storeEntity(payload.data as OrderEntity, list)
            }

            SyncObjectType.TAG -> {
                val tagEntity = payload.data as TagEntity
                if (tagEntity.id != null && tagEntity.name != null) {
                    storeEntity(tagEntity, list)
                }
            }
        }
        results[payload.objectType] = list
    }

    private fun storeEntity(
        e: RestaurantEntity,
        list: MutableList<RestaurantEntity>
    ) {
        e.restaurantId = eatManager.restaurantId()
        list.add(e)
    }

    fun get(type: SyncObjectType): MutableList<RestaurantEntity> {
        return results[type] ?: mutableListOf()
    }

    fun size(): Int {
        return results.size
    }

    fun getReservationTableRelationships(): MutableList<ReservationTableCrossRef> {
        return reservationTableCrossRef
    }

//    fun getReservationPreferenceRelationShips(): MutableList<ReservationShiftCrossRef> {
//        return reservationPreferenceCrossRef
//    }

    fun wipeData() {
        results.clear()
//        reservationPreferenceCrossRef.clear()
        reservationTableCrossRef.clear()
    }
}