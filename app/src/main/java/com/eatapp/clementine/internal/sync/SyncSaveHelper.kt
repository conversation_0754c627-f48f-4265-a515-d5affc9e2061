package com.eatapp.clementine.internal.sync

import com.eatapp.clementine.data.database.dao.ClosingPeriodDao
import com.eatapp.clementine.data.database.dao.ConciergeDao
import com.eatapp.clementine.data.database.dao.DayNoteDao
import com.eatapp.clementine.data.database.dao.GuestDao
import com.eatapp.clementine.data.database.dao.OnlineSeatingShiftDao
import com.eatapp.clementine.data.database.dao.OrderDao
import com.eatapp.clementine.data.database.dao.PaymentDao
import com.eatapp.clementine.data.database.dao.PosRecordDao
import com.eatapp.clementine.data.database.dao.ReservationDao
import com.eatapp.clementine.data.database.dao.RestaurantServerDao
import com.eatapp.clementine.data.database.dao.RestaurantUserDao
import com.eatapp.clementine.data.database.dao.RoomDao
import com.eatapp.clementine.data.database.dao.SyncActionNumberDao
import com.eatapp.clementine.data.database.dao.TableDao
import com.eatapp.clementine.data.database.dao.TagDao
import com.eatapp.clementine.data.database.entities.ClosingPeriodEntity
import com.eatapp.clementine.data.database.entities.ConciergeEntity
import com.eatapp.clementine.data.database.entities.DayNoteEntity
import com.eatapp.clementine.data.database.entities.GuestEntity
import com.eatapp.clementine.data.database.entities.OnlineSeatingShiftEntity
import com.eatapp.clementine.data.database.entities.OrderEntity
import com.eatapp.clementine.data.database.entities.PaymentEntity
import com.eatapp.clementine.data.database.entities.PosRecordEntity
import com.eatapp.clementine.data.database.entities.ReservationEntity
import com.eatapp.clementine.data.database.entities.ReservationTableCrossRef
import com.eatapp.clementine.data.database.entities.RestaurantServerEntity
import com.eatapp.clementine.data.database.entities.RestaurantUserEntity
import com.eatapp.clementine.data.database.entities.RoomEntity
import com.eatapp.clementine.data.database.entities.SyncActionNumberEntity
import com.eatapp.clementine.data.database.entities.TableEntity
import com.eatapp.clementine.data.database.entities.TagEntity
import com.eatapp.clementine.internal.managers.EatManager
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SyncSaveHelper @Inject constructor(
    private val reservationDao: ReservationDao,
    private val tableDao: TableDao,
    private val guestDao: GuestDao,
    private val closingPeriodDao: ClosingPeriodDao,
    private val conciergeDao: ConciergeDao,
    private val dayNoteDao: DayNoteDao,
    private val shiftsDao: OnlineSeatingShiftDao,
    private val paymentDao: PaymentDao,
    private val posRecordDao: PosRecordDao,
    private val serversDao: RestaurantServerDao,
    private val usersDao: RestaurantUserDao,
    private val roomDao: RoomDao,
    private val orderDao: OrderDao,
    private val tagDao: TagDao,
    private val eatManager: EatManager,
    private val actionNumberDao: SyncActionNumberDao,
) {

    suspend fun updateReservation(reservationEntity: ReservationEntity) {
        reservationEntity.restaurantId = eatManager.restaurantId()
        reservationDao.updateReservation(reservationEntity)
        reservationDao.updateReservationTables(reservationEntity.reservationId, reservationEntity.tableIds ?: emptyList())
    }

    suspend fun saveReservations(reservationEntities: List<ReservationEntity>) {
        reservationDao.saveReservations(reservationEntities)
    }

    suspend fun saveReservationTableRelationships(relationships: List<ReservationTableCrossRef>) {
        reservationDao.insertReservationWithTables(relationships)
    }

//    suspend fun saveReservationPreferenceRelationships(relationships: List<ReservationShiftCrossRef>) {
//        reservationDao.insertReservationWithPreferences(relationships)
//    }

    suspend fun deleteReservation(reservationEntity: ReservationEntity) {
        reservationDao.deleteReservation(reservationEntity)
    }

    suspend fun updateTable(tableEntity: TableEntity) {
        tableEntity.restaurantId = eatManager.restaurantId()
        tableDao.updateTable(tableEntity)
    }

    suspend fun deleteTable(tableEntity: TableEntity) {
        tableDao.deleteTable(tableEntity)
    }

    suspend fun saveTables(tableEntities: List<TableEntity>) {
        tableDao.saveTables(tableEntities)
    }

    suspend fun updateGuest(guestEntity: GuestEntity) {
        guestEntity.restaurantId = eatManager.restaurantId()
        guestDao.updateGuest(guestEntity)
    }

    suspend fun deleteGuest(guestEntity: GuestEntity) {
        guestDao.deleteGuest(guestEntity)
    }

    suspend fun saveGuests(guestEntities: List<GuestEntity>) {
        guestDao.saveGuests(guestEntities)
    }

    suspend fun updateRoom(roomEntity: RoomEntity) {
        roomEntity.restaurantId = eatManager.restaurantId()
        roomDao.updateRoom(roomEntity)
    }

    suspend fun deleteRoom(roomEntity: RoomEntity) {
        roomDao.deleteRoom(roomEntity)
    }

    suspend fun saveRooms(roomEntities: List<RoomEntity>) {
        roomDao.saveRooms(roomEntities)
    }

    suspend fun updateUser(restaurantUserEntity: RestaurantUserEntity) {
        restaurantUserEntity.restaurantId = eatManager.restaurantId()
        usersDao.updateRestaurantUser(restaurantUserEntity)
    }

    suspend fun deleteUser(restaurantUserEntity: RestaurantUserEntity) {
        usersDao.deleteRestaurantUser(restaurantUserEntity)
    }

    suspend fun saveUsers(restaurantUserEntities: List<RestaurantUserEntity>) {
        usersDao.saveRestaurantUsers(restaurantUserEntities)
    }

    suspend fun updateServer(restaurantServerEntity: RestaurantServerEntity) {
        restaurantServerEntity.restaurantId = eatManager.restaurantId()
        serversDao.updateRestaurantServer(restaurantServerEntity)
    }

    suspend fun deleteServer(restaurantServerEntity: RestaurantServerEntity) {
        serversDao.deleteRestaurantServer(restaurantServerEntity)
    }

    suspend fun saveServers(servers: List<RestaurantServerEntity>) {
        serversDao.saveRestaurantServers(servers)
    }

    suspend fun updatePosRecord(posRecordEntity: PosRecordEntity) {
        posRecordEntity.restaurantId = eatManager.restaurantId()
        posRecordDao.updatePosRecord(posRecordEntity)
    }

    suspend fun deletePosRecord(posRecordEntity: PosRecordEntity) {
        posRecordDao.deletePosRecord(posRecordEntity)
    }

    suspend fun savePosRecords(posRecordEntities: List<PosRecordEntity>) {
        posRecordDao.savePosRecords(posRecordEntities)
    }

    suspend fun updateDayNote(dayNote: DayNoteEntity) {
        dayNote.restaurantId = eatManager.restaurantId()
        dayNoteDao.updateDayNote(dayNote)
    }

    suspend fun deleteDayNote(dayNote: DayNoteEntity) {
        dayNoteDao.deleteDayNote(dayNote)
    }

    suspend fun saveDayNotes(dayNotes: List<DayNoteEntity>) {
        dayNoteDao.saveDayNotes(dayNotes)
    }

    suspend fun updatePayment(paymentEntity: PaymentEntity) {
        paymentEntity.restaurantId = eatManager.restaurantId()
        paymentDao.updatePayment(paymentEntity)
    }

    suspend fun deletePayment(paymentEntity: PaymentEntity) {
        paymentDao.deletePayment(paymentEntity)
    }

    suspend fun savePayments(paymentEntities: List<PaymentEntity>) {
        paymentDao.savePayments(paymentEntities)
    }

    suspend fun updateShift(onlineSeatingShiftEntity: OnlineSeatingShiftEntity) {
        onlineSeatingShiftEntity.restaurantId = eatManager.restaurantId()
        shiftsDao.updateOnlineSeatingShift(onlineSeatingShiftEntity)
    }

    suspend fun deleteShift(onlineSeatingShiftEntity: OnlineSeatingShiftEntity) {
        shiftsDao.deleteOnlineSeatingShift(onlineSeatingShiftEntity)
    }

    suspend fun saveShifts(shifts: List<OnlineSeatingShiftEntity>) {
        shiftsDao.saveOnlineSeatingShifts(shifts)
    }

    suspend fun updateClosingPeriod(closingPeriodEntity: ClosingPeriodEntity) {
        closingPeriodEntity.restaurantId = eatManager.restaurantId()
        closingPeriodDao.updateClosingPeriod(closingPeriodEntity)
    }

    suspend fun deleteClosingPeriod(closingPeriodEntity: ClosingPeriodEntity) {
        closingPeriodDao.deleteClosingPeriod(closingPeriodEntity)
    }

    suspend fun saveClosingPeriods(closingPeriodEntities: List<ClosingPeriodEntity>) {
        closingPeriodDao.saveClosingPeriods(closingPeriodEntities)
    }

    suspend fun updateConcierge(conciergeEntity: ConciergeEntity) {
        conciergeEntity.restaurantId = eatManager.restaurantId()
        conciergeDao.updateConcierge(conciergeEntity)
    }

    suspend fun deleteConcierge(conciergeEntity: ConciergeEntity) {
        conciergeDao.deleteConcierge(conciergeEntity)
    }

    suspend fun saveConcierges(conciergeEntities: List<ConciergeEntity>) {
        conciergeDao.saveConcierges(conciergeEntities)
    }

    suspend fun updateOrder(orderEntity: OrderEntity) {
        orderEntity.restaurantId = eatManager.restaurantId()
        orderDao.updateOrder(orderEntity)
    }

    suspend fun deleteOrder(orderEntity: OrderEntity) {
        orderDao.deleteOrder(orderEntity)
    }

    suspend fun saveOrders(orderEntities: List<OrderEntity>) {
        orderDao.saveOrders(orderEntities)
    }

    suspend fun saveTags(tagEntities: List<TagEntity>) {
        tagDao.saveTags(tagEntities)
    }

    suspend fun updateTag(tagEntity: TagEntity) {
        tagEntity.restaurantId = eatManager.restaurantId()
        tagDao.updateTag(tagEntity)
    }

    suspend fun deleteTag(tagEntity: TagEntity) {
        tagDao.deleteTag(tagEntity)
    }

    suspend fun getActionNumber(): Int {
        return actionNumberDao.syncActionNumber(eatManager.restaurantId())
    }

    suspend fun saveActionNumber(actionNumber: Int) {
        val syncActionNumberEntity = SyncActionNumberEntity(
            eatManager.restaurantId(),
            actionNumber
        )

        actionNumberDao.updateActionNumber(syncActionNumberEntity)
    }
}