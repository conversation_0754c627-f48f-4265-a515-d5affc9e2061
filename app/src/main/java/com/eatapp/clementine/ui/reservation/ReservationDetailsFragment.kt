package com.eatapp.clementine.ui.reservation

import android.annotation.SuppressLint
import android.app.DatePickerDialog
import android.content.ActivityNotFoundException
import android.content.Intent
import android.net.Uri
import android.os.Handler
import android.os.Looper
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.MotionEvent
import android.view.View
import android.widget.CheckBox
import android.widget.CompoundButton
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.content.ContextCompat
import androidx.core.net.toUri
import androidx.core.view.updateLayoutParams
import androidx.core.widget.NestedScrollView
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.SimpleItemAnimator
import com.eatapp.clementine.R
import com.eatapp.clementine.VoucherUpdateState
import com.eatapp.clementine.adapter.ReservationDetailsAdapter
import com.eatapp.clementine.data.network.response.guest.Guest
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.network.response.room.Table
import com.eatapp.clementine.data.network.response.user.User
import com.eatapp.clementine.data.network.response.vouchers.Voucher
import com.eatapp.clementine.data.network.response.vouchers.VoucherAssignmentModel
import com.eatapp.clementine.databinding.ReservationDetailsFragmentBinding
import com.eatapp.clementine.internal.Constants
import com.eatapp.clementine.internal.Constants.GUEST_CREATE_MODE_EXTRA
import com.eatapp.clementine.internal.Constants.GUEST_EXTRA
import com.eatapp.clementine.internal.Constants.GUEST_EXTRA_EMAIL
import com.eatapp.clementine.internal.Constants.GUEST_EXTRA_FIRST_NAME
import com.eatapp.clementine.internal.Constants.GUEST_EXTRA_LAST_NAME
import com.eatapp.clementine.internal.Constants.GUEST_EXTRA_NEW_FLOW
import com.eatapp.clementine.internal.Constants.GUEST_EXTRA_PHONE_NUMBER
import com.eatapp.clementine.internal.Constants.GUEST_EXTRA_RESERVATION_ID
import com.eatapp.clementine.internal.Constants.GUEST_SIMPLIFIED_MODE_EXTRA
import com.eatapp.clementine.internal.Status
import com.eatapp.clementine.internal.managers.OptionType
import com.eatapp.clementine.internal.parcelableArrayList
import com.eatapp.clementine.internal.showConfirmationAlert
import com.eatapp.clementine.internal.showErrorAlert
import com.eatapp.clementine.internal.showToast
import com.eatapp.clementine.internal.visibleOrGone
import com.eatapp.clementine.ui.base.BaseFragment
import com.eatapp.clementine.ui.common.selector.CountrySelectorFragment
import com.eatapp.clementine.ui.common.tables.TablesActivity
import com.eatapp.clementine.ui.common.tables.TablesReservationDataHolder
import com.eatapp.clementine.ui.common.tables.conflict.ConflictItem
import com.eatapp.clementine.ui.guest.GuestActivity
import com.eatapp.clementine.ui.guest.GuestProfileFragment
import com.eatapp.clementine.ui.reservation.loyalty.LoyaltyPopupDialog
import com.eatapp.clementine.ui.reservation.loyalty.LoyaltySharedViewModel
import com.eatapp.clementine.ui.reservation.vouchers.VouchersPopupDialog
import com.eatapp.clementine.ui.reservation.vouchers.VouchersSharedViewModel
import com.eatapp.clementine.views.AdvancedGuestSearchListener
import com.eatapp.clementine.views.LoadingButton
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import java.net.URLEncoder
import java.nio.charset.StandardCharsets
import java.util.Calendar
import java.util.Collections
import java.util.Date
import java.util.Locale


enum class SelectionType {
    TAKER,
    EDITOR
}

@AndroidEntryPoint
class ReservationDetailsFragment :
    BaseFragment<ReservationDetailsViewModel, ReservationDetailsFragmentBinding>() {

    private lateinit var reservationListItemsAdapter: ReservationDetailsAdapter

    private var guestProfileFragment: GuestProfileFragment? = null

    private val sharedReservationViewModel by activityViewModels<SharedReservationViewModel>()
    private val vouchersSharedViewModel by viewModels<VouchersSharedViewModel>()
    private val loyaltySharedViewModel by viewModels<LoyaltySharedViewModel>()

    // Flag to prevent status update, when waitlist checkbox it triggered programmatically
    private var shouldUpdateStatus = true

    // Flag to enable auto fill of guests results in advanced guest search view.
    // The flow should auto fill only once per reservation
    private var allowAutoComplete = true

    // Flag to be able to proceed with reservation creation, if create/update button was clicked
    // while the advanced guest search input is not empty
    private var fromCreateReservationButton = false

    private var isUserScrolling = false

    override fun viewModelClass() = ReservationDetailsViewModel::class.java

    override fun inflateLayout() = ReservationDetailsFragmentBinding.inflate(layoutInflater)

    override fun viewCreated() {

        if (vm.reservation.value == null) {

            activity?.intent?.extras?.getString(Constants.RESTAURANT_ID_EXTRA)
                ?.let { vm.restaurantId(it) }

            val date = activity?.intent?.getSerializableExtra(Constants.DATE) as Date?

            (activity as? ReservationActivity)?.reservation
                ?.let {

                    vm.reservation(it)
                    vm.updateInitialReservationState(it)

                } ?: run {
                val emptyReservation = Reservation(date)

                if (activity?.intent?.extras?.getBoolean(Constants.IS_WAITLIST) == true)
                    emptyReservation.status = Status.Value.WAITLIST.code

                vm.reservation(emptyReservation)
                vm.updateInitialReservationState(emptyReservation)
            }

            activity?.intent?.extras?.getParcelable<Guest>(GUEST_EXTRA)
                ?.let { guest ->
                    vm.guest(guest)
                }

            activity?.intent?.extras?.getString(Constants.TABLE_ID_EXTRA)
                ?.let { t -> vm.tableId(t) }
        }

        sharedReservationViewModel.guest.observe(viewLifecycleOwner) {
            vm.guest(it)
            vm.sendMessage(it?.phone != null || it?.email != null)
            reservationListItemsAdapter.updateGuestTags(vm.guestTaggingsList())
            reservationListItemsAdapter.updateItem(ReservationDetailsItemType.SEND_MESSAGE)
        }

        sharedReservationViewModel.comments.observe(viewLifecycleOwner) {
            vm.updateComments(it)
            reservationListItemsAdapter.updateItem(ReservationDetailsItemType.COMMENTS)
        }

        sharedReservationViewModel.country.observe(viewLifecycleOwner) {
            binding.itemAdvancedGuestSearch.setCountry(it)
        }

        bindUi()
        observe()
    }

    override fun onResume() {
        super.onResume()
        vm.checkConflicts()
    }

    private var startGuestActivityIntent = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) {
        if (featureFlagsManager.showNewGuestFlow) {
            if (it.resultCode == Constants.GUEST_RESULT_UPDATED || it.resultCode == Constants.GUEST_RESULT_ADDED) {
                it.data?.getParcelableExtra<Guest>(GUEST_EXTRA)?.let {
                    populateGuestData(it)
                }
            }
        } else {
            if (it.resultCode == Constants.GUEST_RESULT_UPDATED) {
                val guest = it.data?.getParcelableExtra<Guest>(GUEST_EXTRA)
                vm.guest(guest)
                vm.sendMessage(guest?.phone != null || guest?.email != null)
                reservationListItemsAdapter.updateGuestTags(vm.guestTaggingsList())
                reservationListItemsAdapter.updateItem(ReservationDetailsItemType.SEND_MESSAGE)
            }
        }
    }

    private var startTablesActivityIntent = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) {
        if (it.resultCode == Constants.TABLES_RESULT) {
            val tables = it.data?.parcelableArrayList<Table>(Constants.TABLES_EXTRA)
            vm.updateTables(tables)
            reservationListItemsAdapter.updateItem(ReservationDetailsItemType.TABLE)
        }
    }

    override fun onShowLockdownView(view: View) {}

    override fun onHideLockdownView(view: View) {}

    private fun bindUi() {
        (binding.rvListItems.itemAnimator as? SimpleItemAnimator)?.supportsChangeAnimations = false
        setupSaveButton()
        setupTableReadyButton()
        bindReservationListItemsAdapter()
        setupAdvancedGuestSearch()
        setUpGuestProfile()
    }

    fun scrollToGuest() {
        if (::binding.isInitialized) {
            binding.containerScroll.smoothScrollTo(0, binding.guestProfileTitleTv.top)
        }
    }

    fun scrollToTop() {
        if (::binding.isInitialized) {
            binding.containerScroll.smoothScrollTo(0, binding.rvListItems.top)
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun bindReservationListItemsAdapter() {

        binding.containerScroll.setOnTouchListener { _, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN, MotionEvent.ACTION_MOVE -> isUserScrolling = true
                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> isUserScrolling = false
            }
            false
        }

        binding.containerScroll.setOnScrollChangeListener(NestedScrollView.OnScrollChangeListener { _, _, scrollY, _, oldScrollY ->
            if (scrollY != oldScrollY && isUserScrolling) {
                binding.itemAdvancedGuestSearch.clearFocus()
                hideKeyboard()
            }
        })

        val callback = object : ItemTouchHelper.SimpleCallback(
            ItemTouchHelper.UP or ItemTouchHelper.DOWN or ItemTouchHelper.START or ItemTouchHelper.END,
            0
        ) {
            override fun onMove(
                recyclerView: RecyclerView,
                viewHolder: RecyclerView.ViewHolder,
                target: RecyclerView.ViewHolder
            ): Boolean {
                val fromPosition = viewHolder.absoluteAdapterPosition
                val toPosition = target.absoluteAdapterPosition

                if (viewHolder is ReservationDetailsAdapter.DeleteReservationViewHolder
                    || target is ReservationDetailsAdapter.DeleteReservationViewHolder
                    || viewHolder is ReservationDetailsAdapter.GuestViewHolder
                    || target is ReservationDetailsAdapter.GuestViewHolder
                ) {
                    return false
                }

                Collections.swap(vm.reservationDetailsListItems.value!!, fromPosition, toPosition)
                vm.saveReservationDetailsListSorting()
                reservationListItemsAdapter.notifyItemMoved(fromPosition, toPosition)
                return true
            }

            override fun onSwiped(viewHolder: RecyclerView.ViewHolder, direction: Int) {}

            override fun getMovementFlags(
                recyclerView: RecyclerView,
                viewHolder: RecyclerView.ViewHolder
            ): Int {
                return if (viewHolder is ReservationDetailsAdapter.DeleteReservationViewHolder || viewHolder is ReservationDetailsAdapter.GuestViewHolder) {
                    makeFlag(
                        ItemTouchHelper.ACTION_STATE_IDLE,
                        ItemTouchHelper.DOWN or ItemTouchHelper.UP
                    )
                } else {
                    makeFlag(
                        ItemTouchHelper.ACTION_STATE_DRAG,
                        ItemTouchHelper.DOWN or ItemTouchHelper.UP or ItemTouchHelper.START or ItemTouchHelper.END
                    )
                }
            }
        }

        val touchHelper = ItemTouchHelper(callback)
        touchHelper.attachToRecyclerView(binding.rvListItems)

        reservationListItemsAdapter = ReservationDetailsAdapter(
            vm.reservation.value!!,
            vm.conflictList.value ?: emptyList(),
            vm.tagsAvailable,
            vm.hideReservationTaker,
            vm.deleteButtonVisible,
            vm.permissionReservation,
            vm.permissionGuest,
            vm.loyaltyActive,
            vm.isFreemium,
            vm.isMarketingVisible,
            vm.posActive,
            vm.currency,
            vm.reservationTaggingsList(),
            vm.guestTaggingsList(),
            vm.featureFlagsManager.variationWhatsappChatFlow,
            featureFlagsManager.showNewGuestFlow,
            vm.vouchers().isNotEmpty() && vm.vouchersEnabled(),
            addTagListener = {
                val list = vm.tagsList()
                bottomSheetGroupDialog(list, resources.getString(R.string.add_tags), false) {
                    vm.updateReservationTags(list)
                    reservationListItemsAdapter.updateReservationTags(vm.reservationTaggingsList())
                }
            }, removeTagListener = {
                vm.removeReservationTag(it)
                reservationListItemsAdapter.updateReservationTags(vm.reservationTaggingsList())
            })
        binding.rvListItems.adapter = reservationListItemsAdapter

        reservationListItemsAdapter.tableClickListener = {
            if (vm.tables.value.isNullOrEmpty()) {
                requireContext().showErrorAlert(
                    getString(R.string.missing_tables_title),
                    getString(R.string.missing_tables_description)
                )
            } else {
                openTables()
            }
        }

        reservationListItemsAdapter.statusClickListener = {
            when (vm.reservation.value?.locked == true) {
                true -> showLockAlert()
                false -> showStatusesList()
            }
        }

        reservationListItemsAdapter.dateClickListener = DateClickListener()

        reservationListItemsAdapter.timeUpdateListener = {
            when (vm.reservation.value?.locked == true) {
                true -> showLockAlert()
                false -> {
                    vm.updateTime(it)
                    reservationListItemsAdapter.updateItem(ReservationDetailsItemType.DATE_TIME)
                }
            }
        }
        reservationListItemsAdapter.timeClickListener = {
            when (vm.reservation.value?.locked == true) {
                true -> showLockAlert()
                false -> showTimesList()
            }
        }

        reservationListItemsAdapter.coversClickListener = {
            when (vm.reservation.value?.locked == true) {
                true -> showLockAlert()
                false -> showCoversList()
            }
        }

        reservationListItemsAdapter.coversUpdateListener = {
            when (vm.reservation.value?.locked == true) {
                true -> showLockAlert()
                false -> {
                    vm.updateCovers(it)
                    reservationListItemsAdapter.updateItem(ReservationDetailsItemType.COVERS_DURATION)
                }
            }
        }

        reservationListItemsAdapter.waitTimeUpdateListener = {
            when (vm.reservation.value?.locked == true) {
                true -> showLockAlert()
                false -> {
                    vm.updateWaitQuote(it)
                    reservationListItemsAdapter.updateItem(ReservationDetailsItemType.WAIT_TIME)
                }
            }
        }

        reservationListItemsAdapter.waitTimeClickListener = {
            when (vm.reservation.value?.locked == true) {
                true -> showLockAlert()
                false -> showWaitQuoteList()
            }
        }

        reservationListItemsAdapter.durationUpdateListener = {
            when (vm.reservation.value?.locked == true) {
                true -> showLockAlert()
                false -> {
                    vm.updateDuration(it)
                    reservationListItemsAdapter.updateItem(ReservationDetailsItemType.COVERS_DURATION)
                }
            }
        }

        reservationListItemsAdapter.durationClickListener = {
            when (vm.reservation.value?.locked == true) {
                true -> showLockAlert()
                false -> showDurationsList()
            }
        }

        reservationListItemsAdapter.commentsClickListener = {
            showComments()
        }

        reservationListItemsAdapter.walkInCheckedChangeListener =
            CompoundButton.OnCheckedChangeListener { _, isChecked ->

                if (vm.reservation.value?.walkIn == isChecked) {
                    return@OnCheckedChangeListener
                }

                vm.reservation.value?.walkIn = isChecked

                vm.onWalkinCheckedChanged(isChecked)

                val hasPhoneOrEmail =
                    vm.reservation.value?.guest?.phone != null || vm.reservation.value?.guest?.email != null
                vm.sendMessage(if (isChecked) false else hasPhoneOrEmail)

                reservationListItemsAdapter.updateItem(ReservationDetailsItemType.STATUS)
                reservationListItemsAdapter.updateItem(ReservationDetailsItemType.WALK_IN_WAITLIST)
                reservationListItemsAdapter.updateItem(ReservationDetailsItemType.SEND_MESSAGE)

                updateButtonTitle()
            }

        reservationListItemsAdapter.waitlistCheckedChangeListener =
            CompoundButton.OnCheckedChangeListener { _, isChecked ->

                val currentStatus = vm.reservation.value?.status
                if (isChecked && currentStatus == Status.Value.WAITLIST.code) {
                    return@OnCheckedChangeListener
                }

                if (!shouldUpdateStatus) {
                    return@OnCheckedChangeListener
                }

                when (isChecked) {
                    true -> vm.updateStatus(Status.Value.WAITLIST.code)
                    false -> vm.updateStatus(Status.Value.NOT_CONFIRMED.code)
                }

                if (isChecked) {
                    vm.sendMessage(false)
                }

                reservationListItemsAdapter.updateItem(ReservationDetailsItemType.STATUS)
                reservationListItemsAdapter.updateItem(ReservationDetailsItemType.WALK_IN_WAITLIST)
                reservationListItemsAdapter.updateItem(ReservationDetailsItemType.SEND_MESSAGE)
                reservationListItemsAdapter.updateItem(ReservationDetailsItemType.DATE_TIME)
                vm.checkConflicts()
            }

        reservationListItemsAdapter.notesChangedListener = object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                vm.updateNote(s.toString())
            }

            override fun afterTextChanged(s: Editable?) {}
        }

        reservationListItemsAdapter.takerClickListener = { showTakersList(SelectionType.TAKER) }

        reservationListItemsAdapter.guestChangeIconClickListener = {

            when (it) {
                true -> this.findNavController()
                    .navigate(ReservationFragmentDirections.guestsAction())

                false ->
                    if (featureFlagsManager.showNewGuestFlow) {
                        showAlert(
                            resources.getString(R.string.remove_guest),
                            resources.getString(R.string.edit_guest_desc),
                            positiveButtonText = resources.getString(R.string.remove_guest),
                            positiveButtonListener = {
                                vm.guest(null)
                                vm.sendMessage(false)
                                reservationListItemsAdapter.updateItem(ReservationDetailsItemType.GUEST)
                                reservationListItemsAdapter.updateItem(ReservationDetailsItemType.SEND_MESSAGE)
                                setupAdvancedGuestSearch()
                            },
                            negativeButtonText = resources.getString(R.string.cancel_label)
                        )
                    } else {
                        showAlert(
                            resources.getString(R.string.edit_guest),
                            resources.getString(R.string.edit_guest_desc),
                            positiveButtonText = resources.getString(R.string.add_new_guest),
                            positiveButtonListener = {
                                this.findNavController()
                                    .navigate(ReservationFragmentDirections.guestsAction())
                            },
                            negativeButtonText = resources.getString(R.string.remove_guest),
                            negativeButtonListener = {
                                vm.guest(null)
                                vm.sendMessage(false)
                                reservationListItemsAdapter.updateItem(ReservationDetailsItemType.GUEST)
                                reservationListItemsAdapter.updateItem(ReservationDetailsItemType.SEND_MESSAGE)
                            },
                            neutralButtonText = resources.getString(R.string.cancel_label)
                        )
                    }
            }
        }

        reservationListItemsAdapter.guestSearchLayoutClickListener = { navigateGuest() }

        reservationListItemsAdapter.guestDetailsClickListener = { navigateGuest() }

        reservationListItemsAdapter.guestPhoneNumberClickListener = {
            try {
                val intent = Intent(
                    Intent.ACTION_DIAL, Uri.fromParts(
                        "tel",
                        vm.reservation.value?.guest?.phone, null
                    )
                )
                startActivity(intent)
            } catch (_: Exception) {
            }
        }

        reservationListItemsAdapter.guestTagsChevronClickListener = {
            vm.toggleGuestTagsExpanded()
            reservationListItemsAdapter.updateItem(ReservationDetailsItemType.GUEST)
        }

        reservationListItemsAdapter.guestGoogleClickListener = {
            googleGuest()
        }

        reservationListItemsAdapter.guestWhatsappClickListener = {

            if (vm.shouldShowUpdate()) {

                showAlert(
                    title = getString(R.string.whatsapp_update_required_title),
                    description = getString(R.string.whatsapp_update_required_description),
                    positiveButtonText = getString(R.string.whatsapp_update_now),
                    positiveButtonListener = {
                        try {
                            startActivity(
                                Intent(
                                    Intent.ACTION_VIEW,
                                    getString(
                                        R.string.playstore_app_template,
                                        requireContext().packageName
                                    ).toUri()
                                )
                            )
                        } catch (e: ActivityNotFoundException) {
                            startActivity(
                                Intent(
                                    Intent.ACTION_VIEW,
                                    getString(
                                        R.string.playstore_web_template,
                                        requireContext().packageName
                                    ).toUri()
                                )
                            )
                        }
                    },
                    negativeButtonText = getString(R.string.whatsapp_update_cancel)
                )
            } else {

                val dateFormatter = java.text.SimpleDateFormat("dd/MM", Locale.US)
                val timeFormatter = java.text.SimpleDateFormat("hh:mm a", Locale.US)

                val message =
                    if (!vm.eatManager.isWhatsappUserAlreadyNotified(
                            vm.reservation.value?.id ?: ""
                        )
                    )
                        getString(
                            R.string.whatsapp_template,
                            vm.reservation.value?.guest?.firstName,
                            vm.eatManager.restaurant()?.name,
                            dateFormatter.format(vm.reservation.value?.startTime ?: Date()),
                            timeFormatter.format(vm.reservation.value?.startTime ?: Date()),
                            vm.reservation.value?.covers
                        )
                    else {
                        ""
                    }
                sendWhatsappMessage(vm.reservation.value?.guest?.phone ?: "", message)
            }
        }

        reservationListItemsAdapter.deleteReservationClickListener = {

            if (vm.permissionReservation.boolValue) {
                requireContext().showErrorAlert(
                    resources.getString(R.string.delete_reservation),
                    vm.permissionReservation.errorMessage
                )
            } else {
                requireContext().showConfirmationAlert(
                    R.string.delete_reservation_title,
                    R.string.delete_reservation_desc
                ) {
                    vm.deleteReservation()
                }
            }
        }

        reservationListItemsAdapter.marketingCheckedChangeListener =
            View.OnClickListener { view ->
                vm.updateGuest((view as? CheckBox)?.isChecked == true)
            }

        reservationListItemsAdapter.sendMessageCheckedChangeListener =
            CompoundButton.OnCheckedChangeListener { _, isChecked ->
                vm.sendMessage(isChecked)
            }

        reservationListItemsAdapter.customFieldUpdateListener = {
            vm.updateCustomFields(it)
        }

        reservationListItemsAdapter.customFieldCounterUpdateListener = {
            vm.updateCustomFields(it.first, it.second)
            reservationListItemsAdapter.updateCustomField(it.first)
        }

        reservationListItemsAdapter.customFieldCounterClickListener = {
            when (vm.reservation.value?.locked == true) {
                true -> showLockAlert()
                false -> showCustomFieldCountList(it)
            }
        }

        reservationListItemsAdapter.sourceClickListener = { currentlySelected ->
            showSourcesList(currentlySelected)
        }

        reservationListItemsAdapter.vouchersClickListener = {
            if (vm.reservationVouchers().isEmpty()) {
                requireContext().showToast(getString(R.string.assigned_vouchers_label_reservation))
            } else {
                vouchersSharedViewModel.updateVouchers(vm.reservationVouchers())
                val dialog =
                    VouchersPopupDialog.newInstance(VouchersPopupDialog.DialogType.ASSIGN)
                dialog.show(childFragmentManager, null)
            }
        }

        reservationListItemsAdapter.voucherClickListener = {
            if (it.redeemed) {
                requireContext().showToast(getString(R.string.voucher_redeemed_label))
            } else {
                vouchersSharedViewModel.updateVouchers(listOf(it))
                val dialog =
                    VouchersPopupDialog.newInstance(VouchersPopupDialog.DialogType.REDEEM)
                dialog.show(childFragmentManager, null)
            }
        }

        reservationListItemsAdapter.loyaltyClickListener = {
            showLoyaltyPopup()
        }
    }

    private fun setupSaveButton() {
        updateButtonTitle()

        if (featureFlagsManager.showNewGuestFlow) {
            binding.createBtn.setOnClickListener {

                if (!vm.reservation.value?.tempName.isNullOrEmpty() || vm.reservation.value?.guest != null) {
                    modifyReservation()
                    return@setOnClickListener
                }

                if (fromCreateReservationButton) {
                    modifyReservation()
                    return@setOnClickListener
                }

                if (binding.itemAdvancedGuestSearch.isInputEmpty()) {
                    modifyReservation()
                    return@setOnClickListener
                }

                fromCreateReservationButton = true
                vm.searchGuests(binding.itemAdvancedGuestSearch.query)
            }
        } else {
            binding.createBtn.setOnClickListener {
                modifyReservation()
            }
        }
    }

    private fun modifyReservation() {
        if (vm.createMode.value == true) {
            vm.createReservation()
        } else {
            if (vm.isReservationUpdated && editorRequired()) {

                val taker = vm.takers.value?.firstOrNull { it.name == vm.reservation.value?.createdBy }
                val pinRequired = taker?.takerPinRequired == true

                if (pinRequired) {
                    val action =
                        ReservationFragmentDirections.actionReservationDetailsFragmentToPinFragment(
                            taker?.pinCode!!
                        )
                    action.showEditorSelectionButton = true
                    findNavController().navigate(action)

                    sharedReservationViewModel.pin.observe(viewLifecycleOwner) { _ ->
                        handleEditorSelection(taker.id)
                        sharedReservationViewModel.pin.removeObservers(viewLifecycleOwner)
                    }
                } else {
                    showTakersList(SelectionType.EDITOR)
                }
            } else {
                vm.updateReservation()
            }
        }
    }

    private fun updateButtonTitle() {
        val createMode = vm.createMode.value ?: false
        val isWalkIn = vm.reservation.value?.walkIn ?: false

        val titleResId = if (createMode) {
            if (isWalkIn) {
                R.string.create_walk_in
            } else {
                R.string.create_reservation
            }
        } else {
            if (isWalkIn) {
                R.string.update_walk_in
            } else {
                R.string.update_reservation
            }
        }
        binding.createBtn.setTitle(
            getString(titleResId)
        )
    }

    private fun bindActionsStates(conflictList: List<ConflictItem>) {
        if (conflictList.any { it.permission?.optionValue == OptionType.Error.type }) {
            binding.createBtn.state = LoadingButton.LoadingButtonState.Disabled
        } else if (conflictList.any { it.permission?.optionValue == OptionType.Warning.type }) {
            binding.createBtn.state = LoadingButton.LoadingButtonState.PartiallyDisabled
        } else if (vm.isReservationUpdatable || vm.isFreemium) {
            binding.createBtn.state = LoadingButton.LoadingButtonState.Available
        } else {
            binding.createBtn.state = LoadingButton.LoadingButtonState.Disabled
        }
    }

    private fun setupTableReadyButton() {

        val hasPhoneNumberOrEmail = vm.reservation.value?.guest?.phone?.isNotEmpty() == true ||
                vm.reservation.value?.guest?.email?.isNotEmpty() == true

        binding.tableReadyBtn.setOnClickListener {

            if (!vm.manageReservationPermission.boolValue) {
                showErrorAlert(
                    requireContext().resources.getString(R.string.manage_reservation),
                    vm.manageReservationPermission.errorMessage
                )
                return@setOnClickListener
            }

            if (!hasPhoneNumberOrEmail) {
                showErrorAlert(
                    requireContext().resources.getString(R.string.phone_and_email),
                    requireContext().resources.getString(R.string.no_phone_and_email)
                )
                return@setOnClickListener
            }

            showAlert(
                requireContext().resources.getString(R.string.send_message),
                requireContext().resources.getString(R.string.send_message_desc),
                positiveButtonText = requireContext().resources.getString(R.string.send),
                positiveButtonListener = {
                    binding.tableReadyBtn.showLoading()
                    vm.sendTableIsReadyMessage()
                }, neutralButtonText = requireContext().resources.getString(R.string.cancel_label)
            )
        }

        binding.tableReadyBtn.state = LoadingButton.LoadingButtonState.AvailableLight

        binding.tableReadyBtn.visibility =
            when (vm.reservation.value?.status == Status.Value.WAITLIST.code
                    && vm.createMode.value == false) {
                true -> View.VISIBLE
                false -> View.GONE
            }

        if (vm.reservation.value?.waitlistTableReadyAt == null) {
            if (hasPhoneNumberOrEmail) {
                binding.tableReadyBtn.state = LoadingButton.LoadingButtonState.AvailableLight
            } else {
                binding.tableReadyBtn.state = LoadingButton.LoadingButtonState.PartiallyDisabled
            }
        } else {
            binding.tableReadyBtn.state = LoadingButton.LoadingButtonState.Disabled
        }

        val image = when (vm.reservation.value?.waitlistTableReadyAt == null) {
            true -> ContextCompat.getDrawable(requireContext(), R.drawable.ic_icon_table_ready)
            else -> ContextCompat.getDrawable(
                requireContext(),
                R.drawable.ic_icon_table_ready_message_sent
            )
        }

        binding.tableReadyBtn.setLeftIcon(image!!, R.color.grey800)

        val title = when (vm.reservation.value?.waitlistTableReadyAt == null) {
            true -> requireContext().resources.getString(R.string.table_ready)
            false -> String.format(
                "%s - %s %s", requireContext().resources.getString(R.string.table_ready),
                requireContext().resources.getString(R.string.sent_at),
                vm.reservation.value?.waitlistTableReadyAt
            )
        }

        binding.tableReadyBtn.setTitle(title)
    }

    private fun observe() {

        sharedReservationViewModel.showOtherEditorsClicked.observe(viewLifecycleOwner) {
            showTakersList(SelectionType.EDITOR)
        }

        vm.update.observe(viewLifecycleOwner) {
            when (it) {
                true -> binding.createBtn.showLoading()
                else -> {
                    binding.createBtn.hideLoading()
                    val intent = Intent()
                    intent.putExtra(Constants.INITIAL_STATUS_EXTRA, vm.initialStatus)
                    intent.putExtra(Constants.RESERVATION_EXTRA, vm.reservation.value)
                    activity?.setResult(Constants.RESERVATION_RESULT_UPDATED, intent)
                    activity?.finish()
                }
            }
        }

        vm.create.observe(viewLifecycleOwner) {
            when (it) {
                true -> binding.createBtn.showLoading()
                else -> {
                    binding.createBtn.hideLoading()
                    val intent = Intent()
                    intent.putExtra(Constants.RESERVATION_EXTRA, vm.reservation.value)
                    activity?.setResult(Constants.RESERVATION_RESULT_ADDED, intent)
                    activity?.finish()
                }
            }
        }

        vm.delete.observe(viewLifecycleOwner) {
            when (it) {
                true -> reservationListItemsAdapter.showDeleteLoading()
                else -> {
                    reservationListItemsAdapter.hideDeleteLoading()
                    val intent = Intent()
                    intent.putExtra(Constants.RESERVATION_EXTRA, vm.reservation.value)
                    activity?.setResult(Constants.RESERVATION_RESULT_DELETED, intent)
                    activity?.finish()
                }
            }
        }

        vm.conflictList.observe(viewLifecycleOwner) {
            reservationListItemsAdapter.updateConflicts(it)
            bindActionsStates(it)
        }

        vm.reservationTakerMandatory.observe(viewLifecycleOwner) {
            if (it) {
                showTakersList(SelectionType.TAKER, updateReservation = true)
            }
        }

        vm.reservationDetailsListItems.observe(viewLifecycleOwner) {
            Log.e("TIMING", "OBSERVER TRIGGERED")
            reservationListItemsAdapter.submitList(it)
        }

        vm.tableReadyMessageSent.observe(viewLifecycleOwner) {
            binding.tableReadyBtn.hideLoading()
            setupTableReadyButton()
        }

        vm.guests.observe(viewLifecycleOwner) {
            handleGuestsResponse(it ?: emptyList())
        }

        vm.loading.observe(viewLifecycleOwner) {
            binding.itemAdvancedGuestSearch.showButtonLoading(it)
        }

        vm.guestCreated.observe(viewLifecycleOwner) {
            it?.let {
                updateAdvancedGuestItemVisibility(false)
                populateGuestData(it)
            }
        }

        vm.tempGuestCreated.observe(viewLifecycleOwner) {
            updateAdvancedGuestItemVisibility(false)
            reservationListItemsAdapter.updateItem(ReservationDetailsItemType.GUEST)
        }

        vm.shouldProceedWithReservation.observe(viewLifecycleOwner) {
            modifyReservation()
        }

        vm.loading.observe(viewLifecycleOwner) {
            binding.itemAdvancedGuestSearch.showProgress(it)
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.lifecycle.repeatOnLifecycle(Lifecycle.State.STARTED) {
                vm.voucherState.collect { state ->
                    state.getContentIfNotHandled()?.let {
                        when (it) {
                            is VoucherUpdateState.Loading -> {
                                vouchersSharedViewModel.showVoucherLoading(true)
                            }

                            is VoucherUpdateState.Success -> {
                                vouchersSharedViewModel.showVoucherLoading(false)
                                vouchersSharedViewModel.dismissPopup()
                                reservationListItemsAdapter.updateItem(ReservationDetailsItemType.VOUCHER)
                            }

                            is VoucherUpdateState.Error -> {
                                vouchersSharedViewModel.showVoucherLoading(false)
                            }

                            is VoucherUpdateState.Idle -> { /* Do nothing */
                            }
                        }
                    }
                }
            }
        }

        vm.loyaltyPointsUpdate.observe(viewLifecycleOwner) {
            it?.let {
                if (it) {
                    loyaltySharedViewModel.showPopupLoading(true)
                } else {
                    loyaltySharedViewModel.showPopupLoading(false)
                    loyaltySharedViewModel.dismissPopup()
                    reservationListItemsAdapter.updateItem(ReservationDetailsItemType.LOYALTY)
                }
            }
        }

        vouchersSharedViewModel.assignVouchers.observe(viewLifecycleOwner) { vouchers ->
            if (vouchers.isEmpty()) {
                requireContext().showToast(getString(R.string.empty_assign_list_label))
            } else {
                if (editorRequired()) {
                    showVoucherEditorList(false, null, vouchers)
                } else {
                    vm.updateVoucherAssignments(vouchers)
                }
            }
        }

        vouchersSharedViewModel.redeemVoucher.observe(viewLifecycleOwner) { voucherAssignment ->
            if (editorRequired()) {
                showVoucherEditorList(true, voucherAssignment, null)
            } else {
                vm.redeemVoucher(voucherAssignment)
            }
        }

        loyaltySharedViewModel.updatedPoints.observe(viewLifecycleOwner) {
            vm.updateLoyaltyPoints(it)
        }
    }

    private fun setUpGuestProfile() {
        val guest = vm.reservation.value?.guest

        binding.guestProfileGp.visibleOrGone = guest != null

        if (guest == null) return

        if (guestProfileFragment == null) {
            guestProfileFragment = GuestProfileFragment.newInstance(
                guest = guest.clone(), simplifiedMode = true, reservationId = vm.reservation.value?.id, embeddedMode = true
            )

            activity?.supportFragmentManager?.beginTransaction()?.replace(binding.guestProfileFc.id, guestProfileFragment!!)?.commit()
        } else {
            guestProfileFragment!!.updateGuest(guest)
        }
    }

    private fun setupAdvancedGuestSearch() = with(binding) {
        if (!featureFlagsManager.showNewGuestFlow || !vm.permissionGuest.boolValue) {
            updateAdvancedGuestItemVisibility(false)
            return
        }

        if (vm.reservation.value?.guest != null || !vm.reservation.value?.tempName.isNullOrEmpty()) {
            updateAdvancedGuestItemVisibility(false)
            return
        }

        updateAdvancedGuestItemVisibility(true)
        itemAdvancedGuestSearch.setCountry(vm.eatManager.restaurantCountry())
        itemAdvancedGuestSearch.setCountries(vm.eatManager.countries())
        itemAdvancedGuestSearch.listener = object : AdvancedGuestSearchListener {
            override fun onFocusChanged(query: String) {
                vm.searchGuests(query)

                vm.sendMessage(
                    binding.itemAdvancedGuestSearch.phoneText().isNotEmpty()
                            || binding.itemAdvancedGuestSearch.emailText().isNotEmpty()
                )
                reservationListItemsAdapter.updateSendMessage(
                    binding.itemAdvancedGuestSearch.phoneText(),
                    binding.itemAdvancedGuestSearch.emailText()
                )
            }

            override fun onGuestSelected(guest: Guest) {
                populateGuestData(guest)
            }

            override fun onMoreDetailsClicked(guestDraft: Guest) {
                vm.analyticsManager.trackViewGuestProfile()
                val intent = Intent(context, GuestActivity::class.java)
                intent.putExtra(GUEST_CREATE_MODE_EXTRA, true)
                intent.putExtra(GUEST_EXTRA_FIRST_NAME, guestDraft.firstName)
                intent.putExtra(GUEST_EXTRA_LAST_NAME, guestDraft.lastName)
                intent.putExtra(GUEST_EXTRA_PHONE_NUMBER, guestDraft.phone)
                intent.putExtra(GUEST_EXTRA_EMAIL, guestDraft.email)
                intent.putExtra(GUEST_EXTRA_NEW_FLOW, true)
                intent.putExtra(GUEST_EXTRA_RESERVATION_ID, vm.reservation.value?.id)
                startGuestActivityIntent.launch(intent)
            }

            override fun onGuestListExpanded(expanded: Boolean) {
                if (expanded) {
                    itemAdvancedGuestSearch.updateLayoutParams<ConstraintLayout.LayoutParams> {
                        bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                    }
                } else {
                    itemAdvancedGuestSearch.updateLayoutParams<ConstraintLayout.LayoutParams> {
                        bottomToBottom = ConstraintLayout.LayoutParams.UNSET
                    }
                }
                bottomButtons.visibleOrGone = !expanded
            }

            override fun onAutoCompletePerformed() {
                allowAutoComplete = false
            }
        }

        itemAdvancedGuestSearch.setOnFlagClickListener {
            val fragment =
                CountrySelectorFragment.newInstance(binding.itemAdvancedGuestSearch.currentCountry()?.code)
            bottomSheetFragment(getString(R.string.phone_selector_title), fragment)
        }

        itemAdvancedGuestSearch.setOnSaveGuestClickListener {
            if (vm.loading.value == true) {
                return@setOnSaveGuestClickListener
            }
            vm.createGuest(it)
        }
    }

    private fun handleGuestsResponse(guests: List<Guest>) {
        when (guests.size) {
            0 -> {
                if (fromCreateReservationButton) {
                    fromCreateReservationButton = false
                    val guest = binding.itemAdvancedGuestSearch.draftGuest()
                    vm.createGuest(guest, true)
                } else {
                    binding.itemAdvancedGuestSearch.noGuestsFound()
                }
            }

            1 -> {
                val guest = guests.first()

                if (fromCreateReservationButton) {
                    vm.guest(guest)
                    binding.createBtn.performClick()
                    fromCreateReservationButton = false
                } else {
                    binding.itemAdvancedGuestSearch.singleGuestFound(guest, allowAutoComplete)
                    binding.itemAdvancedGuestSearch.setCountry(vm.country(guest.phone))
                }
            }

            else -> {
                binding.itemAdvancedGuestSearch.multipleGuestFound(guests)

                if (fromCreateReservationButton) {
                    fromCreateReservationButton = false
                    binding.itemAdvancedGuestSearch.updateListVisibility(true)
                }
            }
        }
    }

    private fun populateGuestData(guest: Guest) {
        updateAdvancedGuestItemVisibility(false)
        vm.guest(guest)
        vm.sendMessage(guest.phone != null || guest.email != null)
        reservationListItemsAdapter.updateGuestTags(vm.guestTaggingsList())
        reservationListItemsAdapter.updateSendMessage(
            binding.itemAdvancedGuestSearch.phoneText(),
            binding.itemAdvancedGuestSearch.emailText()
        )
    }

    private fun updateAdvancedGuestItemVisibility(visible: Boolean) {
        binding.itemAdvancedGuestSearch.visibleOrGone = visible

        val constraintSet = ConstraintSet()
        constraintSet.clone(binding.container)
        constraintSet.clear(binding.rvListItems.id, ConstraintSet.TOP)

        if (visible) {
            constraintSet.connect(
                binding.rvListItems.id,
                ConstraintSet.TOP,
                binding.itemAdvancedGuestSearch.id,
                ConstraintSet.BOTTOM
            )
        } else {
            constraintSet.connect(
                binding.rvListItems.id,
                ConstraintSet.TOP,
                ConstraintSet.PARENT_ID,
                ConstraintSet.TOP
            )
            binding.itemAdvancedGuestSearch.updateListVisibility(false)
        }

        constraintSet.applyTo(binding.container)
    }

    private fun navigateGuest() {

        when (vm.reservation.value?.guest == null) {
            true -> {
                if (!vm.reservation.value?.tempName.isNullOrEmpty()) return
                this.findNavController()
                    .navigate(ReservationFragmentDirections.guestsAction())
            }

            false -> {
                vm.analyticsManager.trackViewGuestProfile()
                val intent = Intent(context, GuestActivity::class.java)
                intent.putExtra(GUEST_EXTRA, vm.reservation.value?.guest?.clone())
                intent.putExtra(GUEST_SIMPLIFIED_MODE_EXTRA, true)
                intent.putExtra(GUEST_EXTRA_RESERVATION_ID, vm.reservation.value?.id)
                startGuestActivityIntent.launch(intent)
            }
        }
    }

    private fun showStatusesList() {
        val list = Status.statusesList(
            vm.reservation.value?.status,
            vm.isFreemium,
            vm.reservation.value?.startTime ?: Date()
        )
        bottomSheetDialog(list, "", true) {
            shouldUpdateStatus = false
            vm.updateStatus(list.first { it.isSelected }.value as String)
            vm.userChangedReservationStatus = true
            reservationListItemsAdapter.updateItem(ReservationDetailsItemType.STATUS)
            reservationListItemsAdapter.updateItem(ReservationDetailsItemType.WAIT_TIME)
            reservationListItemsAdapter.updateItem(ReservationDetailsItemType.SEND_MESSAGE)
            reservationListItemsAdapter.updateItem(ReservationDetailsItemType.WALK_IN_WAITLIST)
            reservationListItemsAdapter.updateItem(ReservationDetailsItemType.DATE_TIME)
            // Delay is added to allow UI to perform updates on checkbox
            Handler(Looper.getMainLooper()).postDelayed({
                shouldUpdateStatus = true
            }, 500)
            vm.checkConflicts()
        }
    }

    private fun showTimesList() {

        val list = vm.timesList()
        bottomSheetDialog(list, "", true, centerItemHorizontally = true) {
            vm.updateTime(list.first { it.isSelected }.value as Date)
            reservationListItemsAdapter.updateItem(ReservationDetailsItemType.DATE_TIME)
        }
    }

    private fun showCoversList() {

        val list = vm.counterList(CounterListType.COVERS)
        bottomSheetDialog(list, "", true, centerItemHorizontally = true) {
            vm.updateCovers(list.first { it.isSelected }.value as Int)
            reservationListItemsAdapter.updateItem(ReservationDetailsItemType.COVERS_DURATION)
        }
    }

    private fun showDurationsList() {

        val list = vm.durationsList()
        bottomSheetDialog(list, "", true, centerItemHorizontally = true) {
            vm.updateDuration(list.first { it.isSelected }.value as Int)
            reservationListItemsAdapter.updateItem(ReservationDetailsItemType.COVERS_DURATION)
            reservationListItemsAdapter.updateItem(ReservationDetailsItemType.TABLE)
        }
    }

    private fun showWaitQuoteList() {

        val list = vm.waitQuoteList()
        bottomSheetDialog(list, "", true, centerItemHorizontally = true) {
            vm.updateWaitQuote(list?.first { it.isSelected }?.value as Int)
            reservationListItemsAdapter.updateItem(ReservationDetailsItemType.WAIT_TIME)
        }
    }

    private fun showSourcesList(currentlySelected: String?) {
        if (vm.reservation.value?.online == true) {
            Toast.makeText(
                requireContext(),
                getString(R.string.online_booking_source_message),
                Toast.LENGTH_SHORT
            )
                .show()
            return
        }

        if (vm.reservation.value?.relationships?.concierge?.data != null) {
            Toast.makeText(
                requireContext(),
                getString(R.string.concierge_booking_source_message),
                Toast.LENGTH_SHORT
            )
                .show()
            return
        }

        val list = vm.sourcesList(currentlySelected)
        bottomSheetDialog(list, "", true, allowDeselect = true) {
            vm.updateSource(it?.id)
            reservationListItemsAdapter.updateItem(ReservationDetailsItemType.SOURCE)
        }
    }

    private fun showTakersList(type: SelectionType, updateReservation: Boolean = false) {
        val list = vm.takersList()

        if (type == SelectionType.EDITOR) {
            if (vm.reservationEditorPermission.boolValue && list?.isEmpty() == true) {
                requireContext().showErrorAlert(
                    getString(R.string.error),
                    getString(R.string.no_reservation_taker_configured)
                )
                return
            }
        }

        if (type == SelectionType.TAKER) {
            if (vm.permissionReservationTaker.boolValue && list?.isEmpty() == true) {
                requireContext().showErrorAlert(
                    getString(R.string.error),
                    getString(R.string.no_reservation_taker_configured)
                )
                return
            }
        }

        val title =
            if (type == SelectionType.TAKER) requireContext().getString(R.string.reservation_by_hint) else requireContext().getString(
                R.string.reservation_edited_by_hint
            )
        bottomSheetDialog(
            list,
            "",
            true,
            allowDeselect = type == SelectionType.TAKER,
            subtitle = title
        ) {

            val user = list?.firstOrNull { user -> user.isSelected }?.value as? User
            val takerPinRequired = user?.takerPinRequired == true

            if (takerPinRequired) {
                val action =
                    ReservationFragmentDirections.actionReservationDetailsFragmentToPinFragment(
                        user!!.pinCode!!
                    )
                findNavController().navigate(action)

                sharedReservationViewModel.pin.observe(viewLifecycleOwner) { _ ->
                    if (type == SelectionType.TAKER) {
                        handleTakerSelection(it?.name, updateReservation)
                    }
                    if (type == SelectionType.EDITOR) {
                        handleEditorSelection(user.id)
                    }
                    sharedReservationViewModel.pin.removeObservers(viewLifecycleOwner)
                }
            } else {
                if (type == SelectionType.TAKER) {
                    handleTakerSelection(it?.name, updateReservation)
                }
                if (type == SelectionType.EDITOR) {
                    handleEditorSelection(user!!.id)
                }
            }
        }
    }

    private fun showVoucherEditorList(
        redeem: Boolean,
        voucherAssignment: VoucherAssignmentModel?,
        vouchers: List<Voucher>?
    ) {
        val list = vm.takersList()

        if (vm.reservationEditorPermission.boolValue && list?.isEmpty() == true) {
            requireContext().showErrorAlert(
                getString(R.string.error),
                getString(R.string.no_reservation_taker_configured)
            )
            return
        }

        val title = requireContext().getString(R.string.reservation_edited_by_hint)

        bottomSheetDialog(
            list,
            "",
            true,
            allowDeselect = false,
            subtitle = title
        ) {
            val user = list?.firstOrNull { user -> user.isSelected }?.value as? User
            val takerPinRequired = user?.takerPinRequired == true

            if (takerPinRequired) {
                vouchersSharedViewModel.showVoucherPopup(false)
                val action =
                    ReservationFragmentDirections.actionReservationDetailsFragmentToPinFragment(
                        user!!.pinCode!!
                    )
                findNavController().navigate(action)

                sharedReservationViewModel.pin.observe(viewLifecycleOwner) { _ ->
                    vouchersSharedViewModel.showVoucherPopup(true)
                    vm.updateEditor(user.id)
                    if (redeem) {
                        vm.redeemVoucher(voucherAssignment!!)
                    } else {
                        vm.updateVoucherAssignments(vouchers!!)
                    }
                    sharedReservationViewModel.pin.removeObservers(viewLifecycleOwner)
                }
            } else {
                vm.updateEditor(user!!.id)
                if (redeem) {
                    vm.redeemVoucher(voucherAssignment!!)
                } else {
                    vm.updateVoucherAssignments(vouchers!!)
                }
            }
        }
    }

    private fun handleTakerSelection(takerName: String?, updateReservation: Boolean) {
        vm.updateTaker(takerName)
        reservationListItemsAdapter.updateItem(ReservationDetailsItemType.TAKER)
        if (updateReservation) {
            if (vm.createMode.value == true) {
                vm.createReservation()
            } else {
                vm.updateReservation()
            }
        }
    }

    private fun handleEditorSelection(userId: String) {
        vm.updateEditor(userId)
        vm.updateReservation()
    }

    private fun showComments() {
        this.findNavController()
            .navigate(
                ReservationFragmentDirections
                    .commentsAction(vm.reservation.value!!)
            )
    }

    private fun showCustomFieldCountList(name: String) {

        val list = vm.counterList(CounterListType.CUSTOM_FIELD, customFieldName = name)
        bottomSheetDialog(list, "", true, centerItemHorizontally = true) {
            vm.updateCustomFields(Pair(name, list.first { it.isSelected }.value as Int))
            reservationListItemsAdapter.updateCustomField(name)
        }
    }

    private fun showLoyaltyPopup() {
        if (!vm.permissionGuest.boolValue) {
            requireContext().showErrorAlert(
                getString(R.string.edit_guest),
                vm.permissionGuest.errorMessage
            )
            return
        }
        val dialog = LoyaltyPopupDialog.newInstance(vm.reservation.value?.guest!!)
        dialog.show(childFragmentManager, null)
    }

    private inner class DateClickListener : View.OnClickListener {

        override fun onClick(view: View) {

            when (vm.reservation.value?.locked == true) {
                true -> showLockAlert()
                false -> {
                    val c = Calendar.getInstance()

                    c.time = when (vm.reservation.value?.status == Status.Value.WAITLIST.code) {
                        true -> vm.reservation.value?.waitlistQueuedAt
                        else -> vm.reservation.value?.startTime
                    } ?: Date()

                    val datePicker = DatePickerDialog(
                        requireContext(), { _, y, m, d ->

                            vm.updateDate(y, m, d)
                            reservationListItemsAdapter.updateItem(ReservationDetailsItemType.DATE_TIME)

                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH)
                    )

                    datePicker.show()
                }
            }
        }
    }

    private fun openTables() {
        val intent = Intent(context, TablesActivity::class.java)
        intent.putParcelableArrayListExtra(Constants.CLOSINGS_EXTRA, ArrayList(vm.closings))

        TablesReservationDataHolder.reservations = vm.reservations

        intent.putExtra(Constants.RESTAURANT_ID_EXTRA, vm.restaurantId)
        intent.putExtra(Constants.RESERVATION_EXTRA, vm.reservation.value)
        startTablesActivityIntent.launch(intent)
    }

    private fun showLockAlert() {
        context?.resources?.let {
            requireContext().showErrorAlert(
                it.getString(R.string.reservation_locked_title),
                getString(R.string.reservation_locked_desc)
            )
        }
    }

    private fun googleGuest() {
        val q =
            "${vm.reservation.value?.guestName} ${vm.reservation.value?.guest?.email ?: ""} ${vm.reservation.value?.guest?.attributes?.organisation ?: ""}"

        val browserIntent =
            Intent(Intent.ACTION_VIEW, Uri.parse(getString(R.string.google_query_template, q)))
        startActivity(browserIntent)
    }

    private fun sendWhatsappMessage(phoneNumber: String, message: String) {
        try {
            val i = Intent(Intent.ACTION_VIEW)

            val url = getString(
                R.string.whatsapp_api_template, phoneNumber, URLEncoder.encode(
                    message,
                    StandardCharsets.UTF_8.name()
                )
            )

            i.setPackage(getString(R.string.whatsapp_package_name))
            i.data = Uri.parse(url)
            startActivity(i)

            vm.eatManager.whatsappUserAlreadyNotified(vm.reservation.value?.id ?: "", true)
        } catch (e: java.lang.Exception) {
            openWhatsappWeb()
        }
    }

    private fun openWhatsappWeb() {
        val url = getString(R.string.whatsapp_web_url)

        val browserIntent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
        startActivity(browserIntent)
    }

    private fun editorRequired(): Boolean {
        return vm.reservationEditorPermission.boolValue && vm.reservation.value?.attributes?.editedBy.isNullOrEmpty()
    }
}