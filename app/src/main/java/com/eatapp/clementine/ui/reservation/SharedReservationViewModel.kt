package com.eatapp.clementine.ui.reservation

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.eatapp.clementine.data.network.response.apiresources.Country
import com.eatapp.clementine.data.network.response.comment.CommentModel
import com.eatapp.clementine.data.network.response.guest.Guest
import com.eatapp.clementine.internal.SingleLiveEvent
import com.eatapp.clementine.internal.asLiveData

class SharedReservationViewModel : ViewModel() {

    private val _comments = MutableLiveData<MutableList<CommentModel>?>()
    val comments: LiveData<MutableList<CommentModel>?> by lazy {
        _comments.asLiveData()
    }

    private val _guest = MutableLiveData<Guest?>()
    val guest: LiveData<Guest?> by lazy {
        _guest.asLiveData()
    }

    private val _pin = SingleLiveEvent<String?>()
    val pin: LiveData<String?> by lazy {
        _pin.asLiveData()
    }

    private val _country = SingleLiveEvent<Country?>()
    val country: LiveData<Country?> by lazy {
        _country.asLiveData()
    }

    private val _showOtherEditorsClicked = SingleLiveEvent<Void>()
    val showOtherEditorsClicked: LiveData<Void> = _showOtherEditorsClicked

    fun comments(comments: MutableList<CommentModel>?) {
        _comments.postValue(comments)
    }

    fun guest(guest: Guest?) {
        _guest.postValue(guest)
    }

    fun pin(pin: String?) {
        _pin.postValue(pin)
    }

    fun showOtherEditorsClicked() {
        _showOtherEditorsClicked.call()
    }

    fun country(country: Country) {
        _country.value = country
    }
}