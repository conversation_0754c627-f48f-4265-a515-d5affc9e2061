package com.eatapp.clementine.views

import android.app.Dialog
import android.content.Context
import android.content.res.ColorStateList
import android.graphics.Color
import android.graphics.Rect
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.FrameLayout
import android.widget.LinearLayout
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.view.ViewCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import com.eatapp.clementine.R
import com.eatapp.clementine.databinding.BottomSheetFragmentLayoutBinding
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.google.android.material.shape.CornerFamily
import com.google.android.material.shape.MaterialShapeDrawable
import com.google.android.material.shape.ShapeAppearanceModel
import net.yslibrary.android.keyboardvisibilityevent.KeyboardVisibilityEvent
import net.yslibrary.android.keyboardvisibilityevent.Unregistrar
import kotlin.math.roundToInt

class BottomSheetFragment : BottomSheetDialogFragment() {

    private var bottomSheetNavHeight = 0
    private var unRegistrar: Unregistrar? = null

    private lateinit var binding: BottomSheetFragmentLayoutBinding

    private var pendingFragment: Fragment? = null
    private var sheetTitle: String? = null
    private var draggable: Boolean = false
    private var cancellable: Boolean = false
    private var showTitle: Boolean = false
    private var webViewMinusPadding: Boolean = false
    var onDismiss: (() -> Unit)? = null

    companion object {
        private const val ARG_SHEET_TITLE = "arg_sheet_title"
        private const val ARG_DRAGGABLE = "arg_draggable"
        private const val ARG_CANCELLABLE = "arg_cancellable"
        private const val ARG_SHOW_TITLE = "arg_show_title"
        private const val ARG_WEB_VIEW_MINUS_PADDING = "arg_web_view_minus_padding"

        @JvmStatic
        fun newInstance(
            sheetTitle: String?,
            draggable: Boolean,
            cancellable: Boolean,
            showTitle: Boolean,
            webViewMinusPadding: Boolean,
            onDismiss: (() -> Unit)? = null
        ) = BottomSheetFragment().apply {
            arguments = Bundle().apply {
                putString(ARG_SHEET_TITLE, sheetTitle)
                putBoolean(ARG_DRAGGABLE, draggable)
                putBoolean(ARG_CANCELLABLE, cancellable)
                putBoolean(ARG_SHOW_TITLE, showTitle)
                putBoolean(ARG_WEB_VIEW_MINUS_PADDING, webViewMinusPadding)
            }
            this.onDismiss = onDismiss
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            sheetTitle = it.getString(ARG_SHEET_TITLE)
            draggable = it.getBoolean(ARG_DRAGGABLE, false)
            cancellable = it.getBoolean(ARG_CANCELLABLE, false)
            showTitle = it.getBoolean(ARG_SHOW_TITLE, false)
            webViewMinusPadding = it.getBoolean(ARG_WEB_VIEW_MINUS_PADDING, false)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = BottomSheetFragmentLayoutBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        // Set initial state
        binding.dialogTitle.text = sheetTitle
        binding.closeButton.setOnClickListener {
            dismiss()
            onDismiss?.let { it() }
        }

        bottomSheetNavHeight = resources.getDimension(R.dimen.bottom_sheet_nav_height).roundToInt()

        if (!showTitle) {
            binding.titleCont.visibility = View.GONE
            binding.bottomSeparator.visibility = View.GONE
            bottomSheetNavHeight = 0
        }

        // Set up keyboard visibility listener
        unRegistrar = activity?.let {
            KeyboardVisibilityEvent.registerEventListener(it) { shown ->
                handleKeyboardVisibility(shown)
            }
        }

        // Configure bottom sheet behavior
        val behavior = BottomSheetBehavior.from(view.parent as View)
        behavior.isDraggable = draggable
        isCancelable = cancellable

        // Set background and layout parameters
        (view.parent as View).setBackgroundColor(requireContext().resources.getColor(R.color.bottom_sheet_bcg))
        val layoutParams = (view.parent as View).layoutParams as CoordinatorLayout.LayoutParams
        (view.parent as View).layoutParams = layoutParams

        // Set up dimensions
        val displayMetrics = DisplayMetrics()
        val windowManager = context?.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        windowManager.defaultDisplay.getMetrics(displayMetrics)
        val height = displayMetrics.heightPixels

        val sheetLayoutParams = (binding.sheetContainer as View).layoutParams as LinearLayout.LayoutParams
        sheetLayoutParams.height = (height * 0.89).roundToInt() - bottomSheetNavHeight
        (binding.sheetContainer as View).layoutParams = sheetLayoutParams
        behavior.peekHeight = (height * 0.92).roundToInt()

        // Load pending fragment if any
        pendingFragment?.let {
            loadFragment(it)
            pendingFragment = null
        }
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState) as BottomSheetDialog

        dialog.setOnShowListener {
            val bottomSheet = dialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet) as FrameLayout
            val background = MaterialShapeDrawable(
                ShapeAppearanceModel.Builder()
                    .setTopLeftCorner(CornerFamily.ROUNDED, 32f)
                    .setTopRightCorner(CornerFamily.ROUNDED, 32f)
                    .build()
            ).apply {
                fillColor = ColorStateList.valueOf(Color.WHITE)
            }

            ViewCompat.setBackground(bottomSheet, background)
        }

        return dialog
    }

    private fun handleKeyboardVisibility(shown: Boolean) {
        val r = Rect()
        activity?.window?.decorView?.getWindowVisibleDisplayFrame(r)

        val wvPadding = if (webViewMinusPadding) 
            resources.getDimension(R.dimen.bottom_sheet_wv_keyboard_padding).roundToInt() 
        else 0

        val sheetParams = (binding.sheetContainerInner as View).layoutParams as FrameLayout.LayoutParams
        val padding = if (shown) bottomSheetNavHeight - wvPadding else (bottomSheetNavHeight - r.top)
        sheetParams.height = ((r.height() - r.top) * 0.89).roundToInt() - padding
        (binding.sheetContainerInner as View).layoutParams = sheetParams
    }

    override fun onDestroy() {
        super.onDestroy()
        unRegistrar?.unregister()
    }

    fun show(fragment: Fragment, fragmentManager: FragmentManager, tag: String?) {
        loadFragment(fragment)
        super.show(fragmentManager, tag)
    }

    private fun loadFragment(fragment: Fragment) {
        if (isAdded) {
            val ft = childFragmentManager.beginTransaction()
            ft.replace(R.id.sheet_container_inner, fragment)
            ft.commit()
        } else {
            pendingFragment = fragment
        }
    }
}