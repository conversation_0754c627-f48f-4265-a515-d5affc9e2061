package com.eatapp.clementine.views

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.util.DisplayMetrics
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.LinearLayout
import androidx.constraintlayout.widget.ConstraintLayout
import com.eatapp.clementine.R
import com.eatapp.clementine.adapter.SelectorAdapter
import com.eatapp.clementine.databinding.BottomSheetLayoutBinding
import com.eatapp.clementine.internal.SelectorItem
import com.eatapp.clementine.internal.visible
import com.eatapp.clementine.internal.visibleOrGone
import kotlin.math.roundToInt

@SuppressLint("ViewConstructor")
class BottomSheetView(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
    items: MutableList<SelectorItem>?,
    actionTitle: String,
    allowDeselect: Boolean = false,
    subtitle: String? = null,
    singleSelection: Boolean,
    centerItemsHorizontally: Boolean = false
) : ConstraintLayout(context, attrs, defStyleAttr) {

    var dismiss: ((SelectorItem?) -> Unit)? = null

    private var adapter: SelectorAdapter =
        SelectorAdapter(singleSelection, centerItemsHorizontally, allowDeselect = allowDeselect)

    private val binding: BottomSheetLayoutBinding =
        BottomSheetLayoutBinding.inflate(LayoutInflater.from(context), this, true)

    init {
        adapter.dismiss = { item ->
            dismiss?.let { it(item) }
        }

        if (singleSelection) {
            binding.actionCont.visibility = View.GONE
        } else {
            binding.applyBtn.setOnClickListener {
                dismiss?.let { it(null) }
            }
        }

        subtitle?.let {
            binding.separator.visibleOrGone = false
            binding.containerSubtitle.visible = true
            binding.textSubtitle.text = it
        }

        binding.applyBtn.setTitle(actionTitle)

        binding.list.adapter = adapter

        adapter.submitList(items)

        val displayMetrics = DisplayMetrics()
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        windowManager.defaultDisplay.getMetrics(displayMetrics)
        val height = displayMetrics.heightPixels

        val maxHeight = (height * 0.80).roundToInt()
        val listSize = items?.size!! * context.resources.getDimension(R.dimen.selector_item_height).toInt()

        val layoutParams = binding.list.layoutParams as LinearLayout.LayoutParams

        if (listSize > maxHeight) {
            layoutParams.height = maxHeight
        } else {
            layoutParams.height = listSize
        }

        binding.list.layoutParams = layoutParams

        binding.list.smoothScrollToPosition(items.indexOfFirst { it.isSelected }.plus(5))
    }
}