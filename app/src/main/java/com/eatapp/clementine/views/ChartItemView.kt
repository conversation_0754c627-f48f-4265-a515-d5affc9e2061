package com.eatapp.clementine.views

import android.content.Context
import android.util.AttributeSet
import android.widget.LinearLayout
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.content.ContextCompat
import com.eatapp.clementine.R
import com.eatapp.clementine.internal.ChartItem
import kotlin.math.roundToInt


class ChartItemView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    var value : AppCompatTextView
    var percent : AppCompatTextView
    var title : AppCompatTextView

    init {

        inflate(context, R.layout.chart_item_view, this)

        value = findViewById(R.id.value)
        percent = findViewById(R.id.percent)
        title = findViewById(R.id.title)

    }

    fun setItem(item: ChartItem) {

        value.text = item.value.toString()
        percent.text = String.format("(%s%s)", item.percent.roundToInt().toString(), "%")
        title.text = resources.getString(item.title)
        title.setTextColor(ContextCompat.getColor(context, item.colorTitle))
    }
}