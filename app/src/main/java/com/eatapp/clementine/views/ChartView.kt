package com.eatapp.clementine.views

import android.content.Context
import android.graphics.PorterDuff
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.core.content.ContextCompat
import com.eatapp.clementine.R
import com.eatapp.clementine.internal.ChartItem
import kotlin.math.roundToInt

class ChartView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    enum class Position {
        FIRST,
        MIDDLE,
        LAST
    }

    private val chartItemsCont: LinearLayout
    private val desItemsCont: LinearLayout

    init {

        inflate(context, R.layout.chart_view, this)

        desItemsCont = findViewById(R.id.desItemsCont)
        chartItemsCont = findViewById(R.id.chartItemsCont)
    }

    fun setData(list: List<ChartItem>){

        desItemsCont.removeAllViews()
        chartItemsCont.removeAllViews()

        list.forEachIndexed { index, item ->

            addChartItem(item, getPosition(index, list))
            addDesItem(item)

        }
    }

    private fun addChartItem(item: ChartItem, position: Position) {

        if (item.value == 0) return

        val view = View(context)

        when (position) {
            Position.FIRST -> view.background =
                ContextCompat.getDrawable(context, R.drawable.shape_rounded_chart_left_bcg)
            Position.MIDDLE -> view.background =
                ContextCompat.getDrawable(context, R.drawable.shape_rounded_chart_middle_bcg)
            Position.LAST -> view.background =
                ContextCompat.getDrawable(context, R.drawable.shape_rounded_chart_right_bcg)
        }

        if (item.percent.roundToInt() == 100) view.background = ContextCompat
            .getDrawable(context, R.drawable.shape_rounded_chart_full_bcg)

        view.background.setColorFilter(ContextCompat.getColor(context, item.colorChart), PorterDuff.Mode.SRC_ATOP)

        val params = LayoutParams(0, ViewGroup.LayoutParams.MATCH_PARENT, item.percent)
        view.layoutParams = params

        chartItemsCont.addView(view)

        if (position != Position.LAST && item.percent.roundToInt() != 100) {
            val separator = View(context)
            separator.setBackgroundColor(ContextCompat.getColor(context, R.color.white))
            val p = LayoutParams(resources.getDimension(R.dimen.separator_chart_view_width).toInt(),
                ViewGroup.LayoutParams.MATCH_PARENT)
            separator.layoutParams = p

            chartItemsCont.addView(separator)
        }
    }

    private fun addDesItem(item: ChartItem) {

        val view = ChartItemView(context)
        view.setItem(item)

        val params = LayoutParams(0, ViewGroup.LayoutParams.WRAP_CONTENT, 1.0f)
        view.layoutParams = params

        desItemsCont.addView(view)
    }

    private fun getPosition(index: Int, list: List<ChartItem>): Position {

        return when (index) {
            0 -> Position.FIRST
            list.size-1 -> Position.LAST
            else -> {
                when {
                    list[0].percent.roundToInt() == 0 -> Position.FIRST
                    list[2].percent.roundToInt() == 0 -> Position.LAST
                    else -> Position.MIDDLE
                }
            }
        }

    }

}