package com.eatapp.clementine.views

import android.content.Context
import android.util.AttributeSet
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.eatapp.clementine.R
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.network.response.room.Table
import com.eatapp.clementine.data.network.response.server.Server
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

class ChitPrint @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    val mainCont: ConstraintLayout
    private val guestLbl: TextView
    private val coversLbl: TextView
    private val timeLbl: TextView
    private val tablesLbl: TextView
    private val tablesCont: LinearLayout
    private val serverLbl: TextView
    private val serverCont: LinearLayout
    private val guestNotesLbl: TextView
    private val guestNotesCont: LinearLayout
    private val guestTagsLbl: TextView
    private val guestTagsCont: LinearLayout
    private val reservationNotesLbl: TextView
    private val reservationNotesCont: LinearLayout
    private val reservationTagsLbl: TextView
    private val reservationTagsCont: LinearLayout
    private val timeCreatedLbl: TextView
    private val notesAndTagsCont: LinearLayout

    init {

        inflate(context, R.layout.chit_print_view, this)

        mainCont = findViewById(R.id.main_cont)
        guestLbl = findViewById(R.id.guest)
        coversLbl = findViewById(R.id.covers)
        timeLbl = findViewById(R.id.time)
        tablesLbl = findViewById(R.id.tables)
        tablesCont = findViewById(R.id.tables_cont)
        serverLbl = findViewById(R.id.server)
        serverCont = findViewById(R.id.server_cont)
        guestNotesLbl = findViewById(R.id.guest_notes)
        guestNotesCont = findViewById(R.id.guest_notes_cont)
        guestTagsLbl = findViewById(R.id.guest_tags)
        guestTagsCont = findViewById(R.id.guest_tags_cont)
        reservationNotesLbl = findViewById(R.id.reservation_notes)
        reservationNotesCont = findViewById(R.id.reservation_notes_cont)
        reservationTagsLbl = findViewById(R.id.reservation_tags)
        reservationTagsCont = findViewById(R.id.reservation_tags_cont)
        timeCreatedLbl = findViewById(R.id.time_created)
        notesAndTagsCont = findViewById(R.id.notes_tags_cont)
    }

    fun set(reservation: Reservation?, servers: List<Server>?) {

        /*
         Guest name
        */
        guestLbl.text = reservation?.guestName

        /*
         Covers
         */
        coversLbl.text = String.format("%s %s", reservation?.covers.toString(), if (reservation?.covers == 1) { "cover" } else { "covers" })

        /*
         Time
         */
        timeLbl.text = SimpleDateFormat("hh:mm a", Locale.US).format(reservation?.startTime ?: Date())

        /*
         Table
         */
        reservation?.tables?.also {
            val tablesList = getTables(it)
            if (tablesList != "") {
                tablesLbl.text = tablesList
            } else {
                tablesCont.visibility = GONE
            }
        } ?: run {
            tablesCont.visibility = GONE
        }

        /*
         Server
         */
        reservation?.tables?.also {
            val serversList = getServers(it, servers)
            if (serversList != "") {
                serverLbl.text = serversList
            } else {
                serverCont.visibility = GONE
            }
        } ?: run {
            serverCont.visibility = GONE
        }

        var thirdSectionExists = false

        /*
         Guest notes
         */
        if (reservation?.guest?.notes?.isBlank() == false) {
            thirdSectionExists = true
            guestNotesLbl.text = reservation.guest?.notes
        } else {
            guestNotesCont.visibility = GONE
        }

        /*
         Guest tags
         */
        if (reservation?.guest?.taggings?.isEmpty() == false) {

            val tagsArray = mutableListOf<String>()

            reservation.guest?.taggings?.forEach { tagging ->
                tagsArray.add(tagging.name)
            }

            val tags = tagsArray.joinToString( ", " )

            thirdSectionExists = true

            guestTagsLbl.text = tags

        } else {
            guestTagsCont.visibility = GONE
        }

        /*
         Reservation notes
         */
        if (reservation?.notes?.isBlank() == false) {
            thirdSectionExists = true
            reservationNotesLbl.text = reservation.notes
        } else {
            reservationNotesCont.visibility = GONE
        }

        /*
         Reservation tags
         */
        if (reservation?.taggings?.isEmpty() == false) {

            val tagsArray = mutableListOf<String>()

            reservation.taggings?.forEach { tagging ->
                tagsArray.add(tagging.name)
            }

            val tags = tagsArray.joinToString( ", " )

            thirdSectionExists = true

            reservationTagsLbl.text = tags

        } else {
            reservationTagsCont.visibility = GONE
        }

        if (!thirdSectionExists) {
            notesAndTagsCont.visibility = GONE
        }

        /*
         Time created
         */
        timeCreatedLbl.text = SimpleDateFormat("dd.MM.yyyy", Locale.US).format(Date())

    }

    private fun getServers(tables: List<Table>?, servers: List<Server>?): String {
        val serversList = mutableListOf<String>()
        tables?.let {
            for (reservationTable in it) {

                reservationTable.restaurantServerId?.let { id ->
                    serversList.add(servers?.find { server -> server.id == id }?.name ?: "")
                }
            }
        }
        return serversList.joinToString( ", " )
    }

    private fun getTables(tables: List<Table>): String {
        val tablesList = mutableListOf<String>()

        for (reservationTable in tables) {
            tablesList.add(reservationTable.number)
        }
        return tablesList.joinToString( ", " )
    }
}