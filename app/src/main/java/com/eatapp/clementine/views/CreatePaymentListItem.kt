package com.eatapp.clementine.views

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.MotionEvent
import android.widget.LinearLayout
import androidx.core.content.ContextCompat
import androidx.core.widget.doOnTextChanged
import com.eatapp.clementine.R
import com.eatapp.clementine.databinding.CreatePaymentListItemBinding
import com.eatapp.clementine.internal.visibleOrGone

class CreatePaymentListItem @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    var childrenClickable = true

    private val binding: CreatePaymentListItemBinding =
        CreatePaymentListItemBinding.inflate(LayoutInflater.from(context), this, true)

    override fun onInterceptTouchEvent(ev: MotionEvent?): Boolean {
        return !childrenClickable
    }

    fun setInputType(inputType: Int) {
        binding.editItem.inputType = inputType
    }

    fun onTextChanged(action: (String) -> Unit) {
        binding.editItem.doOnTextChanged { text, _, _, _ ->
            action(text.toString())
        }
    }

    fun editable(editable: Boolean) {
        binding.editItem.isEnabled = editable
    }

    fun showChevron(show: Boolean) {
        binding.editItem.setCompoundDrawablesRelativeWithIntrinsicBounds(
            null,
            null,
            if (show) ContextCompat.getDrawable(context, R.drawable.ic_icon_arrow_down) else null,
            null
        )
    }

    fun setHint(hint: String) {
        binding.hint = hint
    }

    fun setTitle(title: String) {
        binding.title = title
    }

    fun setText(string: String) {
        binding.editItem.setText(string)
    }

    fun getText(): String {
        return binding.editItem.text.toString()
    }

    fun setCurrency(currency: String) {
        binding.textCurrency.text = currency
        binding.textCurrency.visibleOrGone = currency.isNotEmpty()
    }
}