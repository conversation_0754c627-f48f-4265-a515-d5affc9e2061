package com.eatapp.clementine.views

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.setPadding
import androidx.recyclerview.widget.RecyclerView
import com.eatapp.clementine.databinding.DoubleStepperViewBinding
import com.eatapp.clementine.internal.px
import com.eatapp.clementine.internal.visibleOrGone


class DoubleStepperView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    val binding: DoubleStepperViewBinding =
        DoubleStepperViewBinding.inflate(LayoutInflater.from(context), this, true)

    fun setConflictsAdapter(adapter: RecyclerView.Adapter<*>) {
        binding.recyclerConflicts.adapter = adapter
    }

    fun showConflictsList(show: <PERSON><PERSON><PERSON>) {
        if (show) {
            binding.recyclerConflicts.setPadding(0, 0, 0, 8.px)
        } else {
            binding.recyclerConflicts.setPadding(0)
        }
        binding.recyclerConflicts.visibleOrGone = show
    }
}