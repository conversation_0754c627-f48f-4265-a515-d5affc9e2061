package com.eatapp.clementine.views

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import androidx.core.content.ContextCompat
import com.eatapp.clementine.R
import com.eatapp.clementine.data.network.response.message.Message
import com.eatapp.clementine.databinding.ErrorDetailsViewBinding

class ErrorDetailsView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private val binding: ErrorDetailsViewBinding = ErrorDetailsViewBinding.inflate(
        LayoutInflater.from(context), this, true
    )

    var onMetaClickListener: ((String) -> Unit)? = null
    var onCloseClickListener: (() -> Unit)? = null

    init {
        binding.closeButton.setOnClickListener {
            onCloseClickListener?.invoke()
        }
    }

    fun setMessage(m: Message) {
        binding.apply {
            message = m
        }

        binding.metaBtn.title.setTextColor(ContextCompat.getColor(context!!, R.color.green500))

        binding.metaBtn.setOnClickListener {
            m.errorDetails?.link?.let { it1 -> onMetaClickListener?.invoke(it1) }
        }
    }
} 