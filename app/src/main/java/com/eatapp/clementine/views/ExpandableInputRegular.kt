package com.eatapp.clementine.views

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.text.InputType
import android.text.TextWatcher
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.inputmethod.EditorInfo
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.widget.addTextChangedListener
import com.eatapp.clementine.databinding.InputRegularBinding

class ExpandableInputRegular @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    val binding: InputRegularBinding =
        InputRegularBinding.inflate(LayoutInflater.from(context), this, true)

    fun updateView(
        title: String,
        placeholder: String,
        editValue: String?,
        disabled: <PERSON><PERSON><PERSON>,
        listener: TextWatcher?) {

        binding.run {
            etValue.setOnFocusChangeListener { _, _ ->
                lineCount = etValue.lineCount
                Handler(Looper.getMainLooper()).postDelayed({
                    etValue.setSelection(etValue.length())
                }, 100)
                showMoreExpanded = lineCount > 5
            }

            etValue.addTextChangedListener {
                if (etValue.lineCount > 0) {
                    lineCount = etValue.lineCount
                }
                showMoreVisible = lineCount > 5
                showMoreExpanded = lineCount > 5
                listener?.onTextChanged(etValue.text, 0, 0, 0)
            }

            expandBtn.setOnClickListener {
                showMoreExpanded = showMoreExpanded?.not()
            }

            enabled = !disabled
            ctaVisible = false
            lineCount = 5
            inputType = InputType.TYPE_CLASS_TEXT or InputType.TYPE_TEXT_FLAG_MULTI_LINE
            hint = placeholder
            value = editValue
            key = title
            showMoreExpanded = false

            etValue.imeOptions = EditorInfo.IME_ACTION_DONE

            Handler(Looper.getMainLooper()).postDelayed({
                lineCount = etValue.lineCount
                showMoreVisible = etValue.lineCount > 5
            }, 100)

            executePendingBindings()
        }
    }
}