package com.eatapp.clementine.views

import android.content.Context
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.ProgressBar
import androidx.appcompat.widget.AppCompatTextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import com.eatapp.clementine.R
import com.eatapp.clementine.internal.tintColor
import androidx.core.content.withStyledAttributes
import androidx.databinding.library.baseAdapters.BR


class LoadingButton@JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    enum class LoadingButtonState {
        Available,
        AvailableLight,
        PartiallyDisabled,
        Disabled
    }

    var title : AppCompatTextView
    private var progress : ProgressBar
    var iconLeft : ImageView
    var iconRight : ImageView
    private var mainContainer : LinearLayout

    var state: LoadingButtonState = LoadingButtonState.Available
        set(v) {
            field = v

            val drawable = background.mutate()

            when (state) {
                LoadingButtonState.Available -> {
                    drawable.setTint(ContextCompat.getColor(context, R.color.green500))
                    background = drawable
                    title.setTextColor(ContextCompat.getColor(context!!, R.color.white))
                    iconLeft.tintColor(R.color.white)
                    iconRight.tintColor(R.color.white)
                    setOnTouchListener(null)
                }

                LoadingButtonState.AvailableLight -> {
                    drawable.setTint(ContextCompat.getColor(context, R.color.green100))
                    background = drawable
                    title.setTextColor(ContextCompat.getColor(context!!, R.color.grey800))
                    iconLeft.tintColor(R.color.grey800)
                    iconRight.tintColor(R.color.grey800)
                    setOnTouchListener(null)
                }

                LoadingButtonState.PartiallyDisabled -> {
                    drawable.setTint(ContextCompat.getColor(context, R.color.grey600))
                    background = drawable
                    title.setTextColor(ContextCompat.getColor(context!!, R.color.white))
                    iconLeft.tintColor(R.color.white)
                    iconRight.tintColor(R.color.white)
                    setOnTouchListener(null)
                }

                LoadingButtonState.Disabled -> {
                    drawable.setTint(ContextCompat.getColor(context, R.color.grey200))
                    background = drawable
                    title.setTextColor(ContextCompat.getColor(context!!, R.color.grey500))
                    iconLeft.tintColor(R.color.grey500)
                    iconRight.tintColor(R.color.grey500)
                    setOnTouchListener { _, _ -> true } // Block all touch events
                }
            }
        }

    init {

        inflate(context, R.layout.loading_button, this)

        title = findViewById(R.id.title)
        progress = findViewById(R.id.progress)
        iconLeft = findViewById(R.id.left_icon)
        iconRight = findViewById(R.id.right_icon)
        mainContainer = findViewById(R.id.main_container)

        context.withStyledAttributes(attrs, R.styleable.LoadingButton) {
            title.text = getString(R.styleable.LoadingButton_title)

            progress.indeterminateDrawable.setColorFilter(
                getColor
                    (R.styleable.LoadingButton_progressBarColor, 0),
                android.graphics.PorterDuff.Mode.MULTIPLY
            )

            val icL = getDrawable(R.styleable.LoadingButton_leftIcon)
            iconLeft.setImageDrawable(icL)

            val icR = getDrawable(R.styleable.LoadingButton_rightIcon)
            iconRight.setImageDrawable(icR)

            iconLeft.visibility = when (icL) {
                null -> GONE
                else -> VISIBLE
            }

            iconRight.visibility = when (icR) {
                null -> GONE
                else -> VISIBLE
            }
        }

        if (background == null) {
            background = ContextCompat.getDrawable(context, R.drawable.shape_rounded_btn_bcg_green)
        }
    }

    fun showLoading(show: Boolean) {
        title.animate().alpha(if (show) 0f else 1f)
        progress.animate().alpha(if (show) 1f else 0f)
    }

    fun showLoading() {
        title.animate().alpha(0f)
        iconLeft.animate().alpha(0f)
        iconRight.animate().alpha(0f)
        progress.animate().alpha(1f)
    }

    fun hideLoading() {
        title.animate().alpha(1f)
        iconLeft.animate().alpha(1f)
        iconRight.animate().alpha(1f)
        progress.animate().alpha(0f)
    }

    fun setTitle(title: String) {
        this.title.text = title
    }

    fun setLeftIcon(ic: Drawable, tint: Int) {
        iconLeft.setImageDrawable(ic)
        iconLeft.visibility = VISIBLE
        iconLeft.tintColor(tint)
    }

    fun setRightIcon(ic: Drawable, tint: Int) {
        iconRight.setImageDrawable(ic)
        iconRight.visibility = VISIBLE
        iconRight.tintColor(tint)
    }

    override fun setVisibility(visibility: Int) {
        super.setVisibility(visibility)
        mainContainer.requestLayout()
        mainContainer.invalidate()
    }
}