package com.eatapp.clementine.views

import android.content.Context
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.View
import android.widget.Button
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.appcompat.widget.AppCompatTextView
import androidx.cardview.widget.CardView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import com.eatapp.clementine.R
import com.eatapp.clementine.data.network.response.product.Cta
import com.eatapp.clementine.data.network.response.product.Message
import com.eatapp.clementine.data.network.response.product.ProductMessageLevel
import com.eatapp.clementine.data.network.response.product.ProductMessageType
import com.eatapp.clementine.data.network.response.restaurant.AccountStateType
import com.eatapp.clementine.ui.base.BaseFragment
import java.util.Calendar
import java.util.Date


class LockdownView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    var productMessage: Message? = null
    var token: String = ""
    var restaurantId: String = ""
    var listener: ((cta: Cta?, token: String, restaurantId: String) -> Unit)? = null

    private var extraOverlayMargin: Boolean = false
    private var allowInteraction: Boolean = false

    private var contentView : ConstraintLayout
    private var popup : CardView
    private var popupTitle : AppCompatTextView
    private var popupText : AppCompatTextView
    private var popupCont : LinearLayout
    private var popupAction : Button
    private var popupGraphic : ImageView
    private var popupClose : Button

    private var banner : CardView
    private var bannerContent : FrameLayout
    private var bannerTitle : AppCompatTextView
    private var bannerText : AppCompatTextView
    private var bannerActionPrimary : Button
    private var bannerActionSecondary : Button
    private var overlay : View
    private var bannerClose : Button

    private var bannerMinimized : CardView
    private var bannerMinimizedAction : ImageView

    companion object {
        var messageDisplayedAt: Date? = null
    }

    init {

        inflate(context, R.layout.lockdown_layout, this)

        contentView = findViewById(R.id.lockdownView)
        popup = findViewById(R.id.alert_main)
        popupTitle = findViewById(R.id.main_alert_title)
        popupText = findViewById(R.id.main_alert_text)
        popupCont = findViewById(R.id.main_alert_text_cont)
        popupAction = findViewById(R.id.main_alert_action)
        popupGraphic = findViewById(R.id.main_alert_graphic)

        banner = findViewById(R.id.alert_small)
        bannerContent = findViewById(R.id.alert_small_content)
        bannerTitle = findViewById(R.id.small_alert_title)
        bannerText = findViewById(R.id.small_alert_text)
        bannerActionPrimary = findViewById(R.id.small_alert_action_primary)
        bannerActionSecondary = findViewById(R.id.small_alert_action_secondary)

        bannerMinimized = findViewById(R.id.banner_minimized)
        bannerMinimizedAction = findViewById(R.id.banner_minimized_action)

        overlay = findViewById(R.id.overlay)

        popupClose = findViewById(R.id.main_alert_close)
        popupClose.setOnClickListener {
            showBanner()
        }

        bannerClose = findViewById(R.id.small_alert_close)
        bannerClose.setOnClickListener {
            hideBanner()
        }

        popupAction.setOnClickListener {
            action(productMessage?.popUp?.primaryCta)
        }

        bannerActionPrimary.setOnClickListener {
            action(productMessage?.banner?.primaryCta)
        }

        bannerActionSecondary.setOnClickListener {
            action(productMessage?.banner?.secondaryCta)
        }

        bannerMinimizedAction.setOnClickListener {
            hideBannerMinimised()
        }
    }

    fun updateWithSubscription(productMessage: Message,
                               accountState: AccountStateType,
                               token: String,
                               restaurantId: String,
                               screenType: BaseFragment.ScreenType) {

        this.productMessage = productMessage
        this.token = token
        this.restaurantId = restaurantId

        extraOverlayMargin = screenType == BaseFragment.ScreenType.Reservations
                || screenType == BaseFragment.ScreenType.Waitlist
                || screenType == BaseFragment.ScreenType.GuestSpend
                || screenType == BaseFragment.ScreenType.DailyNotes
                || screenType == BaseFragment.ScreenType.Reports

        allowInteraction = accountState == AccountStateType.ACTIVE ||
                (accountState == AccountStateType.IN_HOUSE
                        && productMessage.name != ProductMessageType.BillingBlocked
                        && (screenType == BaseFragment.ScreenType.Reservations || screenType == BaseFragment.ScreenType.Waitlist))

        popupTitle.text = productMessage.popUp?.title
        popupText.text = productMessage.popUp?.content
        popupAction.text = productMessage.popUp?.primaryCta?.text

        bannerTitle.text = productMessage.banner?.title
        bannerText.text = productMessage.banner?.content
        bannerActionPrimary.text = productMessage.banner?.primaryCta?.text
        bannerActionSecondary.text = productMessage.banner?.secondaryCta?.text

        if (productMessage.banner?.secondaryCta == null) {
             bannerActionSecondary.visibility = GONE
        }

        bannerClose.visibility = when (productMessage.banner?.state) {
            "persistent" -> GONE
            else -> VISIBLE
        }

        var res: Drawable? = ContextCompat.getDrawable(context, R.drawable.exclamation_triangle)

        try {
            res = context?.let { ContextCompat.getDrawable(it, resources.getIdentifier(productMessage.banner?.icon, "drawable", context?.packageName)) }
        } catch (e: Exception) {}

        bannerMinimizedAction.setImageDrawable(res)

        when (productMessage.level) {
            ProductMessageLevel.Alert -> {
                bannerContent.setBackgroundColor(ContextCompat.getColor(context!!, R.color.colorRed50))
                bannerMinimizedAction.setBackgroundColor(ContextCompat.getColor(context!!, R.color.colorRed50))
            }
            ProductMessageLevel.Warning -> {
                bannerContent.setBackgroundColor(ContextCompat.getColor(context!!, R.color.colorYellow50))
                bannerMinimizedAction.setBackgroundColor(ContextCompat.getColor(context!!, R.color.colorYellow50))
            }
        }

        when (productMessage.name) {
            ProductMessageType.BillingBlocked -> {
                popupGraphic.setImageDrawable(
                    ContextCompat.getDrawable(context, R.drawable.empty_state_billing_blocked))
                if (productMessage.popUp?.title.isNullOrBlank()) {
                    popupTitle.text = resources.getString(R.string.billing_blocked_title)
                }
                if (productMessage.popUp?.content.isNullOrBlank()) {
                    popupText.text = resources.getString(R.string.billing_blocked_text)
                }
            }
            ProductMessageType.BillingWarning -> {
                popupGraphic.setImageDrawable(
                    ContextCompat.getDrawable(context, R.drawable.empty_state_billing_warning))
                if (productMessage.banner?.title.isNullOrBlank()) {
                    popupTitle.text = resources.getString(R.string.billing_warning_title)
                }
                if (productMessage.popUp?.content.isNullOrBlank()) {
                    popupText.text = resources.getString(R.string.billing_warning_text)
                }
            }
            ProductMessageType.SubscriptionExpiring -> {
                popupGraphic.setImageDrawable(
                    ContextCompat.getDrawable(context, R.drawable.empty_state_subscription_expiring))
                if (productMessage.popUp?.title.isNullOrBlank()) {
                    popupTitle.text = resources.getString(R.string.subscription_expiring_title)
                }
                if (productMessage.popUp?.content.isNullOrBlank()) {
                    popupText.text = resources.getString(R.string.subscription_expiring_text)
                }
            }
            ProductMessageType.SubscriptionExpired -> {
                popupGraphic.setImageDrawable(
                    ContextCompat.getDrawable(context, R.drawable.empty_state_subscription_expired))
                if (productMessage.popUp?.title.isNullOrBlank()) {
                    popupTitle.text = resources.getString(R.string.subscription_expired_title)
                }
                if (productMessage.popUp?.content.isNullOrBlank()) {
                    popupText.text = resources.getString(R.string.subscription_expired_text)
                }
            }
            ProductMessageType.TrialExpired -> {
                popupGraphic.setImageDrawable(
                    ContextCompat.getDrawable(context, R.drawable.empty_state_trial_expired))
                View.inflate(context, R.layout.trial_expired_layout, popupCont)
                if (productMessage.popUp?.title.isNullOrBlank()) {
                    popupTitle.text = resources.getString(R.string.trial_expired_title)
                }
                if (productMessage.popUp?.content.isNullOrBlank()) {
                    popupText.text = resources.getString(R.string.trial_expired_text)
                }
            }
            ProductMessageType.TrialActive -> {
                popupGraphic.setImageDrawable(
                    ContextCompat.getDrawable(context, R.drawable.empty_state_subscription_expiring))
                if (productMessage.popUp?.title.isNullOrBlank()) {
                    popupTitle.text = resources.getString(R.string.trial_active_title)
                }
                if (productMessage.popUp?.content.isNullOrBlank()) {
                    popupText.text = resources.getString(R.string.trial_active_text)
                }
            }
            ProductMessageType.CoversApproaching -> {
                popupGraphic.setImageDrawable(
                    ContextCompat.getDrawable(context, R.drawable.empty_state_covers_approaching))
                if (productMessage.quota == 50) {
                    View.inflate(context, R.layout.covers_50_layout, popupCont)
                    if (productMessage.popUp?.title.isNullOrBlank()) {
                        popupTitle.text = resources.getString(R.string.covers_50_expiring_title)
                    }
                    if (productMessage.popUp?.content.isNullOrBlank()) {
                        popupText.text = resources.getString(R.string.covers_50_expiring_text)
                    }
                } else {
                    View.inflate(context, R.layout.covers_500_layout, popupCont)
                    if (productMessage.popUp?.title.isNullOrBlank()) {
                        popupTitle.text = resources.getString(R.string.covers_500_expiring_title)
                    }
                    if (productMessage.popUp?.content.isNullOrBlank()) {
                        popupText.text = resources.getString(R.string.covers_500_expiring_text)
                    }
                }
            }
            ProductMessageType.CoversExceeded -> {
                popupGraphic.setImageDrawable(
                    ContextCompat.getDrawable(context, R.drawable.empty_state_covers_exceeded))
                if (productMessage.quota == 50) {
                    View.inflate(context, R.layout.covers_50_layout, popupCont)
                    if (productMessage.popUp?.title.isNullOrBlank()) {
                        popupTitle.text = resources.getString(R.string.covers_50_expired_title)
                    }
                    if (productMessage.popUp?.content.isNullOrBlank()) {
                        popupText.text = resources.getString(R.string.covers_50_expired_text)
                    }
                } else {
                    View.inflate(context, R.layout.covers_500_layout, popupCont)
                    if (productMessage.popUp?.title.isNullOrBlank()) {
                        popupTitle.text = resources.getString(R.string.covers_500_expired_title)
                    }
                    if (productMessage.popUp?.content.isNullOrBlank()) {
                        popupText.text = resources.getString(R.string.covers_500_expired_text)
                    }
                }
            }
            else -> {}
        }

        var timeElapsed = true

        if (messageDisplayedAt != null) {
            val calendar = Calendar.getInstance()
            calendar.time = messageDisplayedAt!!
            if (productMessage.name == ProductMessageType.SmsApproaching || productMessage.name == ProductMessageType.SmsExceeded) {
                calendar.add(Calendar.HOUR, 24) //One day
            } else {
                calendar.add(Calendar.MINUTE, 30) //30 minutes
            }
            timeElapsed = Date() > calendar.time
        }

        if (productMessage.popUp != null && timeElapsed) {
            showPopUp()
        } else if ((timeElapsed || productMessage.banner?.state == "persistent")
            && productMessage.name != ProductMessageType.SmsApproaching && productMessage.name != ProductMessageType.SmsExceeded)  {
            showBanner()
        } else {
            hideBanner()
        }
    }

    fun removeTopMargin() {
        val params = overlay.layoutParams as LayoutParams
        params.topMargin = 0
        overlay.layoutParams = params
    }

    private fun action(cta: Cta?) {
        listener?.let { it(cta, token, restaurantId) }
    }

    private fun showPopUp() {
        messageDisplayedAt = Date()
        popup.visibility = View.VISIBLE
        popup.animate().alpha(1f)
    }

    private fun showBanner() {
        messageDisplayedAt = Date()

        popup.animate().alpha(0f).withEndAction {
            popup.visibility = View.GONE
        }
        banner.animate().alpha(1f)
        overlay.animate().alpha(0f).withEndAction {
            if (allowInteraction) {
                contentView.removeView(overlay)
            }
        }
        if (extraOverlayMargin) {
            val params = overlay.layoutParams as LayoutParams
            params.topMargin += context.resources.getDimension(R.dimen.tab_height).toInt()
            overlay.layoutParams = params
        }
    }

    private fun hideBanner() {
        banner.animate().alpha(0f).withEndAction {
            banner.visibility = View.GONE
            popup.visibility = View.GONE
        }
        bannerMinimized.animate().alpha(1f)
        bannerMinimized.visibility = VISIBLE
        if (allowInteraction) {
            contentView.removeView(overlay)
        }
        if (extraOverlayMargin) {
            val params = overlay.layoutParams as LayoutParams
            params.topMargin += context.resources.getDimension(R.dimen.tab_height).toInt()
            overlay.layoutParams = params
        }
    }

    private fun hideBannerMinimised() {
        banner.visibility = VISIBLE
        banner.animate().alpha(1f)

        bannerMinimized.animate().alpha(0f).withEndAction {
            bannerMinimized.visibility = View.GONE
        }
    }
}