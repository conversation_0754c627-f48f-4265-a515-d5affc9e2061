package com.eatapp.clementine.views

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import com.eatapp.clementine.R
import com.eatapp.clementine.databinding.LoyaltyModificationViewBinding

interface LoyaltyModificationListener {
    fun onPlusValueClicked(value: Int)
    fun onMinusValueClicked(value: Int)
}

enum class Operator(val value: Int) {
    PLUS(0),
    MINUS(1);

    companion object {
        fun fromInt(value: Int): Operator {
            return entries.find { it.value == value } ?: PLUS
        }
    }
}

class LoyaltyModificationView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private var operator = Operator.PLUS

    val binding: LoyaltyModificationViewBinding =
        LoyaltyModificationViewBinding.inflate(LayoutInflater.from(context), this, true)

    var listener: LoyaltyModificationListener? = null

    init {
        val attributes = context.obtainStyledAttributes(attrs, R.styleable.LoyaltyModificationView)
        binding.tvValue.text = attributes.getString(R.styleable.LoyaltyModificationView_value)
        val statusValue = attributes.getInt(R.styleable.LoyaltyModificationView_operator, 0)
        operator = Operator.fromInt(statusValue)
        attributes.recycle()

        if (operator == Operator.PLUS) {
            binding.tvOperator.text = "+"
        } else {
            binding.tvOperator.text = "-"
        }

        binding.root.setOnClickListener {
            if (operator == Operator.PLUS) {
                listener?.onPlusValueClicked(binding.tvValue.text.toString().toInt())
            } else {
                listener?.onMinusValueClicked(binding.tvValue.text.toString().toInt())
            }
        }
    }
}