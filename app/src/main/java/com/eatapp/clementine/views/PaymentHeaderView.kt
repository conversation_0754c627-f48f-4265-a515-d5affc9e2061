package com.eatapp.clementine.views

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import com.eatapp.clementine.data.network.response.payment.Payment
import com.eatapp.clementine.databinding.PaymentHeaderLayoutBinding
import com.eatapp.clementine.internal.visibleOrGone

class PaymentHeaderView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    private val binding: PaymentHeaderLayoutBinding =
        PaymentHeaderLayoutBinding.inflate(LayoutInflater.from(context), this, true)

    @SuppressLint("DefaultLocale")
    fun updateHeader(payment: Payment, amount: Double?, showInternalNotes: Boolean = false) {
        binding.run {
            amountValue.text = String.format("%.2f %s", amount, payment.attributes.currency)
            internalNotesValue.text = payment.attributes.notes ?: "--"
            descriptionValue.text = payment.attributes.description ?: "--"

            containerInternalNotes.visibleOrGone = showInternalNotes
        }
    }
}