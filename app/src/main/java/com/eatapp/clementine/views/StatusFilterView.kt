package com.eatapp.clementine.views

import android.content.Context
import android.util.AttributeSet
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.appcompat.widget.AppCompatTextView
import com.eatapp.clementine.R

class StatusFilterView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    var title : AppCompatTextView
    var iconLeft : ImageView
    var iconRight : ImageView
    var bcg : LinearLayout

    init {
        inflate(context, R.layout.status_filter_view, this)

        title = findViewById(R.id.title)
        iconLeft = findViewById(R.id.icon_left)
        iconRight = findViewById(R.id.icon_right)
        bcg = findViewById(R.id.bcg)

        val attributes = context.obtainStyledAttributes(attrs, R.styleable.StatusFilterView)
        title.text = attributes.getString(R.styleable.StatusFilterView_text)
        attributes.recycle()
    }
}