package com.eatapp.clementine.views

import android.content.Context
import android.graphics.PorterDuff
import android.os.Build
import android.os.Handler
import android.util.AttributeSet
import android.util.Log
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.core.view.setPadding
import androidx.databinding.BindingAdapter
import androidx.recyclerview.widget.RecyclerView
import com.eatapp.clementine.R
import com.eatapp.clementine.databinding.StepperViewBinding
import com.eatapp.clementine.internal.px
import com.eatapp.clementine.internal.visibleOrGone


class StepperView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    val binding: StepperViewBinding =
        StepperViewBinding.inflate(LayoutInflater.from(context), this, true)

    var listener: ((s: Step) -> Unit)? = null

    enum class Step {
        PLUS,
        MINUS
    }

    init {

        binding.plus.setOnClickListener {}

        Handler().postDelayed({
            binding.minus.setOnTouchListener(StepperOnTouchListener(listener, Step.MINUS))
            binding.plus.setOnTouchListener(StepperOnTouchListener(listener, Step.PLUS))
        }, 100)

        val attributes = context.obtainStyledAttributes(attrs, R.styleable.StepperView)
        binding.title.text = attributes.getString(R.styleable.StepperView_key)
        attributes.recycle()
    }

    fun setConflictsAdapter(adapter: RecyclerView.Adapter<*>) {
        binding.recyclerConflicts.adapter = adapter
    }

    fun setTitle(title: String) {
        this.binding.title.text = title
    }

    fun setValue(value: String?) {
        this.binding.value.text = value
    }

    fun separatorVisibility(visible: Boolean) {
        this.binding.separator.isVisible = visible
    }

    fun titleAllCaps(caps: Boolean) {
        this.binding.title.isAllCaps = caps
    }

    fun showConflictsList(show: Boolean) {
        if (show) {
            binding.recyclerConflicts.setPadding(0, 0, 0, 8.px)
        } else {
            binding.recyclerConflicts.setPadding(0)
        }
        binding.recyclerConflicts.visibleOrGone = show
    }

    companion object {

        @JvmStatic
        @BindingAdapter("hint")
        fun StepperView.setHint(v: String?) {
            binding.value.hint = v
        }

        @JvmStatic
        @BindingAdapter("disabled")
        fun StepperView.setDisabled(v: Boolean) {
            when (v) {
                true -> binding.value.setTextColor(ContextCompat.getColor(context, R.color.grey500))
                else -> binding.value.setTextColor(ContextCompat.getColor(context, R.color.grey800))
            }
            if (v) {
                binding.disable.isClickable = true
                binding.minus.setColorFilter(
                    ContextCompat.getColor(context, R.color.colorGrey100),
                    PorterDuff.Mode.SRC_ATOP
                )
                binding.plus.setColorFilter(
                    ContextCompat.getColor(context, R.color.colorGrey100),
                    PorterDuff.Mode.SRC_ATOP
                )
            }
        }
    }

    class StepperOnTouchListener(
        val listener: ((s: Step) -> Unit)?,
        private val step: Step
    ) : OnTouchListener {

        private var longHandler = Handler()
        private var longRunnable: Runnable? = null

        private fun runnable(view: View?): Runnable {

            longRunnable = Runnable {
                listener?.let { it(step) }
                Log.i("performStep", "action")
                longHandler.postDelayed(runnable(view), 100)
            }

            return longRunnable as Runnable
        }

        override fun onTouch(view: View?, event: MotionEvent?): Boolean {

            when (event?.action) {
                MotionEvent.ACTION_DOWN -> {
                    Log.i("performStep", "down")
                    listener?.let { it(step) }
                    longHandler.postDelayed(runnable(view), 600)
                }

                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    Log.i("performStep", "up")
                    view?.performClick()
                    longRunnable?.let { longHandler.removeCallbacks(it) }
                }
            }

            return false
        }
    }
}