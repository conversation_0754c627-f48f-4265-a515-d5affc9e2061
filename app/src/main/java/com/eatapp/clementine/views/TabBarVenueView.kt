package com.eatapp.clementine.views

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.widget.doOnTextChanged
import androidx.databinding.DataBindingUtil
import com.eatapp.clementine.R
import com.eatapp.clementine.data.network.response.restaurant.Restaurant
import com.eatapp.clementine.databinding.TabBarVenueLayoutBinding
import com.eatapp.clementine.internal.px
import com.eatapp.clementine.internal.slideView
import com.eatapp.clementine.internal.visibleOrGone
import com.eatapp.clementine.ui.home.HomeActivity
import com.eatapp.clementine.ui.home.tab_bar.NavigationRestaurantSelectorAdapter
import kotlin.math.min

typealias RestaurantId = String
typealias RestaurantClickListener = (RestaurantId) -> Unit

class TabBarVenueView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    var restaurantClickListener: RestaurantClickListener? = null

    val binding: TabBarVenueLayoutBinding = DataBindingUtil.inflate(
        LayoutInflater.from(context),
        R.layout.tab_bar_venue_layout, this, true
    )

    private var adapter: NavigationRestaurantSelectorAdapter = NavigationRestaurantSelectorAdapter {
        restaurantClickListener?.invoke(it)
    }

    private var restaurants: List<Restaurant>? = null

    init {
        binding.rvRestaurants.adapter = adapter
        binding.etSearchRestaurant.doOnTextChanged { text, _, _, _ ->
            val list = restaurants?.filter {
                it.name.contains(text.toString(), ignoreCase = true)
            }
            adapter.submitList(list)
        }
    }

    fun submitList(restaurants: List<Restaurant>) {
        this.restaurants = restaurants
        adapter.submitList(restaurants)
    }

    fun currentList(): List<Restaurant> {
        return adapter.currentList
    }

    fun updateRestaurantName(restaurant: Restaurant?, email:String?) {
        restaurant?.name?.let { name ->
            val words = name.split(" ")
            binding.tvRestaurantShortName.text = if (words.size > 1) {
                words.joinToString("") { it.first().toString() }
                    .take(3)
                    .uppercase()
            } else {
                name.take(3).uppercase()
            }
        } ?: run {
            val name = restaurant?.name ?: ""
            binding.tvRestaurantShortName.text = name.take(3).uppercase()
        }

        binding.tvRestaurantName.text = restaurant?.name
        binding.tvRestaurantEmail.text = email?.ifEmpty { "<not available>" }
    }

    fun expandRestaurantSelector(restaurantSelectorExpanded: Boolean) {
        val contentSize = (binding.rvRestaurants.adapter?.itemCount?.times(40.px)?.plus(46.px))
            ?: HomeActivity.RESTAURANT_SELECTOR_COLLAPSED_HEIGHT
        val height = min(contentSize, HomeActivity.RESTAURANT_SELECTOR_MAX_HEIGHT)

        binding.containerRestaurants.slideView(
            binding.containerRestaurants.layoutParams.height,
            if (restaurantSelectorExpanded) height else HomeActivity.RESTAURANT_SELECTOR_COLLAPSED_HEIGHT,
            animateHeight = true
        )

        binding.ivChevron.rotation = if (restaurantSelectorExpanded) 0f else 180f
        binding.separator.visibleOrGone = restaurantSelectorExpanded
        binding.etSearchRestaurant.text?.clear()
    }

    fun showLogout(show: Boolean) {
        binding.containerLogout.visibleOrGone = show
    }

    fun showIconArrow(show: Boolean) {
        binding.containerIconArrow.visibleOrGone = show
    }

    fun setOnLogoutClickListener(listener: (() -> Unit)) {
        binding.containerLogout.setOnClickListener {
            listener.invoke()
        }
    }

    fun setOnContainerSelectorClickListener(listener: ((View) -> Unit)) {
        binding.containerRestaurantSelector.setOnClickListener {
            listener.invoke(it)
        }
    }
}