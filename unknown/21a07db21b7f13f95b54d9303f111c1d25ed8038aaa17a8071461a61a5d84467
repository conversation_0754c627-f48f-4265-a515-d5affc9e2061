package com.eatapp.clementine.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.eatapp.clementine.data.network.response.restaurant.Restaurant
import com.eatapp.clementine.databinding.ListItemRestaurantBinding


class RestaurantsAdapter(val list:  List<Restaurant>?, val listener: (Restaurant) -> Unit)
    : ListAdapter<Restaurant, RestaurantsAdapter.ItemViewHolder>(RestaurantsItemDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ItemViewHolder {

        return ItemViewHolder(ListItemRestaurantBinding.inflate(
            LayoutInflater.from(parent.context), parent, false))

    }

    override fun onBindViewHolder(holder: ItemViewHolder, position: Int) {

        val item = getItem(position)
        holder.bind(createOnClickListener(item, position), item)
    }

    private fun createOnClickListener(restaurant: Restaurant, position: Int): View.OnClickListener {
        return View.OnClickListener {

            for (i in 0 until itemCount) {
                if (getItem(i).isSelected) {
                    getItem(i).isSelected = false
                    notifyItemChanged(i)
                }
            }

            for (i in 0 until list?.size!!) {
                list[i].isSelected = false
            }

            restaurant.isSelected = !restaurant.isSelected
            notifyItemChanged(position)

            listener(restaurant)
        }
    }

    class ItemViewHolder(
        private val binding: ListItemRestaurantBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(l: View.OnClickListener, i: Restaurant) {
            binding.apply {
                clickListener = l
                item = i
                executePendingBindings()
            }
        }

    }
}

private class RestaurantsItemDiffCallback : DiffUtil.ItemCallback<Restaurant>() {

    override fun areItemsTheSame(oldItem: Restaurant, newItem: Restaurant): Boolean {
        return oldItem.id == newItem.id
    }

    override fun areContentsTheSame(oldItem: Restaurant, newItem: Restaurant): Boolean {
        return oldItem == newItem
    }
}