package com.eatapp.clementine.views

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.setPadding
import androidx.recyclerview.widget.RecyclerView
import com.eatapp.clementine.databinding.InputSelectStepperViewBinding
import com.eatapp.clementine.internal.px
import com.eatapp.clementine.internal.visibleOrGone

class InputSelectStepperView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    val binding: InputSelectStepperViewBinding =
        InputSelectStepperViewBinding.inflate(LayoutInflater.from(context), this, true)

    fun setConflictsAdapter(adapter: RecyclerView.Adapter<*>) {
        binding.recyclerConflicts.adapter = adapter
    }

    fun showConflictsList(show: Boolean) {
        if (show) {
            binding.recyclerConflicts.setPadding(0, 0, 0, 8.px)
        } else {
            binding.recyclerConflicts.setPadding(0)
        }
        binding.recyclerConflicts.visibleOrGone = show
    }
}